<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TeamBy Desktop - Loading</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg,
                #0F111A 0%,
                #1A1D2B 50%,
                #12141F 100%
            );
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            height: 100vh;
            width: 100vw;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
            border-radius: 50%;
            margin: 0;
            padding: 0;
        }

        /* Enhanced Background Animations */
        body::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(27, 132, 255, 0.15) 0%, rgba(0, 102, 204, 0.1) 40%, transparent 70%);
            animation: rotate 15s linear infinite;
            z-index: 0;
        }

        body::after {
            content: '';
            position: absolute;
            top: -30%;
            right: -30%;
            width: 150%;
            height: 150%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 60%);
            animation: rotateReverse 25s linear infinite;
            z-index: 0;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.1); }
            100% { transform: rotate(360deg) scale(1); }
        }

        @keyframes rotateReverse {
            0% { transform: rotate(360deg) scale(1); }
            50% { transform: rotate(180deg) scale(0.9); }
            100% { transform: rotate(0deg) scale(1); }
        }

        .splash-container {
            text-align: center;
            z-index: 1;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            width: 100%;
            padding: 2rem;
        }

        .logo-container {
            margin-bottom: 1rem;
            position: relative;
            margin-top: 1rem;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #1B84FF 0%, #0066CC 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            box-shadow:
                0 20px 40px rgba(27, 132, 255, 0.4),
                0 8px 16px rgba(0, 0, 0, 0.5),
                inset 0 2px 0 rgba(255, 255, 255, 0.2);
            animation: logoFloat 3s ease-in-out infinite;
            position: relative;
            overflow: hidden;
        }

        /* Logo floating animation with glow */
        @keyframes logoFloat {
            0%, 100% {
                transform: translateY(0px) scale(1);
                box-shadow:
                    0 20px 40px rgba(27, 132, 255, 0.4),
                    0 8px 16px rgba(0, 0, 0, 0.5),
                    inset 0 2px 0 rgba(255, 255, 255, 0.2);
            }
            50% {
                transform: translateY(-8px) scale(1.05);
                box-shadow:
                    0 30px 60px rgba(27, 132, 255, 0.6),
                    0 15px 30px rgba(0, 0, 0, 0.6),
                    inset 0 3px 0 rgba(255, 255, 255, 0.3);
            }
        }

        /* Logo shimmer effect */
        .logo::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: logoShimmer 2s ease-in-out infinite;
            border-radius: 20px;
        }

        @keyframes logoShimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        }

        .logo-text {
            font-size: 2rem;
            font-weight: bold;
            color: white;
            text-shadow:
                0 2px 4px rgba(0, 0, 0, 0.5),
                0 0 10px rgba(255, 255, 255, 0.3);
            z-index: 2;
            position: relative;
        }

        .app-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.3rem;
            background: linear-gradient(135deg, #FFFFFF 0%, #E2E8F0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .app-subtitle {
            font-size: 0.9rem;
            color: #94A3B8;
            margin-bottom: 1.5rem;
            font-weight: 400;
        }

        .loading-container {
            margin-bottom: 1rem;
        }

        .loading-bar {
            width: 150px;
            height: 3px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            overflow: hidden;
            margin: 0 auto 0.8rem;
            position: relative;
        }

        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #1B84FF 0%, #0066CC 50%, #1B84FF 100%);
            border-radius: 2px;
            width: 0%;
            transition: width 0.3s ease-out;
            box-shadow:
                0 0 15px rgba(27, 132, 255, 0.6),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            animation: progressGlow 2s ease-in-out infinite alternate;
            position: relative;
            overflow: hidden;
        }

        .loading-progress::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: progressShine 1.5s ease-in-out infinite;
        }

        @keyframes progressGlow {
            0% { box-shadow: 0 0 15px rgba(27, 132, 255, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.3); }
            100% { box-shadow: 0 0 25px rgba(27, 132, 255, 0.8), inset 0 1px 0 rgba(255, 255, 255, 0.5); }
        }

        @keyframes progressShine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .loading-text {
            font-size: 0.9rem;
            color: #64748B;
            animation: pulse 1.5s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        .dots {
            display: inline-block;
            animation: dots 1.5s steps(4, end) infinite;
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }

        .version {
            position: absolute;
            bottom: 1.5rem;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.7rem;
            color: #475569;
            opacity: 0.7;
        }

        /* Enhanced Particle Animations */
        .particle {
            position: absolute;
            border-radius: 50%;
            pointer-events: none;
        }

        .particle-small {
            width: 2px;
            height: 2px;
            background: rgba(27, 132, 255, 0.8);
            animation: floatSmall 8s ease-in-out infinite;
        }

        .particle-medium {
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            animation: floatMedium 10s ease-in-out infinite;
        }

        .particle-large {
            width: 6px;
            height: 6px;
            background: rgba(27, 132, 255, 0.4);
            animation: floatLarge 12s ease-in-out infinite;
        }

        @keyframes floatSmall {
            0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100vh) translateX(20px) rotate(360deg); opacity: 0; }
        }

        @keyframes floatMedium {
            0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg) scale(1); opacity: 0; }
            15% { opacity: 0.8; }
            85% { opacity: 0.8; }
            50% { transform: translateY(-50vh) translateX(-15px) rotate(180deg) scale(1.2); }
            100% { transform: translateY(-100vh) translateX(-30px) rotate(360deg) scale(0.8); opacity: 0; }
        }

        @keyframes floatLarge {
            0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg) scale(1); opacity: 0; }
            20% { opacity: 0.6; }
            80% { opacity: 0.6; }
            33% { transform: translateY(-33vh) translateX(25px) rotate(120deg) scale(1.3); }
            66% { transform: translateY(-66vh) translateX(-20px) rotate(240deg) scale(0.9); }
            100% { transform: translateY(-100vh) translateX(40px) rotate(360deg) scale(0.7); opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="splash-container">
        <div class="logo-container">
            <div class="logo">
                <span class="logo-text">TB</span>
            </div>
        </div>
        
        <h1 class="app-title">TeamBy</h1>
        <p class="app-subtitle">Professional Team Collaboration</p>
        
        <div class="loading-container">
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <p class="loading-text">Loading application<span class="dots"></span></p>
        </div>
    </div>
    
    <div class="version">v1.0.0</div>

    <script>
        // Create enhanced floating particles
        function createParticle() {
            const particle = document.createElement('div');
            const types = ['particle-small', 'particle-medium', 'particle-large'];
            const randomType = types[Math.floor(Math.random() * types.length)];

            particle.className = `particle ${randomType}`;
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 4 + 's';

            // Different durations for different particle types
            let duration;
            switch(randomType) {
                case 'particle-small': duration = Math.random() * 2 + 6; break;
                case 'particle-medium': duration = Math.random() * 3 + 8; break;
                case 'particle-large': duration = Math.random() * 4 + 10; break;
            }

            particle.style.animationDuration = duration + 's';
            document.body.appendChild(particle);

            setTimeout(() => {
                particle.remove();
            }, duration * 1000);
        }

        // Create particles with varying frequency
        function startParticleSystem() {
            // Small particles - frequent
            setInterval(() => createParticle(), 200);

            // Medium particles - moderate
            setInterval(() => {
                if (Math.random() > 0.3) createParticle();
            }, 400);

            // Large particles - rare
            setInterval(() => {
                if (Math.random() > 0.7) createParticle();
            }, 800);
        }

        startParticleSystem();

        // Update loading text
        const loadingMessages = [
            'Initializing application',
            'Loading components',
            'Setting up workspace',
            'Almost ready'
        ];
        
        let messageIndex = 0;
        const loadingText = document.querySelector('.loading-text');
        
        setInterval(() => {
            messageIndex = (messageIndex + 1) % loadingMessages.length;
            loadingText.innerHTML = loadingMessages[messageIndex] + '<span class="dots"></span>';
        }, 1500);

        // Simulate loading progress - single pass from 0 to 100%
        let progress = 0;
        const progressBar = document.querySelector('.loading-progress');
        const totalDuration = 8000; // 8 seconds to match the 9 second delay
        const updateInterval = 50; // Update every 50ms for smooth animation
        const incrementPerUpdate = (100 / (totalDuration / updateInterval));

        const updateProgress = () => {
            if (progress < 100) {
                progress += incrementPerUpdate;
                if (progress > 100) progress = 100;
                progressBar.style.width = progress + '%';
                setTimeout(updateProgress, updateInterval);
            }
        };

        // Start progress after a short delay
        setTimeout(updateProgress, 500);
    </script>
</body>
</html>
