// Simple test script to verify secondary window functionality
// This can be run in the browser console when the main app is loaded

console.log('🧪 Testing secondary window functionality...');

// Test if the invoke function is available
if (typeof window.__TAURI__ !== 'undefined' && window.__TAURI__.core) {
  console.log('✅ Tauri core is available');
  
  // Test the open_secondary_window command
  window.__TAURI__.core.invoke('open_secondary_window')
    .then(() => {
      console.log('✅ Secondary window opened successfully!');
    })
    .catch((error) => {
      console.error('❌ Failed to open secondary window:', error);
    });
} else {
  console.log('❌ Tauri core not available - make sure you\'re running in Tauri dev mode');
}
