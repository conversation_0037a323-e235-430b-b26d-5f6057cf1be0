<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TeamBy - Call</title>
    <link rel="stylesheet" href="/src/index.css" />
    <style>
      body {
        margin: 0;
        padding: 0;
        background: transparent;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        overflow: hidden;
        width: 100%;
        height: 100%;
      }
      
      #root {
        width: 100%;
        height: 100%;
        background: transparent;
      }
      
      /* Loading state */
      .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100vh;
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
        color: white;
        font-size: 16px;
        border-radius: 12px;
      }

      /* Call window specific styles */
      .call-window {
        background: rgba(26, 29, 43, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(42, 45, 60, 0.8);
        border-radius: 12px;
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      /* Error handling styles */
      .error-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        background: linear-gradient(135deg, rgba(26, 29, 43, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%);
        color: white;
        text-align: center;
        border-radius: 12px;
        padding: 20px;
      }
    </style>
    <script>
      // Enhanced error handling
      window.addEventListener('error', function(e) {
        console.error('📞 [CALL_WINDOW] Error:', e.error);
        console.error('📞 [CALL_WINDOW] Filename:', e.filename);
        console.error('📞 [CALL_WINDOW] Line:', e.lineno);
      });

      window.addEventListener('unhandledrejection', function(e) {
        console.error('📞 [CALL_WINDOW] Unhandled promise rejection:', e.reason);
      });

      // Ensure DOM is ready
      document.addEventListener('DOMContentLoaded', function() {
        console.log('📞 [CALL_WINDOW] DOM Content Loaded');
      });
    </script>
  </head>
  <body>
    <div id="root">
      <div class="loading">Loading Call...</div>
    </div>
    <script type="module" src="/src/call-tauri.tsx"></script>
  </body>
</html>
