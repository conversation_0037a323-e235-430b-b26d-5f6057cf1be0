/**
 * Enhanced test utilities with comprehensive provider support
 * Provides consistent testing environment for all components
 */

import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { ReactElement, ReactNode } from 'react';
import { <PERSON><PERSON>erRouter, MemoryRouter } from 'react-router-dom';
import { HeroUIProvider } from '@heroui/react';
import { AppProvider } from '../context/AppContext';
import { TimeTrackingProvider } from '../context/TimeTrackingContext';
import { PulseBoardProvider } from '../context/PulseBoardContext';
import { ErrorProvider } from '../shared/components/error-boundary';
import { User } from '../shared/types';

/**
 * Test configuration options
 */
interface TestConfig {
  initialEntries?: string[];
  mockUser?: User;
  skipProviders?: string[];
  additionalProviders?: React.ComponentType<{ children: ReactNode }>[];
}

/**
 * Mock user for testing
 */
export const mockUser: User = {
  id: 'test-user-1',
  name: 'Test User',
  email: '<EMAIL>',
  avatar: '/test-avatar.jpg',
  role: 'Developer',
  department: 'Engineering',
  status: 'active',
  isOnline: true,
  hourlyRate: 50
};

/**
 * All providers wrapper for testing
 */
const AllTheProviders = ({
  children,
  config = {}
}: {
  children: ReactNode;
  config?: TestConfig;
}) => {
  const {
    initialEntries = ['/'],
    skipProviders = [],
    additionalProviders = []
  } = config;

  let content = children;

  // Wrap with additional providers (innermost first)
  additionalProviders.reverse().forEach(Provider => {
    content = <Provider>{content}</Provider>;
  });

  // Core providers
  if (!skipProviders.includes('PulseBoardProvider')) {
    content = <PulseBoardProvider>{content}</PulseBoardProvider>;
  }

  if (!skipProviders.includes('TimeTrackingProvider')) {
    content = <TimeTrackingProvider>{content}</TimeTrackingProvider>;
  }

  if (!skipProviders.includes('AppProvider')) {
    content = <AppProvider>{content}</AppProvider>;
  }

  if (!skipProviders.includes('ErrorProvider')) {
    content = <ErrorProvider>{content}</ErrorProvider>;
  }

  // Router (use MemoryRouter for testing)
  if (!skipProviders.includes('Router')) {
    content = (
      <MemoryRouter initialEntries={initialEntries}>
        {content}
      </MemoryRouter>
    );
  }

  // UI Provider (outermost)
  if (!skipProviders.includes('HeroUIProvider')) {
    content = <HeroUIProvider>{content}</HeroUIProvider>;
  }

  return <>{content}</>;
};

/**
 * Enhanced render function with configuration
 */
export const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'> & { config?: TestConfig }
): RenderResult => {
  const { config, ...renderOptions } = options || {};

  return render(ui, {
    wrapper: ({ children }) => (
      <AllTheProviders config={config}>
        {children}
      </AllTheProviders>
    ),
    ...renderOptions
  });
};

/**
 * Render with specific providers only
 */
export const renderWithProviders = (
  ui: ReactElement,
  providers: string[],
  options?: Omit<RenderOptions, 'wrapper'>
): RenderResult => {
  const allProviders = [
    'HeroUIProvider',
    'Router',
    'ErrorProvider',
    'AppProvider',
    'TimeTrackingProvider',
    'PulseBoardProvider'
  ];

  const skipProviders = allProviders.filter(p => !providers.includes(p));

  return customRender(ui, {
    ...options,
    config: { skipProviders }
  });
};

/**
 * Render without any providers (for unit testing)
 */
export const renderWithoutProviders = (
  ui: ReactElement,
  options?: RenderOptions
): RenderResult => {
  return render(ui, options);
};

// Re-export everything from testing library
export * from '@testing-library/react';
export { customRender as render };

// Re-export test utilities
export * from './mocks';
export * from './helpers';
