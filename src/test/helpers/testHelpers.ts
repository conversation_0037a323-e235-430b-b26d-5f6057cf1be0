/**
 * Test helper functions
 * Provides utility functions for common testing scenarios
 */

import { screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';

/**
 * Wait for element to appear
 */
export const waitForElement = async (selector: string, timeout: number = 5000) => {
  return await waitFor(
    () => {
      const element = screen.getByTestId(selector) || screen.getByText(selector);
      expect(element).toBeInTheDocument();
      return element;
    },
    { timeout }
  );
};

/**
 * Wait for element to disappear
 */
export const waitForElementToDisappear = async (selector: string, timeout: number = 5000) => {
  return await waitFor(
    () => {
      expect(screen.queryByTestId(selector) || screen.queryByText(selector)).not.toBeInTheDocument();
    },
    { timeout }
  );
};

/**
 * Fill form field
 */
export const fillFormField = async (labelText: string, value: string) => {
  const user = userEvent.setup();
  const field = screen.getByLabelText(labelText);
  await user.clear(field);
  await user.type(field, value);
};

/**
 * Submit form
 */
export const submitForm = async (formTestId?: string) => {
  const user = userEvent.setup();
  const form = formTestId 
    ? screen.getByTestId(formTestId)
    : screen.getByRole('form') || document.querySelector('form');
  
  if (!form) {
    throw new Error('Form not found');
  }

  const submitButton = form.querySelector('button[type="submit"]') || 
                      form.querySelector('input[type="submit"]');
  
  if (submitButton) {
    await user.click(submitButton);
  } else {
    fireEvent.submit(form);
  }
};

/**
 * Click button by text
 */
export const clickButton = async (buttonText: string) => {
  const user = userEvent.setup();
  const button = screen.getByRole('button', { name: buttonText });
  await user.click(button);
};

/**
 * Select option from dropdown
 */
export const selectOption = async (selectLabel: string, optionText: string) => {
  const user = userEvent.setup();
  const select = screen.getByLabelText(selectLabel);
  await user.selectOptions(select, optionText);
};

/**
 * Upload file
 */
export const uploadFile = async (inputLabel: string, file: File) => {
  const user = userEvent.setup();
  const input = screen.getByLabelText(inputLabel) as HTMLInputElement;
  await user.upload(input, file);
};

/**
 * Create mock file
 */
export const createMockFile = (name: string, type: string, content: string = 'mock content'): File => {
  return new File([content], name, { type });
};

/**
 * Mock console methods
 */
export const mockConsole = () => {
  const originalConsole = { ...console };
  
  beforeEach(() => {
    console.log = vi.fn();
    console.warn = vi.fn();
    console.error = vi.fn();
    console.info = vi.fn();
  });

  afterEach(() => {
    Object.assign(console, originalConsole);
  });

  return {
    expectConsoleLog: (message: string) => {
      expect(console.log).toHaveBeenCalledWith(expect.stringContaining(message));
    },
    expectConsoleWarn: (message: string) => {
      expect(console.warn).toHaveBeenCalledWith(expect.stringContaining(message));
    },
    expectConsoleError: (message: string) => {
      expect(console.error).toHaveBeenCalledWith(expect.stringContaining(message));
    }
  };
};

/**
 * Mock window methods
 */
export const mockWindow = () => {
  const originalWindow = { ...window };
  
  beforeEach(() => {
    Object.defineProperty(window, 'location', {
      value: {
        href: 'http://localhost:3000',
        origin: 'http://localhost:3000',
        pathname: '/',
        search: '',
        hash: '',
        reload: vi.fn(),
        assign: vi.fn(),
        replace: vi.fn()
      },
      writable: true
    });

    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
        key: vi.fn(),
        length: 0
      },
      writable: true
    });

    Object.defineProperty(window, 'sessionStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
        key: vi.fn(),
        length: 0
      },
      writable: true
    });
  });

  afterEach(() => {
    Object.assign(window, originalWindow);
  });
};

/**
 * Mock timers
 */
export const mockTimers = () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  return {
    advanceTime: (ms: number) => vi.advanceTimersByTime(ms),
    runAllTimers: () => vi.runAllTimers(),
    runOnlyPendingTimers: () => vi.runOnlyPendingTimers()
  };
};

/**
 * Mock fetch
 */
export const mockFetch = () => {
  const mockFetchFn = vi.fn();
  
  beforeEach(() => {
    global.fetch = mockFetchFn;
  });

  afterEach(() => {
    mockFetchFn.mockReset();
  });

  return {
    mockResponse: (data: any, status: number = 200) => {
      mockFetchFn.mockResolvedValueOnce({
        ok: status >= 200 && status < 300,
        status,
        json: async () => data,
        text: async () => JSON.stringify(data)
      });
    },
    mockError: (error: Error) => {
      mockFetchFn.mockRejectedValueOnce(error);
    },
    expectFetchCalled: (url: string, options?: any) => {
      expect(mockFetchFn).toHaveBeenCalledWith(url, options);
    }
  };
};

/**
 * Mock intersection observer
 */
export const mockIntersectionObserver = () => {
  const mockObserver = {
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  };

  beforeEach(() => {
    global.IntersectionObserver = vi.fn().mockImplementation(() => mockObserver);
  });

  return mockObserver;
};

/**
 * Mock resize observer
 */
export const mockResizeObserver = () => {
  const mockObserver = {
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  };

  beforeEach(() => {
    global.ResizeObserver = vi.fn().mockImplementation(() => mockObserver);
  });

  return mockObserver;
};

/**
 * Mock media query
 */
export const mockMediaQuery = (query: string, matches: boolean = false) => {
  const mockMediaQuery = {
    matches,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn()
  };

  beforeEach(() => {
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(() => mockMediaQuery)
    });
  });

  return mockMediaQuery;
};

/**
 * Test data generators
 */
export const generateTestData = {
  user: (overrides: any = {}) => ({
    id: `user-${Date.now()}`,
    name: 'Test User',
    email: '<EMAIL>',
    avatar: '/test-avatar.jpg',
    role: 'Developer',
    department: 'Engineering',
    status: 'active',
    isOnline: true,
    ...overrides
  }),

  project: (overrides: any = {}) => ({
    id: `project-${Date.now()}`,
    name: 'Test Project',
    color: '#3B82F6',
    description: 'Test project description',
    status: 'active',
    progress: 50,
    teamMembers: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...overrides
  }),

  task: (overrides: any = {}) => ({
    id: `task-${Date.now()}`,
    title: 'Test Task',
    description: 'Test task description',
    status: 'todo',
    priority: 'Medium',
    assignees: [],
    tag: 'Development',
    attachments: [],
    comments: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...overrides
  })
};

/**
 * Async test helpers
 */
export const asyncHelpers = {
  flushPromises: () => new Promise(resolve => setTimeout(resolve, 0)),
  
  waitForNextTick: () => new Promise(resolve => process.nextTick(resolve)),
  
  waitForCondition: async (condition: () => boolean, timeout: number = 5000) => {
    const startTime = Date.now();
    while (!condition() && Date.now() - startTime < timeout) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }
    if (!condition()) {
      throw new Error('Condition not met within timeout');
    }
  }
};
