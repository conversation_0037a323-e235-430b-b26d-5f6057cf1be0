import React, { useEffect } from 'react';
import { useUser } from '../context/UserContext';

/**
 * Test component to verify profile retry behavior
 * This component helps test that:
 * 1. Cache is loaded only once
 * 2. API retries use direct API calls, not cache loading
 * 3. No infinite loops occur
 */
export const ProfileRetryTest: React.FC = () => {
  const { 
    user, 
    isLoading, 
    error, 
    isFromCache, 
    isOfflineMode,
    loadCachedProfile,
    fetchUserProfileFromAPI,
    fetchUserProfile 
  } = useUser();

  useEffect(() => {
    console.log('🧪 [TEST] ProfileRetryTest mounted');
    
    // Load cache once on mount
    loadCachedProfile().then(() => {
      console.log('🧪 [TEST] Cache loaded, now testing API call...');
      
      // Test direct API call
      fetchUserProfileFromAPI().catch((err) => {
        console.log('🧪 [TEST] API call failed as expected:', err);
        console.log('🧪 [TEST] Retry mechanism should now kick in...');
      });
    });
  }, []); // Empty dependency array to run only once

  return (
    <div className="p-4 bg-gray-100 rounded-lg">
      <h2 className="text-lg font-bold mb-4">Profile Retry Test</h2>
      
      <div className="space-y-2">
        <div>
          <strong>User:</strong> {user ? user.email : 'No user'}
        </div>
        <div>
          <strong>Loading:</strong> {isLoading.toString()}
        </div>
        <div>
          <strong>Error:</strong> {error || 'No error'}
        </div>
        <div>
          <strong>From Cache:</strong> {isFromCache.toString()}
        </div>
        <div>
          <strong>Offline Mode:</strong> {isOfflineMode.toString()}
        </div>
      </div>

      <div className="mt-4 space-x-2">
        <button 
          onClick={() => loadCachedProfile()}
          className="px-3 py-1 bg-blue-500 text-white rounded"
        >
          Load Cache
        </button>
        <button 
          onClick={() => fetchUserProfileFromAPI().catch(console.error)}
          className="px-3 py-1 bg-green-500 text-white rounded"
        >
          Direct API Call
        </button>
        <button 
          onClick={() => fetchUserProfile()}
          className="px-3 py-1 bg-purple-500 text-white rounded"
        >
          Full Fetch
        </button>
      </div>

      <div className="mt-4 text-sm text-gray-600">
        <p>Expected behavior:</p>
        <ul className="list-disc list-inside">
          <li>Cache should load only once on mount</li>
          <li>API retries should not reload cache</li>
          <li>Direct API calls should update user data</li>
          <li>No infinite loops should occur</li>
        </ul>
      </div>
    </div>
  );
};
