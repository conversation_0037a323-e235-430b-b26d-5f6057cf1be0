import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor, act } from '../test/test-utils';
import { UserProvider, useUser } from '../context/UserContext';
import { ToastProvider } from '../context/ToastContext';
import React, { useEffect } from 'react';

// Mock <PERSON> invoke
const mockInvoke = vi.fn();
vi.mock('@tauri-apps/api/core', () => ({
  invoke: mockInvoke,
}));

// Mock toast helpers
const mockShowError = vi.fn();
vi.mock('../context/ToastContext', () => ({
  ToastProvider: ({ children }: { children: React.ReactNode }) => children,
  useToastHelpers: () => ({
    showError: mockShowError,
    showSuccess: vi.fn(),
    showWarning: vi.fn(),
    showInfo: vi.fn(),
  }),
}));

// Test component that simulates the AuthWrapper behavior
const TestAuthWrapper = () => {
  const { fetchUserProfile, loadCachedProfile } = useUser();
  const [hasInitialized, setHasInitialized] = React.useState(false);

  useEffect(() => {
    if (!hasInitialized) {
      console.log('🧪 [TEST] Initializing...');
      setHasInitialized(true);
      
      // Simulate AuthWrapper behavior
      loadCachedProfile().then(() => {
        console.log('🧪 [TEST] Cache loaded, now fetching profile...');
        fetchUserProfile();
      });
    }
  }, [hasInitialized, fetchUserProfile, loadCachedProfile]);

  return <div data-testid="test-wrapper">Test Wrapper</div>;
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ToastProvider>
      <UserProvider>
        {component}
      </UserProvider>
    </ToastProvider>
  );
};

describe('TDD: Duplicate API Call Investigation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    console.log = vi.fn(); // Mock console.log to track calls
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  it('should call get_user_profile only once on initial load', async () => {
    // Setup: Mock cached profile exists
    mockInvoke
      .mockResolvedValueOnce({ // get_cached_user_profile
        full_name: 'Test User',
        email: '<EMAIL>',
        avatar: null,
        position: 'Developer',
        company: 'Test Company',
        is_admin: false,
        screen_active: false,
      })
      .mockRejectedValueOnce('NETWORK_CONNECTION_FAILED'); // get_user_profile fails

    // Act: Render component
    renderWithProviders(<TestAuthWrapper />);

    // Wait for cache load
    await waitFor(() => {
      expect(mockInvoke).toHaveBeenCalledWith('get_cached_user_profile');
    });

    // Wait for profile fetch
    await waitFor(() => {
      expect(mockInvoke).toHaveBeenCalledWith('get_user_profile');
    });

    // Assert: get_user_profile should be called exactly once
    const getUserProfileCalls = mockInvoke.mock.calls.filter(
      call => call[0] === 'get_user_profile'
    );
    
    console.log('🧪 [TEST] Total invoke calls:', mockInvoke.mock.calls);
    console.log('🧪 [TEST] get_user_profile calls:', getUserProfileCalls);
    
    expect(getUserProfileCalls).toHaveLength(1);
  });

  it('should identify the source of duplicate calls', async () => {
    // Setup: Track all function calls
    const callTracker = {
      fetchUserProfile: 0,
      fetchUserProfileFromAPI: 0,
      getUserProfile: 0
    };

    // Mock with call tracking
    mockInvoke
      .mockResolvedValueOnce({ // get_cached_user_profile
        full_name: 'Test User',
        email: '<EMAIL>',
        avatar: null,
        position: 'Developer',
        company: 'Test Company',
        is_admin: false,
        screen_active: false,
      })
      .mockImplementation((command) => {
        if (command === 'get_user_profile') {
          callTracker.getUserProfile++;
          console.log(`🧪 [TEST] get_user_profile called (${callTracker.getUserProfile})`);
          return Promise.reject('NETWORK_CONNECTION_FAILED');
        }
        return Promise.resolve();
      });

    // Act: Render and wait
    renderWithProviders(<TestAuthWrapper />);

    await waitFor(() => {
      expect(mockInvoke).toHaveBeenCalledWith('get_cached_user_profile');
    });

    await waitFor(() => {
      expect(callTracker.getUserProfile).toBeGreaterThan(0);
    });

    // Wait a bit more to see if duplicate calls happen
    await act(async () => {
      vi.advanceTimersByTime(100);
    });

    // Assert and debug
    console.log('🧪 [TEST] Final call tracker:', callTracker);
    console.log('🧪 [TEST] All invoke calls:', mockInvoke.mock.calls);
    
    // This test will help us see exactly how many times and why
    expect(callTracker.getUserProfile).toBeLessThanOrEqual(1);
  });

  it('should test fetchUserProfile protection mechanisms', async () => {
    const TestComponent = () => {
      const { fetchUserProfile } = useUser();
      
      return (
        <div>
          <button 
            onClick={() => fetchUserProfile()} 
            data-testid="fetch-button"
          >
            Fetch
          </button>
          <button 
            onClick={() => {
              fetchUserProfile();
              fetchUserProfile(); // Try to call twice immediately
            }} 
            data-testid="double-fetch-button"
          >
            Double Fetch
          </button>
        </div>
      );
    };

    mockInvoke.mockRejectedValue('NETWORK_CONNECTION_FAILED');

    renderWithProviders(<TestComponent />);

    const doubleFetchButton = screen.getByTestId('double-fetch-button');
    
    await act(async () => {
      doubleFetchButton.click();
    });

    // Wait for any async operations
    await waitFor(() => {
      expect(mockInvoke).toHaveBeenCalled();
    });

    const getUserProfileCalls = mockInvoke.mock.calls.filter(
      call => call[0] === 'get_user_profile'
    );

    console.log('🧪 [TEST] Double fetch test - get_user_profile calls:', getUserProfileCalls);
    
    // Should only be called once due to protection
    expect(getUserProfileCalls).toHaveLength(1);
  });
});
