/**
 * Mock hooks for testing
 * Provides mock implementations of custom hooks
 */

import { vi } from 'vitest';
import { mockUser } from './mockData';

/**
 * Mock navigation hook
 */
export const mockUseNavigation = () => ({
  activeApp: 'time',
  setActiveApp: vi.fn(),
  timeSection: 'tracking',
  setTimeSection: vi.fn(),
  projectSection: 'kanban',
  setProjectSection: vi.fn(),
  pulseSection: 'overview',
  setPulseSection: vi.fn(),
  navigateToApp: vi.fn(),
  navigateToSection: vi.fn(),
  getAppPath: vi.fn((app: string) => `/${app}`),
  getCurrentPath: vi.fn(() => '/time-tracking')
});

/**
 * Mock error hook
 */
export const mockUseError = () => ({
  state: {
    errors: [],
    globalError: null,
    isErrorModalOpen: false
  },
  addError: vi.fn(),
  removeError: vi.fn(),
  clearErrors: vi.fn(),
  setGlobalError: vi.fn(),
  openErrorModal: vi.fn(),
  closeErrorModal: vi.fn(),
  reportError: vi.fn()
});

/**
 * Mock async error hook
 */
export const mockUseAsyncError = () => vi.fn();

/**
 * Mock safe async hook
 */
export const mockUseSafeAsync = () => ({
  execute: vi.fn(),
  loading: false,
  error: null,
  reset: vi.fn()
});

/**
 * Mock time tracking hook
 */
export const mockUseTimeTracking = () => ({
  isTracking: false,
  toggleTracking: vi.fn(),
  userStatus: 'available' as const,
  setUserStatus: vi.fn(),
  showOverviewModal: false,
  setShowOverviewModal: vi.fn(),
  timeLogs: [],
  addTimeLog: vi.fn(),
  currentSession: null,
  startTracking: vi.fn(),
  stopTracking: vi.fn(),
  getTotalHours: vi.fn(() => 0),
  getHoursForProject: vi.fn(() => 0)
});

/**
 * Mock pulse board hook
 */
export const mockUsePulseBoard = () => ({
  challenges: [],
  kudos: [],
  recommendations: [],
  giveKudos: vi.fn(),
  addRecommendation: vi.fn(),
  isKudosModalOpen: false,
  setKudosModalOpen: vi.fn(),
  getKudosForUser: vi.fn(() => []),
  getTotalKudos: vi.fn(() => 0),
  getChallengeWinners: vi.fn(() => [])
});

/**
 * Mock app context hook
 */
export const mockUseApp = () => ({
  user: mockUser,
  setUser: vi.fn(),
  theme: 'dark' as const,
  setTheme: vi.fn(),
  sidebarExpanded: false,
  setSidebarExpanded: vi.fn(),
  notifications: [],
  addNotification: vi.fn(),
  removeNotification: vi.fn(),
  clearNotifications: vi.fn()
});

/**
 * Mock local storage hook
 */
export const mockUseLocalStorage = <T>(key: string, defaultValue: T) => {
  const [value, setValue] = vi.fn().mockReturnValue([defaultValue, vi.fn()]);
  return [value, setValue];
};

/**
 * Mock debounce hook
 */
export const mockUseDebounce = <T>(value: T, delay: number) => value;

/**
 * Mock throttle hook
 */
export const mockUseThrottle = <T>(value: T, delay: number) => value;

/**
 * Mock previous hook
 */
export const mockUsePrevious = <T>(value: T) => undefined;

/**
 * Mock mount hook
 */
export const mockUseMount = (callback: () => void) => {
  // Mock implementation - doesn't actually call on mount in tests
};

/**
 * Mock unmount hook
 */
export const mockUseUnmount = (callback: () => void) => {
  // Mock implementation - doesn't actually call on unmount in tests
};

/**
 * Mock interval hook
 */
export const mockUseInterval = (callback: () => void, delay: number | null) => {
  // Mock implementation - doesn't actually set up interval in tests
};

/**
 * Mock timeout hook
 */
export const mockUseTimeout = (callback: () => void, delay: number) => {
  // Mock implementation - doesn't actually set up timeout in tests
};

/**
 * Mock online status hook
 */
export const mockUseOnlineStatus = () => true;

/**
 * Mock window size hook
 */
export const mockUseWindowSize = () => ({
  width: 1200,
  height: 800
});

/**
 * Mock click outside hook
 */
export const mockUseClickOutside = (callback: () => void) => ({
  current: null
});

/**
 * Mock key press hook
 */
export const mockUseKeyPress = (targetKey: string, callback: () => void) => {
  // Mock implementation - doesn't actually listen for key presses in tests
};

/**
 * Mock copy to clipboard hook
 */
export const mockUseCopyToClipboard = () => ({
  copy: vi.fn(),
  copied: false,
  error: null
});

/**
 * Mock form hook
 */
export const mockUseForm = <T extends Record<string, any>>(initialValues: T) => ({
  values: initialValues,
  errors: {} as Record<keyof T, string>,
  touched: {} as Record<keyof T, boolean>,
  isValid: true,
  isSubmitting: false,
  handleChange: vi.fn(),
  handleBlur: vi.fn(),
  handleSubmit: vi.fn(),
  setFieldValue: vi.fn(),
  setFieldError: vi.fn(),
  setFieldTouched: vi.fn(),
  resetForm: vi.fn(),
  validateForm: vi.fn()
});

/**
 * Mock pagination hook
 */
export const mockUsePagination = (totalItems: number, itemsPerPage: number = 10) => ({
  currentPage: 1,
  totalPages: Math.ceil(totalItems / itemsPerPage),
  itemsPerPage,
  totalItems,
  startIndex: 0,
  endIndex: Math.min(itemsPerPage, totalItems),
  hasNext: totalItems > itemsPerPage,
  hasPrev: false,
  goToPage: vi.fn(),
  nextPage: vi.fn(),
  prevPage: vi.fn(),
  goToFirst: vi.fn(),
  goToLast: vi.fn()
});

/**
 * Mock search hook
 */
export const mockUseSearch = <T>(items: T[], searchFields: (keyof T)[]) => ({
  query: '',
  setQuery: vi.fn(),
  results: items,
  isSearching: false,
  clearSearch: vi.fn()
});

/**
 * Mock sort hook
 */
export const mockUseSort = <T>(items: T[]) => ({
  sortedItems: items,
  sortBy: null,
  sortOrder: 'asc' as const,
  setSortBy: vi.fn(),
  setSortOrder: vi.fn(),
  toggleSort: vi.fn()
});

/**
 * Mock filter hook
 */
export const mockUseFilter = <T>(items: T[]) => ({
  filteredItems: items,
  filters: {},
  setFilter: vi.fn(),
  removeFilter: vi.fn(),
  clearFilters: vi.fn(),
  hasActiveFilters: false
});
