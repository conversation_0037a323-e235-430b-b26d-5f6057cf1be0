/**
 * Mock data for testing
 * Provides consistent test data across all test suites
 */

import { 
  User, 
  Project, 
  Task, 
  TimeEntry, 
  ChatMessage, 
  ChatConversation,
  FileItem,
  FolderItem,
  Challenge,
  Kudos,
  Recommendation,
  OrganizationalRequest
} from '../../shared/types/domain';

/**
 * Mock users
 */
export const mockUsers: User[] = [
  {
    id: 'user-1',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatars/john.jpg',
    role: 'Senior Developer',
    department: 'Engineering',
    status: 'active',
    isOnline: true,
    hourlyRate: 75
  },
  {
    id: 'user-2',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatars/jane.jpg',
    role: 'Product Manager',
    department: 'Product',
    status: 'active',
    isOnline: false,
    lastSeen: new Date('2024-01-15T10:30:00Z'),
    hourlyRate: 80
  },
  {
    id: 'user-3',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatars/bob.jpg',
    role: 'Designer',
    department: 'Design',
    status: 'busy',
    isOnline: true,
    hourlyRate: 65
  }
];

/**
 * Mock projects
 */
export const mockProjects: Project[] = [
  {
    id: 'project-1',
    name: 'TeamBy Desktop App',
    color: '#3B82F6',
    icon: '💻',
    description: 'Desktop application for team collaboration',
    deadline: '2024-03-01T00:00:00Z',
    status: 'active',
    teamMembers: [mockUsers[0], mockUsers[1]],
    progress: 65,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T12:00:00Z',
    createdBy: mockUsers[1]
  },
  {
    id: 'project-2',
    name: 'Mobile App',
    color: '#10B981',
    icon: '📱',
    description: 'Mobile companion app',
    deadline: '2024-04-15T00:00:00Z',
    status: 'planning',
    teamMembers: [mockUsers[1], mockUsers[2]],
    progress: 25,
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-15T14:30:00Z',
    createdBy: mockUsers[1]
  }
];

/**
 * Mock tasks
 */
export const mockTasks: Task[] = [
  {
    id: 'task-1',
    title: 'Implement user authentication',
    description: 'Add login and registration functionality',
    status: 'inprogress',
    priority: 'High',
    dueDate: '2024-01-20T00:00:00Z',
    assignees: [mockUsers[0]],
    tag: 'Backend',
    project: mockProjects[0],
    attachments: [],
    comments: [],
    estimatedHours: 16,
    actualHours: 8,
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-15T09:00:00Z',
    createdBy: mockUsers[1]
  },
  {
    id: 'task-2',
    title: 'Design user interface mockups',
    description: 'Create wireframes and mockups for main screens',
    status: 'done',
    priority: 'Medium',
    dueDate: '2024-01-18T00:00:00Z',
    assignees: [mockUsers[2]],
    tag: 'Design',
    project: mockProjects[0],
    attachments: [],
    comments: [],
    estimatedHours: 12,
    actualHours: 14,
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-18T16:00:00Z',
    createdBy: mockUsers[1]
  }
];

/**
 * Mock time entries
 */
export const mockTimeEntries: TimeEntry[] = [
  {
    id: 'time-1',
    startTime: new Date('2024-01-15T09:00:00Z'),
    endTime: new Date('2024-01-15T12:00:00Z'),
    duration: '03:00:00',
    project: 'TeamBy Desktop App',
    task: 'Implement user authentication',
    notes: 'Working on login form validation',
    user: mockUsers[0],
    createdAt: '2024-01-15T09:00:00Z',
    updatedAt: '2024-01-15T12:00:00Z',
    createdBy: mockUsers[0]
  },
  {
    id: 'time-2',
    startTime: new Date('2024-01-15T13:00:00Z'),
    endTime: new Date('2024-01-15T17:00:00Z'),
    duration: '04:00:00',
    project: 'TeamBy Desktop App',
    task: 'Design user interface mockups',
    notes: 'Created wireframes for dashboard',
    user: mockUsers[2],
    createdAt: '2024-01-15T13:00:00Z',
    updatedAt: '2024-01-15T17:00:00Z',
    createdBy: mockUsers[2]
  }
];

/**
 * Mock chat messages
 */
export const mockChatMessages: ChatMessage[] = [
  {
    id: 'msg-1',
    senderId: 'user-1',
    senderName: 'John Doe',
    senderAvatar: '/avatars/john.jpg',
    content: 'Hey team, how is the project going?',
    timestamp: new Date('2024-01-15T10:00:00Z'),
    status: 'read',
    isOutgoing: false,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: 'msg-2',
    senderId: 'user-2',
    senderName: 'Jane Smith',
    senderAvatar: '/avatars/jane.jpg',
    content: 'Making good progress! Authentication is almost done.',
    timestamp: new Date('2024-01-15T10:05:00Z'),
    status: 'read',
    isOutgoing: true,
    replyTo: {
      id: 'msg-1',
      content: 'Hey team, how is the project going?',
      senderName: 'John Doe'
    },
    createdAt: '2024-01-15T10:05:00Z',
    updatedAt: '2024-01-15T10:05:00Z'
  }
];

/**
 * Mock chat conversations
 */
export const mockChatConversations: ChatConversation[] = [
  {
    id: 'conv-1',
    title: 'Project Team',
    avatar: '/avatars/team.jpg',
    type: 'group',
    lastMessage: {
      content: 'Making good progress! Authentication is almost done.',
      timestamp: new Date('2024-01-15T10:05:00Z'),
      senderId: 'user-2',
      senderName: 'Jane Smith',
      status: 'read'
    },
    unreadCount: 0,
    isOnline: true,
    isPinned: true,
    isMuted: false,
    members: mockUsers,
    description: 'Main project discussion',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:05:00Z'
  }
];

/**
 * Mock file items
 */
export const mockFileItems: FileItem[] = [
  {
    id: 'file-1',
    name: 'project-requirements.pdf',
    type: 'application/pdf',
    path: '/documents/project-requirements.pdf',
    section: 'my-files',
    size: '2.5 MB',
    owner: 'Jane Smith',
    modifiedAt: '2024-01-15T14:30:00Z',
    visibilityMode: 'all-except',
    allowedUsers: ['user-1', 'user-2'],
    tags: ['requirements', 'project'],
    version: 1,
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-15T14:30:00Z'
  }
];

/**
 * Mock folder items
 */
export const mockFolderItems: FolderItem[] = [
  {
    id: 'folder-1',
    name: 'Project Documents',
    type: 'folder',
    path: '/documents',
    section: 'shared',
    items: 5,
    owner: 'Jane Smith',
    modifiedAt: '2024-01-15T16:00:00Z',
    visibilityMode: 'all-except',
    allowedUsers: ['user-1', 'user-2', 'user-3'],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T16:00:00Z'
  }
];

/**
 * Mock challenges
 */
export const mockChallenges: Challenge[] = [
  {
    id: 'challenge-1',
    month: '2024-01',
    title: 'Code Quality Champion',
    description: 'Write the most bug-free code this month',
    icon: '🏆',
    winners: ['user-1'],
    badge: 'Quality Master',
    category: 'performance',
    points: 100,
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z'
  }
];

/**
 * Mock kudos
 */
export const mockKudos: Kudos[] = [
  {
    id: 'kudos-1',
    from: 'user-2',
    to: 'user-1',
    count: 5,
    month: '2024-01',
    date: '2024-01-15T12:00:00Z',
    message: 'Great work on the authentication system!',
    category: 'technical',
    createdAt: '2024-01-15T12:00:00Z',
    updatedAt: '2024-01-15T12:00:00Z'
  }
];

/**
 * Mock recommendations
 */
export const mockRecommendations: Recommendation[] = [
  {
    id: 'rec-1',
    from: mockUsers[1],
    to: mockUsers[0],
    title: 'Excellent Problem Solver',
    description: 'John consistently delivers high-quality solutions',
    category: 'skill',
    isPublic: true,
    endorsements: [mockUsers[2]],
    createdAt: '2024-01-15T15:00:00Z',
    updatedAt: '2024-01-15T15:00:00Z'
  }
];

/**
 * Mock organizational requests
 */
export const mockOrganizationalRequests: OrganizationalRequest[] = [
  {
    id: 'req-1',
    type: 'leave',
    title: 'Vacation Request',
    description: 'Annual vacation to Europe',
    status: 'pending',
    requestedBy: mockUsers[0],
    dateRange: {
      from: '2024-02-01T00:00:00Z',
      to: '2024-02-15T00:00:00Z'
    },
    attachments: [],
    comments: [],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  }
];
