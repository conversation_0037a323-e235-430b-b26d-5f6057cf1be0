/**
 * Mock services for testing
 * Provides mock implementations of service interfaces
 */

import { vi } from 'vitest';
import { ApiResponse, PaginationParams } from '../../shared/types';
import { Repository, BaseService } from '../../shared/services/base';
import { 
  mockUsers, 
  mockProjects, 
  mockTasks, 
  mockTimeEntries,
  mockChatMessages,
  mockChatConversations,
  mockFileItems,
  mockChallenges,
  mockKudos,
  mockRecommendations,
  mockOrganizationalRequests
} from './mockData';

/**
 * Generic mock repository
 */
export class MockTestRepository<T> implements Repository<T> {
  private data: T[];

  constructor(initialData: T[] = []) {
    this.data = [...initialData];
  }

  async findAll(params?: PaginationParams): Promise<ApiResponse<T[]>> {
    let items = [...this.data];

    // Apply pagination
    if (params?.page && params?.limit) {
      const startIndex = (params.page - 1) * params.limit;
      const endIndex = startIndex + params.limit;
      items = items.slice(startIndex, endIndex);
    }

    return {
      data: items,
      success: true,
      pagination: params?.page && params?.limit ? {
        total: this.data.length,
        page: params.page,
        limit: params.limit,
        totalPages: Math.ceil(this.data.length / params.limit)
      } : undefined
    };
  }

  async findById(id: string): Promise<ApiResponse<T | null>> {
    const item = this.data.find((item: any) => item.id === id) || null;
    return {
      data: item,
      success: true
    };
  }

  async create(data: Partial<T>): Promise<ApiResponse<T>> {
    const newItem = {
      ...data,
      id: `test-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    } as T;

    this.data.push(newItem);

    return {
      data: newItem,
      success: true,
      message: 'Item created successfully'
    };
  }

  async update(id: string, data: Partial<T>): Promise<ApiResponse<T>> {
    const index = this.data.findIndex((item: any) => item.id === id);
    
    if (index === -1) {
      return {
        data: null,
        success: false,
        message: 'Item not found',
        errors: ['Item not found']
      };
    }

    const updatedItem = {
      ...this.data[index],
      ...data,
      updatedAt: new Date().toISOString()
    } as T;

    this.data[index] = updatedItem;

    return {
      data: updatedItem,
      success: true,
      message: 'Item updated successfully'
    };
  }

  async delete(id: string): Promise<ApiResponse<boolean>> {
    const index = this.data.findIndex((item: any) => item.id === id);
    
    if (index === -1) {
      return {
        data: false,
        success: false,
        message: 'Item not found',
        errors: ['Item not found']
      };
    }

    this.data.splice(index, 1);

    return {
      data: true,
      success: true,
      message: 'Item deleted successfully'
    };
  }

  async search(query: string, params?: PaginationParams): Promise<ApiResponse<T[]>> {
    const searchTerm = query.toLowerCase();
    let items = this.data.filter((item: any) => {
      // Basic search implementation
      const searchableFields = ['name', 'title', 'description', 'content'];
      return searchableFields.some(field => {
        const value = item[field];
        return value && typeof value === 'string' && value.toLowerCase().includes(searchTerm);
      });
    });

    // Apply pagination
    if (params?.page && params?.limit) {
      const startIndex = (params.page - 1) * params.limit;
      const endIndex = startIndex + params.limit;
      items = items.slice(startIndex, endIndex);
    }

    return {
      data: items,
      success: true,
      pagination: params?.page && params?.limit ? {
        total: items.length,
        page: params.page,
        limit: params.limit,
        totalPages: Math.ceil(items.length / params.limit)
      } : undefined
    };
  }

  // Test helper methods
  setData(data: T[]): void {
    this.data = [...data];
  }

  getData(): T[] {
    return [...this.data];
  }

  clear(): void {
    this.data = [];
  }
}

/**
 * Mock service factory
 */
export const createMockService = <T>(initialData: T[] = []) => {
  const repository = new MockTestRepository<T>(initialData);
  
  class MockService extends BaseService<T> {
    constructor() {
      super(repository);
    }

    // Expose repository for testing
    getRepository() {
      return repository;
    }
  }

  return new MockService();
};

/**
 * Pre-configured mock services
 */
export const mockUserService = createMockService(mockUsers);
export const mockProjectService = createMockService(mockProjects);
export const mockTaskService = createMockService(mockTasks);
export const mockTimeEntryService = createMockService(mockTimeEntries);
export const mockChatMessageService = createMockService(mockChatMessages);
export const mockChatConversationService = createMockService(mockChatConversations);
export const mockFileService = createMockService(mockFileItems);
export const mockChallengeService = createMockService(mockChallenges);
export const mockKudosService = createMockService(mockKudos);
export const mockRecommendationService = createMockService(mockRecommendations);
export const mockRequestService = createMockService(mockOrganizationalRequests);

/**
 * Mock API client
 */
export const mockApiClient = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn(),
  request: vi.fn()
};

/**
 * Mock storage
 */
export const mockStorage = {
  get: vi.fn(),
  set: vi.fn(),
  remove: vi.fn(),
  clear: vi.fn(),
  keys: vi.fn()
};

/**
 * Mock error handler
 */
export const mockErrorHandler = {
  reportError: vi.fn(),
  addError: vi.fn(),
  removeError: vi.fn(),
  clearErrors: vi.fn()
};

/**
 * Reset all mocks
 */
export const resetAllMocks = () => {
  vi.clearAllMocks();
  
  // Reset service data
  mockUserService.getRepository().setData(mockUsers);
  mockProjectService.getRepository().setData(mockProjects);
  mockTaskService.getRepository().setData(mockTasks);
  mockTimeEntryService.getRepository().setData(mockTimeEntries);
  mockChatMessageService.getRepository().setData(mockChatMessages);
  mockChatConversationService.getRepository().setData(mockChatConversations);
  mockFileService.getRepository().setData(mockFileItems);
  mockChallengeService.getRepository().setData(mockChallenges);
  mockKudosService.getRepository().setData(mockKudos);
  mockRecommendationService.getRepository().setData(mockRecommendations);
  mockRequestService.getRepository().setData(mockOrganizationalRequests);
};
