import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import { <PERSON><PERSON>Provider } from '@heroui/react'
import { CallDesktop } from './components/CallDesktop'
import './index.css'

// Error Boundary Component
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('📞 [CALL_WINDOW] React Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-state">
          <h2>Something went wrong</h2>
          <p>Call window failed to load properly</p>
          <p style={{ fontSize: '12px', opacity: 0.7 }}>
            {this.state.error?.message}
          </p>
        </div>
      );
    }

    return this.props.children;
  }
}

// Wait for DOM to be ready
const initApp = () => {
  console.log('📞 [CALL_WINDOW] Initializing React app...');
  
  const root = document.getElementById('root');
  if (!root) {
    console.error('📞 [CALL_WINDOW] Root element not found');
    return;
  }

  console.log('📞 [CALL_WINDOW] Root element found, rendering app...');

  ReactDOM.createRoot(root).render(
    <React.StrictMode>
      <ErrorBoundary>
        <HeroUIProvider>
          <CallDesktop />
        </HeroUIProvider>
      </ErrorBoundary>
    </React.StrictMode>,
  );
  
  console.log('📞 [CALL_WINDOW] React app rendered successfully');
}

// Ensure proper initialization timing
console.log('📞 [CALL_WINDOW] Document ready state:', document.readyState);
if (document.readyState === 'loading') {
  console.log('📞 [CALL_WINDOW] Waiting for DOMContentLoaded...');
  document.addEventListener('DOMContentLoaded', initApp);
} else {
  console.log('📞 [CALL_WINDOW] DOM already ready, initializing immediately...');
  initApp();
}
