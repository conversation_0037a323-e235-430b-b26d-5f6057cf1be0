import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@heroui/react";
import { TauriIcon as Icon } from "./TauriIcon";
import { getCurrentWindow } from '@tauri-apps/api/window';

interface CallWindowTopBarProps {
  title?: string;
  onClose?: () => void;
}

export const CallWindowTopBar: React.FC<CallWindowTopBarProps> = ({
  title = "TeamBy - Call",
  onClose
}) => {
  const [appWindow, setAppWindow] = useState<any>(null);

  useEffect(() => {
    const initWindow = async () => {
      try {
        const window = getCurrentWindow();
        setAppWindow(window);
      } catch (error) {
        console.error('Failed to get current window:', error);
      }
    };

    initWindow();
  }, []);

  const handleMinimize = async () => {
    try {
      if (appWindow) {
        await appWindow.minimize();
      }
    } catch (error) {
      console.error('Failed to minimize window:', error);
    }
  };

  const handleClose = async () => {
    try {
      if (onClose) {
        onClose();
      } else if (appWindow) {
        await appWindow.close();
      }
    } catch (error) {
      console.error('Failed to close window:', error);
    }
  };

  return (
    <div 
      data-tauri-drag-region
      className="flex items-center justify-between h-8 bg-[#1A1D2B] border-b border-[#2A2D3C] px-3 select-none"
    >
      {/* Title */}
      <div className="flex items-center">
        <Icon icon="lucide:phone" className="text-[#1B84FF] text-sm mr-2" />
        <span className="text-white text-xs font-medium">{title}</span>
      </div>

      {/* Window Controls */}
      <div className="flex items-center space-x-1">
        <Button
          isIconOnly
          size="sm"
          variant="light"
          className="text-[#7D8597] hover:text-white hover:bg-[#2A2D3C]/50 w-6 h-6 min-w-6"
          onPress={handleMinimize}
        >
          <Icon icon="lucide:minus" className="text-xs" />
        </Button>
        
        <Button
          isIconOnly
          size="sm"
          variant="light"
          className="text-[#7D8597] hover:text-white hover:bg-red-500/20 w-6 h-6 min-w-6"
          onPress={handleClose}
        >
          <Icon icon="lucide:x" className="text-xs" />
        </Button>
      </div>
    </div>
  );
};
