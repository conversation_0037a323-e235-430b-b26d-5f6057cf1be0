import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { TauriIcon as Icon } from '../TauriIcon';
import { useToast, type Toast as ToastType } from '../../context/ToastContext';

const toastIcons = {
  success: 'lucide:check-circle',
  error: 'lucide:x-circle',
  warning: 'lucide:alert-triangle',
  info: 'lucide:info',
};

const toastColors = {
  success: {
    bg: 'bg-green-500/10',
    border: 'border-green-500/20',
    icon: 'text-green-400',
    text: 'text-green-100',
  },
  error: {
    bg: 'bg-red-500/10',
    border: 'border-red-500/20',
    icon: 'text-red-400',
    text: 'text-red-100',
  },
  warning: {
    bg: 'bg-yellow-500/10',
    border: 'border-yellow-500/20',
    icon: 'text-yellow-400',
    text: 'text-yellow-100',
  },
  info: {
    bg: 'bg-blue-500/10',
    border: 'border-blue-500/20',
    icon: 'text-blue-400',
    text: 'text-blue-100',
  },
};

interface ToastItemProps {
  toast: ToastType;
}

const ToastItem: React.FC<ToastItemProps> = ({ toast }) => {
  const { hideToast } = useToast();
  const colors = toastColors[toast.type];

  return (
    <motion.div
      initial={{ opacity: 0, x: 300, scale: 0.3 }}
      animate={{ opacity: 1, x: 0, scale: 1 }}
      exit={{ opacity: 0, x: 300, scale: 0.5, transition: { duration: 0.2 } }}
      className={`
        relative flex items-start gap-3 p-4 rounded-lg border backdrop-blur-sm
        ${colors.bg} ${colors.border}
        shadow-lg max-w-sm w-full
      `}
    >
      {/* Icon */}
      <div className={`flex-shrink-0 ${colors.icon}`}>
        <Icon icon={toastIcons[toast.type]} className="w-5 h-5" />
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <h4 className={`text-sm font-medium ${colors.text}`}>
          {toast.title}
        </h4>
        {toast.message && (
          <p className={`text-xs mt-1 ${colors.text} opacity-80`}>
            {toast.message}
          </p>
        )}
      </div>

      {/* Close button */}
      <button
        onClick={() => hideToast(toast.id)}
        className={`
          flex-shrink-0 p-1 rounded-md transition-colors
          ${colors.text} opacity-60 hover:opacity-100
          hover:bg-white/10
        `}
      >
        <Icon icon="lucide:x" className="w-4 h-4" />
      </button>
    </motion.div>
  );
};

export const ToastContainer: React.FC = () => {
  const { toasts } = useToast();

  return (
    <div className="fixed top-20 right-4 z-50 flex flex-col gap-2">
      <AnimatePresence mode="popLayout">
        {toasts.map((toast) => (
          <ToastItem key={toast.id} toast={toast} />
        ))}
      </AnimatePresence>
    </div>
  );
};

export default ToastContainer;
