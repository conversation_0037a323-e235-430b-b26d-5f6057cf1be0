import React from 'react';

interface SkeletonProps {
  className?: string;
  width?: string | number;
  height?: string | number;
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full';
  animate?: boolean;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  className = '',
  width,
  height,
  rounded = 'md',
  animate = true,
}) => {
  const roundedClasses = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    full: 'rounded-full',
  };

  const style: React.CSSProperties = {};
  if (width) style.width = typeof width === 'number' ? `${width}px` : width;
  if (height) style.height = typeof height === 'number' ? `${height}px` : height;

  return (
    <div
      className={`
        bg-gradient-to-r from-custom-border/20 via-custom-border/40 to-custom-border/20
        ${animate ? 'animate-shimmer bg-[length:200%_100%]' : ''}
        ${roundedClasses[rounded]}
        ${className}
      `}
      style={style}
    />
  );
};

// Avatar Skeleton Component
export const AvatarSkeleton: React.FC<{ size?: number; className?: string }> = ({
  size = 32,
  className = '',
}) => (
  <Skeleton
    width={size}
    height={size}
    rounded="full"
    className={`flex-shrink-0 ${className}`}
  />
);

// Text Skeleton Component
export const TextSkeleton: React.FC<{
  lines?: number;
  className?: string;
  widths?: string[];
}> = ({ lines = 1, className = '', widths }) => (
  <div className={`space-y-2 ${className}`}>
    {Array.from({ length: lines }).map((_, index) => (
      <Skeleton
        key={index}
        height={16}
        width={widths?.[index] || (index === lines - 1 ? '75%' : '100%')}
        rounded="sm"
      />
    ))}
  </div>
);

// Profile Skeleton Component (for TopBar)
export const ProfileSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`flex items-center space-x-3 ${className}`}>
    <div className="relative">
      {/* Badge skeleton */}
      <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3">
        <Skeleton width={12} height={12} rounded="full" />
      </div>
      {/* Avatar skeleton */}
      <AvatarSkeleton size={32} />
    </div>
    <div className="flex-1 min-w-0">
      {/* Name skeleton */}
      <Skeleton height={14} width="80px" rounded="sm" className="mb-1" />
      {/* Status/Position skeleton */}
      <div className="flex items-center">
        <Skeleton height={12} width="60px" rounded="sm" />
        {/* Chevron skeleton */}
        <Skeleton width={10} height={10} rounded="sm" className="ml-1" />
      </div>
    </div>
  </div>
);

// Card Skeleton Component
export const CardSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`bg-custom-card border border-custom-border rounded-lg p-4 space-y-3 ${className}`}>
    <div className="flex items-center space-x-3">
      <AvatarSkeleton size={40} />
      <div className="flex-1">
        <Skeleton height={16} width="70%" rounded="sm" className="mb-2" />
        <Skeleton height={12} width="50%" rounded="sm" />
      </div>
    </div>
    <TextSkeleton lines={3} widths={['100%', '90%', '60%']} />
  </div>
);

// List Item Skeleton Component
export const ListItemSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`flex items-center space-x-3 p-3 ${className}`}>
    <AvatarSkeleton size={24} />
    <div className="flex-1">
      <Skeleton height={14} width="60%" rounded="sm" className="mb-1" />
      <Skeleton height={12} width="40%" rounded="sm" />
    </div>
    <Skeleton width={60} height={20} rounded="sm" />
  </div>
);

// Button Skeleton Component
export const ButtonSkeleton: React.FC<{
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}> = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'h-8 w-16',
    md: 'h-10 w-20',
    lg: 'h-12 w-24',
  };

  return (
    <Skeleton
      className={`${sizeClasses[size]} ${className}`}
      rounded="md"
    />
  );
};
