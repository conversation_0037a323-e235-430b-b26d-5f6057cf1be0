import React, { useState, useEffect } from 'react';
import { Room, RoomEvent, RemoteParticipant, LocalParticipant, Track } from 'livekit-client';

interface WebRTCSupport {
  getUserMedia: boolean;
  RTCPeerConnection: boolean;
  RTCSessionDescription: boolean;
  RTCIceCandidate: boolean;
  WebSocket: boolean;
  mediaDevices: boolean;
}

export const LiveKitConnectionTest: React.FC = () => {
  const [webrtcSupport, setWebrtcSupport] = useState<WebRTCSupport>({
    getUserMedia: false,
    RTCPeerConnection: false,
    RTCSessionDescription: false,
    RTCIceCandidate: false,
    WebSocket: false,
    mediaDevices: false,
  });
  const [connectionStatus, setConnectionStatus] = useState<string>('Not tested');
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState<boolean>(false);
  const [room, setRoom] = useState<Room | null>(null);
  const [participants, setParticipants] = useState<string[]>([]);

  // LiveKit test configuration - Editable
  const [livekitUrl, setLivekitUrl] = useState('wss://livekit.newhorizonco.uk');
  const [accessToken, setAccessToken] = useState('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTI3NDc2MDAsImlzcyI6IkFQSWZOdkdpVXpJVUNsNiIsIm5iZiI6MTc1MjY2MTIwMCwic3ViIjoiZmFlemUgMiIsInZpZGVvIjp7ImNhblB1Ymxpc2giOnRydWUsImNhblB1Ymxpc2hEYXRhIjp0cnVlLCJjYW5TdWJzY3JpYmUiOnRydWUsInJvb20iOiI5ODptZXNiYWhpIiwicm9vbUpvaW4iOnRydWV9fQ.JhGCiOJUzINilsInR5cCl6k');


  // Check WebRTC support on component mount
  useEffect(() => {
    checkWebRTCSupport();

    // Cleanup on unmount
    return () => {
      if (room) {
        room.disconnect();
      }
    };
  }, [room]);

  const checkWebRTCSupport = () => {
    console.log('🔍 [WEBRTC-TEST] Checking WebRTC support...');
    
    const support: WebRTCSupport = {
      getUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
      RTCPeerConnection: !!(window.RTCPeerConnection),
      RTCSessionDescription: !!(window.RTCSessionDescription),
      RTCIceCandidate: !!(window.RTCIceCandidate),
      WebSocket: !!(window.WebSocket),
      mediaDevices: !!(navigator.mediaDevices),
    };

    console.log('🔍 [WEBRTC-TEST] Support results:', support);
    console.log('🔍 [WEBRTC-TEST] User Agent:', navigator.userAgent);

    // Log each support item individually for better debugging
    Object.entries(support).forEach(([key, value]) => {
      console.log(`🔍 [WEBRTC-TEST] ${key}: ${value ? '✅' : '❌'}`);
    });
    
    setWebrtcSupport(support);
  };

  const testLiveKitConnection = async () => {
    console.log('🔘 [LIVEKIT-TEST] Button clicked - starting LiveKit connection test...');
    setIsConnecting(true);
    setConnectionError(null);
    setConnectionStatus('Connecting to LiveKit room...');

    try {
      console.log('🔗 [LIVEKIT-TEST] Creating Room instance...');
      const newRoom = new Room();

      // Add event listeners
      newRoom.on(RoomEvent.Connected, () => {
        console.log('✅ [LIVEKIT-TEST] Connected to room successfully!');
        setConnectionStatus('✅ Connected to LiveKit room!');

        // Get participants
        const participantNames = Array.from(newRoom.remoteParticipants.values())
          .map(p => p.identity);
        setParticipants(participantNames);

        console.log('👥 [LIVEKIT-TEST] Participants:', participantNames);
      });

      newRoom.on(RoomEvent.Disconnected, (reason) => {
        console.log('� [LIVEKIT-TEST] Disconnected from room:', reason);
        setConnectionStatus('Disconnected from room');
      });

      newRoom.on(RoomEvent.ConnectionStateChanged, (state) => {
        console.log('🔄 [LIVEKIT-TEST] Connection state changed:', state);
        if (state === 'disconnected') {
          setConnectionStatus('❌ Connection failed');
        }
      });

      newRoom.on(RoomEvent.ParticipantConnected, (participant: RemoteParticipant) => {
        console.log('� [LIVEKIT-TEST] Participant joined:', participant.identity);
        const participantNames = Array.from(newRoom.remoteParticipants.values())
          .map(p => p.identity);
        setParticipants(participantNames);
      });

      console.log('🔗 [LIVEKIT-TEST] Attempting to connect to:', LIVEKIT_URL);
      console.log('🎫 [LIVEKIT-TEST] Using token:', ACCESS_TOKEN.substring(0, 50) + '...');

      // Connect to room
      await newRoom.connect(LIVEKIT_URL, ACCESS_TOKEN);

      setRoom(newRoom);

    } catch (error: any) {
      console.error('❌ [LIVEKIT-TEST] Connection test failed:', error);
      setConnectionError(error.message);
      setConnectionStatus('❌ Connection failed');
    } finally {
      setIsConnecting(false);
    }
  };

  const disconnectFromRoom = async () => {
    console.log('🔘 [LIVEKIT-TEST] Disconnecting from room...');
    if (room) {
      await room.disconnect();
      setRoom(null);
      setParticipants([]);
      setConnectionStatus('Disconnected from room');
      console.log('✅ [LIVEKIT-TEST] Disconnected successfully');
    }
  };

  const testRTCPeerConnection = async () => {
    console.log('🔘 [RTC-TEST] Button clicked - starting RTCPeerConnection test...');

    if (!webrtcSupport.RTCPeerConnection) {
      console.log('❌ [RTC-TEST] RTCPeerConnection not supported');
      setConnectionError('RTCPeerConnection not supported in this WebView');
      setConnectionStatus('❌ RTCPeerConnection not supported');
      return;
    }

    try {
      console.log('🔍 [RTC-TEST] Testing RTCPeerConnection...');
      
      const pc = new RTCPeerConnection({
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' }
        ]
      });

      console.log('✅ [RTC-TEST] RTCPeerConnection created successfully');
      console.log('🔍 [RTC-TEST] Connection state:', pc.connectionState);
      console.log('🔍 [RTC-TEST] ICE connection state:', pc.iceConnectionState);
      
      pc.close();
      setConnectionStatus('✅ RTCPeerConnection test passed!');
      
    } catch (error: any) {
      console.error('❌ [RTC-TEST] RTCPeerConnection test failed:', error);
      setConnectionError(`RTCPeerConnection error: ${error.message}`);
    }
  };

  const decodeToken = () => {
    try {
      const payload = ACCESS_TOKEN.split('.')[1];
      const decoded = JSON.parse(atob(payload));
      console.log('🔍 [TOKEN] Decoded token:', decoded);
      return decoded;
    } catch (error) {
      console.error('❌ [TOKEN] Failed to decode token:', error);
      return null;
    }
  };

  const tokenData = decodeToken();

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-2xl mx-auto">
      <h2 className="text-xl font-bold mb-4 text-gray-800">LiveKit Connection Test</h2>

      {/* Debug Info */}
      <div className="mb-4 p-2 bg-red-50 border border-red-200 rounded text-sm">
        <div><strong>Debug:</strong></div>
        <div>isConnecting: {isConnecting.toString()}</div>
        <div>WebSocket Support: {webrtcSupport.WebSocket.toString()}</div>
        <div>RTCPeerConnection Support: {webrtcSupport.RTCPeerConnection.toString()}</div>
      </div>
      
      {/* WebRTC Support Status */}
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
        <h3 className="font-semibold text-blue-800 mb-2">WebRTC Support:</h3>
        <div className="grid grid-cols-2 gap-2 text-sm">
          {Object.entries(webrtcSupport).map(([key, supported]) => (
            <div key={key} className="flex items-center">
              <span className={`mr-2 ${supported ? 'text-green-600' : 'text-red-600'}`}>
                {supported ? '✅' : '❌'}
              </span>
              <span className="text-gray-700">{key}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Token Information */}
      {tokenData && (
        <div className="mb-4 p-3 bg-gray-50 border border-gray-200 rounded">
          <h3 className="font-semibold text-gray-800 mb-2">Token Info:</h3>
          <div className="text-sm text-gray-600">
            <div><strong>Room:</strong> {tokenData.video?.room || 'N/A'}</div>
            <div><strong>User:</strong> {tokenData.sub || 'N/A'}</div>
            <div><strong>Expires:</strong> {tokenData.exp ? new Date(tokenData.exp * 1000).toLocaleString() : 'N/A'}</div>
            <div><strong>Can Publish:</strong> {tokenData.video?.canPublish ? '✅' : '❌'}</div>
          </div>
        </div>
      )}

      {/* Connection Status */}
      <div className="mb-4 p-3 bg-gray-50 border border-gray-200 rounded">
        <h3 className="font-semibold text-gray-800 mb-2">Connection Status:</h3>
        <div className="text-sm text-gray-700">{connectionStatus}</div>
        {connectionError && (
          <div className="mt-2 text-sm text-red-600 bg-red-50 p-2 rounded">
            <strong>Error:</strong> {connectionError}
          </div>
        )}
        {room && (
          <div className="mt-2 text-sm text-green-600 bg-green-50 p-2 rounded">
            <strong>Room:</strong> {room.name || 'Connected'}
          </div>
        )}
      </div>

      {/* Participants */}
      {participants.length > 0 && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
          <h3 className="font-semibold text-blue-800 mb-2">Participants ({participants.length}):</h3>
          <div className="text-sm text-blue-700">
            {participants.map((participant, index) => (
              <div key={index}>👤 {participant}</div>
            ))}
          </div>
        </div>
      )}

      {/* Test Buttons */}
      <div className="flex gap-3 flex-wrap">
        {/* Simple Test Button */}
        <button
          onClick={() => {
            console.log('🔘 [SIMPLE-TEST] Simple button clicked!');
            alert('Simple button works!');
          }}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          style={{ cursor: 'pointer', pointerEvents: 'auto', zIndex: 10 }}
        >
          Simple Test
        </button>
        <button
          onClick={() => {
            console.log('🔘 [UI] LiveKit button clicked');
            console.log('🔘 [UI] isConnecting:', isConnecting);
            console.log('🔘 [UI] WebSocket support:', webrtcSupport.WebSocket);
            testLiveKitConnection();
          }}
          onMouseDown={() => console.log('🔘 [UI] LiveKit button mouse down')}
          onMouseUp={() => console.log('🔘 [UI] LiveKit button mouse up')}
          disabled={isConnecting || !webrtcSupport.WebSocket}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          style={{
            cursor: (isConnecting || !webrtcSupport.WebSocket) ? 'not-allowed' : 'pointer',
            pointerEvents: 'auto',
            zIndex: 10
          }}
        >
          {isConnecting ? 'Connecting...' : 'Connect to LiveKit Room'}
        </button>

        {/* Disconnect Button */}
        {room && (
          <button
            onClick={() => {
              console.log('🔘 [UI] Disconnect button clicked');
              disconnectFromRoom();
            }}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            style={{ cursor: 'pointer', pointerEvents: 'auto', zIndex: 10 }}
          >
            Disconnect
          </button>
        )}

        <button
          onClick={() => {
            console.log('🔘 [UI] RTCPeerConnection button clicked');
            console.log('🔘 [UI] RTCPeerConnection support:', webrtcSupport.RTCPeerConnection);
            testRTCPeerConnection();
          }}
          disabled={!webrtcSupport.RTCPeerConnection}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          style={{
            cursor: !webrtcSupport.RTCPeerConnection ? 'not-allowed' : 'pointer',
            pointerEvents: 'auto',
            zIndex: 10
          }}
        >
          Test RTCPeerConnection
        </button>
        
        <button
          onClick={() => {
            console.log('🔘 [UI] Recheck Support button clicked');
            checkWebRTCSupport();
          }}
          className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          style={{
            cursor: 'pointer',
            pointerEvents: 'auto',
            zIndex: 10
          }}
        >
          Recheck Support
        </button>
      </div>

      {/* Server Info */}
      <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded text-sm">
        <h3 className="font-semibold text-yellow-800 mb-1">Server Info:</h3>
        <div className="text-yellow-700">
          <div><strong>URL:</strong> {LIVEKIT_URL}</div>
          <div><strong>Token:</strong> {ACCESS_TOKEN.substring(0, 30)}...</div>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-4 text-xs text-gray-500">
        <p><strong>Instructions:</strong></p>
        <ul className="list-disc list-inside mt-1 space-y-1">
          <li>Check WebRTC support in your WebView</li>
          <li>Test WebSocket connection to LiveKit server</li>
          <li>Test RTCPeerConnection creation</li>
          <li>View console logs for detailed information</li>
        </ul>
      </div>
    </div>
  );
};
