import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  ModalContent,
  ModalBody,
  Button,
  Avatar,
  Badge,
  Tabs,
  Tab,
  Card,
  CardBody,
  Chip,
  Tooltip
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { motion } from "framer-motion";
import type { TeamMember } from "../types/TeamMember";
import { ActivityCalendar } from "./ActivityCalendar";

// Helper function to format last seen date
const formatLastSeen = (lastSeen: string | undefined): string => {
  if (!lastSeen) return "Unknown";

  try {
    const date = new Date(lastSeen);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInMinutes < 1) {
      return "Just now";
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    } else if (diffInDays < 7) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    } else {
      // For older dates, show formatted date
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  } catch (error) {
    return "Unknown";
  }
};

interface UserProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: TeamMember | null;
  isLoading?: boolean;
}

export const UserProfileModal: React.FC<UserProfileModalProps> = ({
  isOpen,
  onClose,
  user,
  isLoading = false
}) => {
  const [activeTab, setActiveTab] = useState("bio");

  if (!isOpen) return null;

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      hideCloseButton={true}
      size="3xl"
      classNames={{
        base: "bg-custom-card text-white",
        closeButton: "text-white hover:bg-custom-border/50"
      }}
      backdrop="blur"
      motionProps={{
        variants: {
          enter: {
            opacity: 1,
            scale: 1,
            y: 0,
            transition: { duration: 0.3, ease: [0.16, 1, 0.3, 1] }
          },
          exit: {
            opacity: 0,
            scale: 0.98,
            y: 20,
            transition: { duration: 0.2, ease: [0.36, 0, 0.66, -0.56] }
          }
        }
      }}
    >
      <ModalContent>
        <ModalBody style={{paddingRight:0}} className="p-0 max-h-[90vh] min-h-[90vh] overflow-y-auto custom-scrollbar">
          {/* Loading State */}
          {isLoading && (
            <div className="flex flex-col items-center justify-center min-h-[90vh] bg-custom-sidebar">
              <div className="flex flex-col items-center gap-4">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-custom-primary"></div>
                <div className="text-custom-text">Loading employee details...</div>
              </div>
            </div>
          )}

          {/* Content */}
          {!isLoading && user && (
            <div className="flex flex-col">
            {/* Header Section */}
            <div className="relative p-8 bg-custom-sidebar rounded-t-lg">
              <div className="flex flex-col md:flex-row gap-6">
                <div className="absolute top-6 right-6 flex gap-6 text-custom-muted">
                  <Tooltip content="Chat">
                    <Icon 
                      icon="lucide:message-circle" 
                      className="w-5 h-5 cursor-pointer hover:text-white transition-colors" 
                    />
                  </Tooltip>
                  <Tooltip content="Call">
                    <Icon 
                      icon="lucide:phone" 
                      className="w-5 h-5 cursor-pointer hover:text-white transition-colors" 
                    />
                  </Tooltip>
                  <Tooltip content="View Screen">
                    <Icon 
                      icon="lucide:monitor" 
                      className="w-5 h-5 cursor-pointer hover:text-white transition-colors" 
                    />
                  </Tooltip>
                </div>
                <Avatar
                  src={user.avatar}
                  className="w-20 h-20 md:w-20 md:h-20 text-3xl"
                  isBordered
                  color={user.isOnline ? "success" : "default"}
                />

                
                <div className="flex flex-col">
                  <div className="flex flex-wrap items-center gap-2">
                    <h1 className="text-white text-xl font-semibold">{user.fullName || user.name}</h1>
                    <Badge color="success" size="sm" className="ml-2">
                      {user.isOnline ? "Online" : "Offline"}
                    </Badge>
                    <div className="text-custom-muted text-sm  flex items-center">
                    <Icon icon="lucide:clock" className="mr-1" />
                    <span>
                      {user.isOnline
                        ? "Online now"
                        : `Last online: ${formatLastSeen(user.lastSeen)}`}
                    </span>
                  </div>
                  </div>
                  
                  <div className="flex items-center gap-2 mt-1">
                    <Tooltip content="3.5 Years at Acme Inc.">
                      <div className="cursor-pointer bg-blue-500 text-white px-2  rounded-md">
                        3.5
                      </div>
                    </Tooltip>
                    <h2 className="text-custom-text text-md ">{user.role}</h2>
                  </div>
                  
                  
                  
                  <blockquote className="mt-2 text-sm italic border-l-1 border-custom-muted pl-1 text-custom-muted">
                    "{user.slogan || "Design is not how it looks. It's how it works."}"
                  </blockquote>
                </div>
              </div>
            </div>
            
            {/* Tabbed Content */}
            <div className="p-6">
              <Tabs 
                selectedKey={activeTab} 
                onSelectionChange={(key) => setActiveTab(key as string)}
                variant="light"
                color="primary"
                className="mb-6"
                classNames={{
                  tabList: "gap-2",
                  tab: "px-0",
                  cursor: "w-full bg-primary",
                  tabContent: "group-data-[selected=true]:text-white px-2"
                }}
              >
                <Tab 
                  key="bio" 
                  title={
                    <div className="flex items-center">
                      <Icon icon="lucide:user" className="mr-2" />
                      Bio
                    </div>
                  }
                >
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <BioTab user={user} />
                  </motion.div>
                </Tab>
                <Tab 
                  key="skills" 
                  title={
                    <div className="flex items-center">
                      <Icon icon="lucide:award" className="mr-2" />
                      Skills
                    </div>
                  }
                >
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <SkillsTab user={user} />
                  </motion.div>
                </Tab>
                <Tab 
                  key="contact" 
                  title={
                    <div className="flex items-center">
                      <Icon icon="lucide:phone" className="mr-2" />
                      Contact
                    </div>
                  }
                >
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <ContactTab user={user} />
                  </motion.div>
                </Tab>
                <Tab 
                  key="achievements" 
                  title={
                    <div className="flex items-center">
                      <Icon icon="lucide:award" className="mr-2" />
                      PulseBoard Achievements
                    </div>
                  }
                >
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <AchievementsTab user={user} />
                  </motion.div>
                </Tab>
                <Tab 
                  key="activity" 
                  title={
                    <div className="flex items-center">
                      <Icon icon="lucide:calendar" className="mr-2" />
                      Activity Calendar
                    </div>
                  }
                >
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <ActivityTab user={user} />
                  </motion.div>
                </Tab>
                <Tab 
                  key="projects" 
                  title={
                    <div className="flex items-center">
                      <Icon icon="lucide:briefcase" className="mr-2" />
                      Projects
                    </div>
                  }
                >
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <ProjectsTab user={user} />
                  </motion.div>
                </Tab>
              </Tabs>
            </div>
          </div>
          )}

          {/* No User State */}
          {!isLoading && !user && (
            <div className="flex flex-col items-center justify-center min-h-[90vh] bg-custom-sidebar">
              <div className="text-custom-muted">No user data available</div>
            </div>
          )}
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

// Bio Tab Content
const BioTab: React.FC<{ user: TeamMember }> = ({ user }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <div className="flex flex-col gap-2">
        <h3 className="text-white text-lg font-medium">Bio</h3>
        <div className="text-custom-text space-y-3">
          <p>{user.bio || "I'm a passionate UI/UX Designer with over 5 years of experience in creating intuitive user interfaces and engaging experiences for web and mobile applications."}</p>
          <p>{"My approach combines creativity with user-centered methodologies to deliver designs that are not only visually appealing but also highly functional and accessible."}</p>
          <blockquote className="border-l-2 border-custom-primary pl-4 italic text-custom-muted">
            "The best design is the one that feels natural to use and invisible to the user."
          </blockquote>
        </div>
      </div>
    </motion.div>
  );
};

// Skills Tab Content
const SkillsTab: React.FC<{ user: TeamMember }> = ({ user }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <div className="flex flex-col gap-2">
        <h3 className="text-white text-lg font-medium">Skills</h3>
        <div className="flex flex-wrap gap-2">
          {(user.skills || ["UI Design", "Wireframing", "User Research", "Prototyping", "Figma", "Design Systems", "Usability Testing"]).map((skill, index) => (
            <Chip 
              key={index}
              size="sm"
              variant="flat"
              className="bg-custom-sidebar border border-custom-border"
            >
              {skill}
            </Chip>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

// Contact Tab Content
const ContactTab: React.FC<{ user: TeamMember }> = ({ user }) => {
  // Get contact items dynamically from all user properties
  const getContactItems = () => {
    const contacts: { key: string; label: string; value: string }[] = [];

    // Define all possible contact fields from user object
    const contactFields = {
      email: user.email,
      phone: user.phone,
      location: user.location,
      twitter: user.twitter,
      linkedin: user.linkedin,
      github: user.github,
    };

    // Dynamically add any field that has a value
    Object.entries(contactFields).forEach(([key, value]) => {
      if (value && value.trim() !== '') {
        contacts.push({
          key,
          label: key,
          value: value.trim()
        });
      }
    });

    // Add last seen status
    if (user.lastSeen) {
      contacts.push({
        key: 'lastSeen',
        label: 'Last online',
        value: formatLastSeen(user.lastSeen)
      });
    }

    return contacts;
  };

  const availableContacts = getContactItems();

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <div className="flex flex-col gap-2">
        <h3 className="text-white text-lg font-medium">Contact Information</h3>

        {availableContacts.length > 0 ? (
          <div className="space-y-3">
            {availableContacts.map((contact) => (
              <div key={contact.key} className="flex items-center justify-between p-3 bg-custom-card/30 rounded-lg border border-custom-border/20">
                <span className="text-custom-muted font-medium capitalize">{contact.label}:</span>
                <span className="text-custom-text">{contact.value}</span>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-custom-muted text-center py-8">
            No contact information available
          </div>
        )}
      </div>
    </motion.div>
  );
};

// Projects Tab Component
const ProjectsTab: React.FC<{ user: TeamMember }> = ({ user }) => {
  const projects = user.projects || [
    { name: "Time Tracker App", role: "Owner", color: "primary" },
    { name: "Dashboard Redesign", role: "Designer", color: "success" },
    { name: "Mobile Application", role: "UX Research", color: "secondary" },
    { name: "Customer Portal", role: "QA", color: "warning" }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className=""
    >
      <h3 className="text-white text-sm font-medium mb-3">Project Participation</h3>
      <div className="flex flex-wrap gap-2">
        {projects.map((project, index) => (
          <Chip
            key={index}
            className="bg-custom-sidebar border border-custom-border px-2"
            variant="flat"
            color="default"
            
          >
            {project.name}
          </Chip>
        ))}
      </div>
    </motion.div>
  );
};

// Achievements Tab Content
const AchievementsTab: React.FC<{ user: TeamMember }> = ({ user }) => {
  const achievements = user.achievements || [
    {
      id: 1,
      title: "Top Collaborator",
      date: "July 2022",
      badge: "🏅",
      description: "Recognized for exceptional teamwork and collaboration across departments"
    },
    {
      id: 2,
      title: "Best Innovator",
      date: "Q1 2024",
      badge: "💡",
      description: "Created innovative solution for improving team productivity"
    },
    {
      id: 3,
      title: "Design Excellence",
      date: "November 2023",
      badge: "🎨",
      description: "Delivered outstanding design work for the mobile app redesign project"
    },
    {
      id: 4,
      title: "Knowledge Sharing Star",
      date: "March 2023",
      badge: "🌟",
      description: "Conducted valuable training sessions for junior designers"
    }
  ];
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {achievements.map((achievement, index) => (
          <motion.div
            key={achievement.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1, duration: 0.3 }}
          >
            <Card className="bg-custom-sidebar border border-custom-border">
              <CardBody>
                <div className="flex items-start gap-3">
                  <div className="text-2xl">{achievement.badge}</div>
                  <div className="flex-1">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-1">
                      <h4 className="text-white font-medium">{achievement.title}</h4>
                      <span className="text-custom-primary text-xs">{achievement.date}</span>
                    </div>
                    <p className="text-custom-text text-sm mt-1">{achievement.description}</p>
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>
      
      <div className="mt-6 text-center">
        <Button 
          variant="flat" 
          color="primary"
          size="sm"
          startContent={<Icon icon="lucide:trophy" />}
        >
          View All Achievements
        </Button>
      </div>
    </motion.div>
  );
};

// Activity Tab Content
const ActivityTab: React.FC<{ user: TeamMember }> = ({ user }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-6"
    >
      <div className="flex items-center justify-between">
        <h3 className="text-white font-medium">Weekly Activity Pattern</h3>
      </div>
      
      <Card className="bg-custom-sidebar border border-custom-border">
        <CardBody>
          <ActivityCalendar userId={user.id} />
        </CardBody>
      </Card>
      
      <div>
        <h3 className="text-white font-medium mb-3">Typical Working Hours</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="bg-custom-sidebar border border-custom-border">
            <CardBody>
              <div className="flex items-center">
                <div className="w-2 h-full bg-success rounded-full mr-3"></div>
                <div>
                  <h4 className="text-white font-medium">Weekdays</h4>
                  <p className="text-custom-text text-sm">9:00 AM - 6:00 PM</p>
                </div>
              </div>
            </CardBody>
          </Card>
          
          <Card className="bg-custom-sidebar border border-custom-border">
            <CardBody>
              <div className="flex items-center">
                <div className="w-2 h-full bg-warning rounded-full mr-3"></div>
                <div>
                  <h4 className="text-white font-medium">Weekends</h4>
                  <p className="text-custom-text text-sm">Occasionally online</p>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>
    </motion.div>
  );
};