import React from "react";
import { Card, CardBody, Button, Input, Select, SelectItem } from "@heroui/react";
import { TauriIcon as Icon } from "./TauriIcon";
import { motion } from "framer-motion";
import { invoke } from "@tauri-apps/api/core";
import { useTimeTracking } from "../context/TimeTrackingContext";
import { useUser } from "../context/UserContext";
import { useToastHelpers } from "../context/ToastContext";
import TimeLogModal from "./TimeLogModal";

// API Response Interfaces
interface Project {
  id: number;
  name: string;
}

interface Task {
  id: number;
  name: string;
}

interface TimeLog {
  duration: string;
  notes: string;
  taskId: string;
  timestamp: Date;
  activityId?: number;
}

export const TimeTracker = () => {
  const {
    isTracking,
    toggleTracking,
    userStatus,
    setUserStatus,
    currentTimeEntry
  } = useTimeTracking();

  // Get user profile status to determine if time tracking should be enabled
  const {
    user,
    isLoading: isProfileLoading,
    hasError: hasProfileError,
    incompleteActivity,
    clearIncompleteActivity
  } = useUser();
  const { showError, showSuccess } = useToastHelpers();

  // Determine if time tracking should be enabled
  // Only enable if profile is loaded successfully (user exists and no errors)
  const isTimeTrackingEnabled = user !== null && !hasProfileError && !isProfileLoading;

  const [seconds, setSeconds] = React.useState(0);
  const [currentActivityId, setCurrentActivityId] = React.useState<number | null>(null);
  const [isStarting, setIsStarting] = React.useState(false);
  const [isStopping, setIsStopping] = React.useState(false);
  const imReadyIntervalRef = React.useRef<number | null>(null);
  const [notes, setNotes] = React.useState("");
  const [selectedTask, setSelectedTask] = React.useState<string>("");
  const [showModal, setShowModal] = React.useState(false);
  const [timeLog, setTimeLog] = React.useState<TimeLog | null>(null);
  const [modalNotes, setModalNotes] = React.useState("");
  const [modalTaskId, setModalTaskId] = React.useState("");
  const [selectedProject, setSelectedProject] = React.useState<string>("");
  const [modalProjectId, setModalProjectId] = React.useState<string>("");
  const [isModalMandatory, setIsModalMandatory] = React.useState(false); // For incomplete activity modal
  const [validationError, setValidationError] = React.useState<string | null>(null);

  // API Data State
  const [projects, setProjects] = React.useState<Project[]>([]);
  const [tasks, setTasks] = React.useState<Task[]>([]);
  const [isLoadingProjects, setIsLoadingProjects] = React.useState(false);
  const [isLoadingTasks, setIsLoadingTasks] = React.useState(false);
  const [projectsError, setProjectsError] = React.useState<string | null>(null);
  const [tasksError, setTasksError] = React.useState<string | null>(null);

  const timerRef = React.useRef<number | null>(null);

  // Fetch projects from API
  const fetchProjects = React.useCallback(async () => {
    setIsLoadingProjects(true);
    setProjectsError(null);

    try {
      console.log('🔄 [TIME-TRACKER] Fetching projects...');
      const projectsData = await invoke<Project[]>('fetch_projects');
      console.log('✅ [TIME-TRACKER] Projects fetched:', projectsData);
      setProjects(projectsData);
    } catch (error) {
      console.error('❌ [TIME-TRACKER] Failed to fetch projects:', error);
      const errorMessage = typeof error === 'string' ? error : 'Failed to fetch projects';
      setProjectsError(errorMessage);

      // Handle auth errors
      if (errorMessage === 'AUTH_EXPIRED') {
        // Could trigger logout or token refresh here
        console.log('🔐 [TIME-TRACKER] Auth expired while fetching projects');
      }
    } finally {
      setIsLoadingProjects(false);
    }
  }, []);

  // Fetch tasks for selected project
  const fetchProjectTasks = React.useCallback(async (projectId: number) => {
    setIsLoadingTasks(true);
    setTasksError(null);

    try {
      console.log('🔄 [TIME-TRACKER] Fetching tasks for project:', projectId);
      const tasksData = await invoke<Task[]>('fetch_project_tasks', { projectId });
      console.log('✅ [TIME-TRACKER] Tasks fetched:', tasksData);
      setTasks(tasksData);
    } catch (error) {
      console.error('❌ [TIME-TRACKER] Failed to fetch tasks:', error);
      const errorMessage = typeof error === 'string' ? error : 'Failed to fetch tasks';
      setTasksError(errorMessage);

      // Handle specific errors
      if (errorMessage === 'AUTH_EXPIRED') {
        console.log('🔐 [TIME-TRACKER] Auth expired while fetching tasks');
      } else if (errorMessage === 'PROJECT_NOT_FOUND') {
        console.log('🔍 [TIME-TRACKER] Project not found');
      }
    } finally {
      setIsLoadingTasks(false);
    }
  }, []);

  // Projects will be loaded on-demand when user opens the select

  // ImReady system functions
  const startImReadySystem = React.useCallback(async (activityId: number) => {
    console.log('💓 [IMREADY] Starting ImReady system for activity:', activityId);

    const sendImReady = async () => {
      try {
        const success = await invoke<boolean>('post_im_ready', { activityId });
        if (success) {
          console.log('✅ [IMREADY] ImReady sent successfully');
          // Clear any previous errors for this activity
          await invoke('delete_error_logs', { activityId });
        }
      } catch (error) {
        console.error('❌ [IMREADY] ImReady failed:', error);
        const errorMessage = typeof error === 'string' ? error : 'UNKNOWN_ERROR';

        // Determine error code
        let errorCode = 0; // Default for network errors
        if (errorMessage.includes('API_ERROR_')) {
          errorCode = parseInt(errorMessage.replace('API_ERROR_', '')) || 0;
        } else if (errorMessage === 'AUTH_EXPIRED') {
          errorCode = 403;
        } else if (errorMessage === 'ACTIVITY_ENDED') {
          errorCode = 400;
        }

        // Store error log
        const timestamp = Date.now();
        try {
          await invoke('insert_error_log', { timestamp, errorCode, activityId });
          console.log('📝 [IMREADY] Error logged:', { timestamp, errorCode, activityId });
        } catch (logError) {
          console.error('❌ [IMREADY] Failed to log error:', logError);
        }
      }
    };

    // Send first ImReady immediately
    await sendImReady();

    // Set up interval for every 10 seconds
    imReadyIntervalRef.current = window.setInterval(sendImReady, 10000);
    console.log('⏰ [IMREADY] ImReady interval started (every 10 seconds)');
  }, []);

  const stopImReadySystem = React.useCallback(() => {
    if (imReadyIntervalRef.current) {
      clearInterval(imReadyIntervalRef.current);
      imReadyIntervalRef.current = null;
      console.log('⏹️ [IMREADY] ImReady system stopped');
    }
  }, []);

  // Handle incomplete activity from profile
  React.useEffect(() => {
    if (incompleteActivity && !showModal) {
      console.log('⚠️ [TIME-TRACKER] Found incomplete activity, opening MANDATORY Overview Modal:', incompleteActivity.activity_id, 'with duration:', incompleteActivity.duration);

      // Set the current activity ID
      setCurrentActivityId(incompleteActivity.activity_id);

      // Create a time log for the incomplete activity with the actual duration
      const incompleteTimeLog: TimeLog = {
        duration: incompleteActivity.duration, // Use the actual duration from server
        notes: "",
        taskId: "",
        timestamp: new Date(),
        activityId: incompleteActivity.activity_id,
      };

      setTimeLog(incompleteTimeLog);
      setIsModalMandatory(true); // Mark as mandatory
      setValidationError(null); // Reset validation error
      setShowModal(true);
    }
  }, [incompleteActivity, showModal]);

  // Handle project selection change
  const handleProjectChange = React.useCallback((projectId: string) => {
    setSelectedProject(projectId);
    setSelectedTask(""); // Clear task selection
    setTasks([]); // Clear current tasks

    if (projectId) {
      const projectIdNum = parseInt(projectId, 10);
      if (!isNaN(projectIdNum)) {
        fetchProjectTasks(projectIdNum);
      }
    }
  }, [fetchProjectTasks]);



  const formatTime = (totalSeconds: number) => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    
    return [
      hours.toString().padStart(2, "0"),
      minutes.toString().padStart(2, "0"),
      seconds.toString().padStart(2, "0"),
    ].join(":");
  };
  
  const startTracking = async () => {
    if (isStarting) return; // Prevent double clicks

    try {
      setIsStarting(true);
      console.log('🚀 [TIME-TRACKER] Starting activity via API...');

      // Call start_activity API
      const activityId = await invoke<number>('start_activity');
      console.log('✅ [TIME-TRACKER] Activity started with ID:', activityId);

      // Store activity ID
      setCurrentActivityId(activityId);

      // Start local timer
      timerRef.current = window.setInterval(() => {
        setSeconds(prev => prev + 1);
      }, 1000);

      // Start ImReady system
      await startImReadySystem(activityId);

      // Use TimeTrackingContext to start tracking BEFORE enabling button
      if (!isTracking) {
        toggleTracking();
      }

      // Small delay to ensure UI state change is visible before enabling button
      await new Promise(resolve => setTimeout(resolve, 100));

      // Enable button AFTER state change is complete
      setIsStarting(false);

      showSuccess('Time Tracking Started', 'Activity started successfully');

    } catch (error) {
      console.error('❌ [TIME-TRACKER] Failed to start activity:', error);
      const errorMessage = typeof error === 'string' ? error : 'Failed to start activity';

      // Handle specific errors
      if (errorMessage === 'AUTH_EXPIRED') {
        showError('Authentication Expired', 'Please login again');
      } else if (errorMessage === 'NETWORK_CONNECTION_FAILED') {
        showError('Connection Failed', 'Please check your internet connection');
      } else {
        showError('Start Failed', 'Failed to start time tracking', 2000); // Hide after 2 seconds
      }

      // Enable button on error
      setIsStarting(false);
    }
  };

  const stopTracking = async () => {
    if (isStopping) return; // Prevent double clicks

    console.log('🛑 [TIME-TRACKER] stopTracking called with state:', {
      seconds,
      currentActivityId,
      isTracking,
      isStopping
    });

    setIsStopping(true);

    try {
      if (timerRef.current) clearInterval(timerRef.current);

      // Stop ImReady system
      stopImReadySystem();

      console.log('🔄 [TIME-TRACKER] About to call handleLogTime...');
      await handleLogTime();
      console.log('✅ [TIME-TRACKER] handleLogTime completed');

      // Small delay to ensure UI state change is visible before enabling button
      await new Promise(resolve => setTimeout(resolve, 100));

      // Enable button AFTER handleLogTime completes (which includes toggleTracking)
      setIsStopping(false);
    } catch (error) {
      console.error('❌ [TIME-TRACKER] Error in stopTracking:', error);
      // Enable button on error
      setIsStopping(false);
    }
  };

  const toggleTimer = () => {
    // Don't allow starting time tracking if profile is not loaded successfully
    if (!isTimeTrackingEnabled && !isTracking) {
      console.log('⚠️ [TIME-TRACKER] Time tracking disabled - profile not loaded');
      return;
    }

    // Don't allow clicks while starting or stopping
    if (isStarting || isStopping) {
      console.log('⚠️ [TIME-TRACKER] Button disabled - operation in progress');
      return;
    }

    if (!isTracking) startTracking();
    else stopTracking();
  };
  
  const handleLogTime = async () => {
    console.log('🔍 [TIME-TRACKER] handleLogTime called with:', {
      seconds,
      currentActivityId,
      condition1: seconds === 0,
      condition2: !currentActivityId
    });

    if (!currentActivityId) {
      console.log('⚠️ [TIME-TRACKER] handleLogTime early return - no currentActivityId:', currentActivityId);
      return;
    }

    if (seconds === 0) {
      console.log('⚠️ [TIME-TRACKER] Warning: seconds is 0, but continuing with API call');
    }

    try {
      console.log('🏁 [TIME-TRACKER] Ending activity via API...');

      // Prepare data for end activity API
      const projectId = selectedProject ? parseInt(selectedProject) : null;
      const taskId = selectedTask ? parseInt(selectedTask) : null;
      const notesText = notes.trim() || null;
      const duration = formatTime(seconds);

      console.log('📋 [TIME-TRACKER] End activity data:', {
        activityId: currentActivityId,
        projectId: projectId,
        taskId: taskId,
        notes: notesText,
        durationSeconds: seconds
      });

      // Call end_activity API
      console.log('🔄 [TIME-TRACKER] About to call invoke end_activity...');
      const success = await invoke<boolean>('end_activity', {
        activityId: currentActivityId,
        projectId: projectId,
        taskId: taskId,
        notes: notesText,
        durationSeconds: seconds
      });
      console.log('✅ [TIME-TRACKER] invoke end_activity returned:', success);

      if (success) {
        console.log('✅ [TIME-TRACKER] Activity ended successfully');

        // Prepare data for modal
        const newTimeLog = {
          duration: formatTime(seconds),
          notes,
          taskId: selectedTask,
          project: selectedProject,
          timestamp: new Date(),
          activityId: currentActivityId
        };

        setTimeLog(newTimeLog);
        setModalNotes(notes);
        setModalTaskId(selectedTask);
        setModalProjectId(selectedProject);
        setShowModal(true);

        // Reset timer and activity
        setSeconds(0);
        setCurrentActivityId(null);
        if (timerRef.current) clearInterval(timerRef.current);

        // Use TimeTrackingContext to stop tracking
        if (isTracking) {
          toggleTracking();
        }

        showSuccess('Time Logged', 'Activity ended successfully');
      }

    } catch (error) {
      console.error('❌ [TIME-TRACKER] Failed to end activity:', error);
      const errorMessage = typeof error === 'string' ? error : 'Failed to end activity';

      // Handle specific errors
      if (errorMessage === 'AUTH_EXPIRED') {
        showError('Authentication Expired', 'Please login again');
      } else if (errorMessage === 'NETWORK_CONNECTION_FAILED') {
        showError('Connection Failed', 'Please check your internet connection');
      } else {
        showError('End Failed', 'Failed to end time tracking');
      }

      // Still show modal for manual completion
      const newTimeLog = {
        duration: formatTime(seconds),
        notes,
        taskId: selectedTask,
        project: selectedProject,
        timestamp: new Date(),
        activityId: currentActivityId
      };

      setTimeLog(newTimeLog);
      setModalNotes(notes);
      setModalTaskId(selectedTask);
      setModalProjectId(selectedProject);
      setShowModal(true);

      // Reset timer and activity
      setSeconds(0);
      setCurrentActivityId(null);
      if (timerRef.current) clearInterval(timerRef.current);

      // Use TimeTrackingContext to stop tracking
      if (isTracking) {
        toggleTracking();
      }
    }
  };
  
  const handleSaveLog = async () => {
    if (!timeLog) return;

    // Validation for mandatory modal
    if (isModalMandatory) {
      const hasProject = modalProjectId && modalProjectId.trim() !== "";
      const hasNotes = modalNotes && modalNotes.trim() !== "";

      if (!hasProject && !hasNotes) {
        setValidationError("Please select a project or add notes to continue.");
        return;
      }
    }

    // Clear validation error if validation passes
    setValidationError(null);

    try {
      console.log('💾 [TIME-TRACKER] Saving time log via update API...');

      // Get activity ID from timeLog
      const activityId = timeLog.activityId;
      if (!activityId) {
        console.error('❌ [TIME-TRACKER] No activity ID found in timeLog');
        showError('Save Failed', 'No activity ID found');
        return;
      }

      // Prepare data for update activity API
      const projectId = modalProjectId ? parseInt(modalProjectId) : null;
      const taskId = modalTaskId ? parseInt(modalTaskId) : null;
      const notesText = modalNotes.trim() || null;

      console.log('📋 [TIME-TRACKER] Update activity data:', {
        activityId,
        projectId,
        taskId,
        notes: notesText
      });

      // Call update_activity API
      const success = await invoke<boolean>('update_activity', {
        activityId,
        projectId,
        taskId,
        notes: notesText
      });

      if (success) {
        console.log('✅ [TIME-TRACKER] Activity updated successfully');

        // Clear incomplete activity from UserContext if this was a mandatory modal
        if (isModalMandatory) {
          console.log('🗑️ [TIME-TRACKER] Clearing incomplete activity after successful update');
          clearIncompleteActivity();
        }

        showSuccess('Time Log Saved', 'Activity details updated successfully');
      }

    } catch (error) {
      console.error('❌ [TIME-TRACKER] Failed to update activity:', error);
      const errorMessage = typeof error === 'string' ? error : 'Failed to update activity';

      // Handle specific errors
      if (errorMessage === 'AUTH_EXPIRED') {
        showError('Authentication Expired', 'Please login again');
      } else if (errorMessage === 'NETWORK_CONNECTION_FAILED') {
        showError('Connection Failed', 'Please check your internet connection');
      } else {
        showError('Save Failed', 'Failed to save time log');
      }
    } finally {
      // Always close modal and reset form
      setShowModal(false);
      setIsModalMandatory(false); // Reset mandatory flag
      setValidationError(null); // Reset validation error
      setNotes("");
      setSelectedTask("");
      setModalNotes("");
      setModalTaskId("");
      setModalProjectId("");
    }
  };
  
  React.useEffect(() => {
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
      stopImReadySystem();
    };
  }, [stopImReadySystem]);
  
  return (
    <>
      <Card className="bg-custom-card border-custom-border shadow-none">
        <CardBody className="p-6">
          <div className="flex justify-between items-center mb-7">
            <h3 className="text-white text-lg font-medium">Time Tracking</h3>
            <Button
              isIconOnly
              variant="light"
              size="sm"
              className="text-custom-muted"
            >
              <Icon icon="lucide:more-vertical" />
            </Button>
          </div>

          <div className="flex flex-col items-center">
            <motion.div
              className={`relative w-24 h-24 mb-6 ${
                (isTimeTrackingEnabled || isTracking) && !isStarting && !isStopping
                  ? 'cursor-pointer'
                  : 'cursor-not-allowed'
              }`}
              onClick={toggleTimer}
              whileHover={(isTimeTrackingEnabled || isTracking) && !isStarting && !isStopping ? { scale: 1.05 } : {}}
              whileTap={(isTimeTrackingEnabled || isTracking) && !isStarting && !isStopping ? { scale: 0.95 } : {}}
            >
              <div className={`${
                isTracking ? 'absolute inset-0 rounded-full flex items-center justify-center bg-red-500/10'
                : 'absolute inset-0 rounded-full flex items-center justify-center bg-primary/10'
              } ${
                !isTimeTrackingEnabled && !isTracking ? 'opacity-50' : ''
              } ${
                isStarting || isStopping ? 'opacity-60' : ''
              }`}>
                <motion.div
                  animate={{
                    boxShadow: isTracking && !isStarting && !isStopping
                      ? [
                          '0 0 0 0 rgba(239, 68, 68, 0.4)',
                          '0 0 0 10px rgba(239, 68, 68, 0)',
                        ]
                      : 'none',
                  }}
                  transition={{ repeat: isTracking && !isStarting && !isStopping ? Infinity : 0, duration: 1.5, repeatType: 'loop' }}
                  className={`w-20 h-20 rounded-full flex items-center justify-center ${
                    isTracking ? 'bg-red-500' : 'bg-primary'
                  } ${
                    !isTimeTrackingEnabled && !isTracking ? 'opacity-50' : ''
                  } ${
                    isStarting || isStopping ? 'opacity-60' : ''
                  } transition-all duration-300`}
                >
                  <Icon
                    icon={isTracking ? 'lucide:pause' : 'lucide:play'}
                    className={`text-white text-2xl ${
                      !isTimeTrackingEnabled && !isTracking ? 'opacity-70' : ''
                    } ${
                      isStarting || isStopping ? 'opacity-70' : ''
                    } transition-all duration-300`}
                  />
                </motion.div>
              </div>
            </motion.div>
            
            <div className="timer-display text-4xl font-bold text-white tracking-wider mb-8">
              {formatTime(seconds)}
            </div>
            
            {/* Inputs */}
            <div className="w-full space-y-3 mt-2">
              <Input
                label="Notes"
                placeholder={!isTimeTrackingEnabled ? "Profile loading..." : "What are you working on?"}
                value={notes}
                onValueChange={setNotes}
                isDisabled={!isTracking || !isTimeTrackingEnabled}
                classNames={{
                  inputWrapper: 'bg-custom-sidebar border-custom-border',
                  input: 'text-custom-text',
                  label: 'text-custom-muted',
                }}
              />

              <Select
                label="Select Project"
                placeholder={!isTimeTrackingEnabled ? "Profile loading..." : isLoadingProjects ? "Loading projects..." : "Choose project"}
                selectedKeys={selectedProject ? [selectedProject] : []}
                onSelectionChange={(keys) => {
                  const key = (keys as Set<string>).values().next().value;
                  if (key) {
                    handleProjectChange(key);
                  }
                }}
                onOpenChange={(isOpen) => {
                  // Fetch projects when dropdown opens and projects haven't been loaded yet
                  if (isOpen && projects.length === 0 && !isLoadingProjects && !projectsError && isTimeTrackingEnabled) {
                    fetchProjects();
                  }
                }}
                isDisabled={!isTracking || isLoadingProjects || !isTimeTrackingEnabled}
                isLoading={isLoadingProjects}
                classNames={{
                  trigger: 'bg-custom-sidebar border-custom-border',
                  value: 'text-custom-text',
                  label: 'text-custom-muted',
                }}
              >
                {projects.map((project) => (
                  <SelectItem key={project.id.toString()} textValue={project.name}>
                    {project.name}
                  </SelectItem>
                ))}
              </Select>
              {projectsError && (
                <p className="text-red-500 text-sm mt-1">
                  {projectsError === 'AUTH_EXPIRED' ? 'Authentication expired' :
                   projectsError === 'NETWORK_CONNECTION_FAILED' ? 'Network connection failed' :
                   'Failed to load projects'}
                </p>
              )}
              
              <Select
                label="Select Task"
                placeholder={!isTimeTrackingEnabled ? "Profile loading..." : isLoadingTasks ? "Loading tasks..." : selectedProject ? "Select a task" : "Select a project first"}
                selectedKeys={selectedTask ? [selectedTask] : []}
                onSelectionChange={(keys) => {
                  const key = (keys as Set<string>).values().next().value;
                  setSelectedTask(key || '');
                }}
                isDisabled={!isTracking || !selectedProject || isLoadingTasks || !isTimeTrackingEnabled}
                isLoading={isLoadingTasks}
                classNames={{
                  trigger: 'bg-custom-sidebar border-custom-border',
                  value: 'text-custom-text',
                  label: 'text-custom-muted',
                }}
              >
                {tasks.map((task) => (
                  <SelectItem key={task.id.toString()} textValue={task.name}>
                    <div className="flex flex-col">
                      <span>{task.name}</span>
                      <span className="text-tiny text-custom-muted">Task ID: {task.id}</span>
                    </div>
                  </SelectItem>
                ))}
              </Select>
              {tasksError && (
                <p className="text-red-500 text-sm mt-1">
                  {tasksError === 'AUTH_EXPIRED' ? 'Authentication expired' :
                   tasksError === 'PROJECT_NOT_FOUND' ? 'Project not found' :
                   tasksError === 'NETWORK_CONNECTION_FAILED' ? 'Network connection failed' :
                   'Failed to load tasks'}
                </p>
              )}
            </div>
          </div>
        </CardBody>
      </Card>
      
      {/* Time Log Modal */}
      <TimeLogModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        type={isModalMandatory ? "complete" : "overview"}
        timeLog={timeLog ? {
          duration: timeLog.duration,
          notes: timeLog.notes,
          taskId: timeLog.taskId,
          timestamp: timeLog.timestamp,
          activityId: timeLog.activityId,
        } : null}
        isMandatory={isModalMandatory}
        validationError={validationError}
        modalNotes={modalNotes}
        modalTaskId={modalTaskId}
        modalProjectId={modalProjectId}
        onNotesChange={setModalNotes}
        onTaskChange={(taskId) => setModalTaskId(taskId)}
        onProjectChange={(projectId) => {
          console.log('📋 [MODAL] Project selection changed to:', projectId);
          setModalProjectId(projectId);
          setModalTaskId(""); // Reset task when project changes
          setTasks([]); // Clear current tasks

          if (projectId) {
            const projectIdNum = parseInt(projectId, 10);
            if (!isNaN(projectIdNum)) {
              console.log('📋 [MODAL] Fetching tasks for project:', projectIdNum);
              fetchProjectTasks(projectIdNum);
            }
          }
        }}
        onSave={handleSaveLog}
        projects={projects}
        tasks={tasks}
        isLoadingProjects={isLoadingProjects}
        isLoadingTasks={isLoadingTasks}
        onProjectDropdownOpen={() => {
          if (projects.length === 0 && !isLoadingProjects && !projectsError) {
            fetchProjects();
          }
        }}
        onTaskDropdownOpen={(projectId) => {
          if (projectId && tasks.length === 0 && !isLoadingTasks && !tasksError) {
            const projectIdNum = parseInt(projectId, 10);
            if (!isNaN(projectIdNum)) {
              console.log('📋 [MODAL] Fetching tasks on dropdown open for project:', projectIdNum);
              fetchProjectTasks(projectIdNum);
            }
          }
        }}
      />

    </>
  );
};
