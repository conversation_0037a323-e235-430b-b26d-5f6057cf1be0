import React, { useState, useEffect } from 'react';
import { Badge, Tooltip } from '@heroui/react';
import { useCentrifugo, CentrifugoConnectionState } from '../hooks/useCentrifugo';

interface ConnectionStatusBadgeProps {
  className?: string;
}

export const ConnectionStatusBadge: React.FC<ConnectionStatusBadgeProps> = ({ 
  className = '' 
}) => {
  const { status, isConnected, isConnecting, hasError, messageCount } = useCentrifugo();
  const [showDataReceived, setShowDataReceived] = useState(false);
  const [lastMessageCount, setLastMessageCount] = useState(0);

  // Show blue indicator when new data is received
  useEffect(() => {
    if (messageCount > lastMessageCount && messageCount > 0) {
      setShowDataReceived(true);
      setLastMessageCount(messageCount);
      
      // Reset to normal state after 2 seconds
      const timer = setTimeout(() => {
        setShowDataReceived(false);
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [messageCount, lastMessageCount]);

  // Determine badge color and status
  const getBadgeProps = () => {
    if (showDataReceived) {
      return {
        color: 'primary' as const,
        variant: 'solid' as const,
        className: 'animate-pulse',
        tooltip: 'Data received from Centrifugo'
      };
    }
    
    if (hasError) {
      return {
        color: 'danger' as const,
        variant: 'solid' as const,
        className: '',
        tooltip: `Connection error: ${status.state}`
      };
    }
    
    if (isConnected) {
      return {
        color: 'success' as const,
        variant: 'solid' as const,
        className: '',
        tooltip: `Connected to channel: ${status.channel || 'Unknown'}`
      };
    }
    
    if (isConnecting) {
      return {
        color: 'warning' as const,
        variant: 'solid' as const,
        className: 'animate-pulse',
        tooltip: 'Connecting to Centrifugo...'
      };
    }
    
    return {
      color: 'default' as const,
      variant: 'solid' as const,
      className: '',
      tooltip: 'Disconnected from Centrifugo'
    };
  };

  const badgeProps = getBadgeProps();

  return (
    <div className={`flex items-center ${className}`}>
      <Tooltip content={badgeProps.tooltip} placement="bottom">
        <div className="flex items-center space-x-2">
          <Badge
            color={badgeProps.color}
            variant={badgeProps.variant}
            size="sm"
            className={`transition-all duration-300 ${badgeProps.className}`}
          >
            <div className="w-2 h-2 rounded-full" />
          </Badge>
          
          {/* Optional status text for debugging */}
          {process.env.NODE_ENV === 'development' && (
            <span className="text-xs text-white/60 font-mono">
              {status.state}
            </span>
          )}
        </div>
      </Tooltip>
    </div>
  );
};

// Alternative compact version for minimal space usage
export const ConnectionStatusDot: React.FC<ConnectionStatusBadgeProps> = ({ 
  className = '' 
}) => {
  const { status, isConnected, isConnecting, hasError, messageCount } = useCentrifugo();
  const [showDataReceived, setShowDataReceived] = useState(false);
  const [lastMessageCount, setLastMessageCount] = useState(0);

  // Show blue indicator when new data is received
  useEffect(() => {
    if (messageCount > lastMessageCount && messageCount > 0) {
      setShowDataReceived(true);
      setLastMessageCount(messageCount);
      
      const timer = setTimeout(() => {
        setShowDataReceived(false);
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [messageCount, lastMessageCount]);

  // Determine dot color
  const getDotColor = () => {
    if (showDataReceived) return 'bg-blue-500';
    if (hasError) return 'bg-red-500';
    if (isConnected) return 'bg-green-500';
    if (isConnecting) return 'bg-yellow-500';
    return 'bg-gray-500';
  };

  const getTooltipText = () => {
    if (showDataReceived) return 'Data received from Centrifugo';
    if (hasError) return `Connection error: ${status.state}`;
    if (isConnected) return `Connected to channel: ${status.channel || 'Unknown'}`;
    if (isConnecting) return 'Connecting to Centrifugo...';
    return 'Disconnected from Centrifugo';
  };

  return (
    <Tooltip content={getTooltipText()} placement="bottom">
      <div className={`flex items-center ${className}`}>
        <div 
          className={`w-2 h-2 rounded-full transition-all duration-300 ${getDotColor()} ${
            (isConnecting || showDataReceived) ? 'animate-pulse' : ''
          }`}
        />
      </div>
    </Tooltip>
  );
};
