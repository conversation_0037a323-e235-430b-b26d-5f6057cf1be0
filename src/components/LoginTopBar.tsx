import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@heroui/react";
import { getCurrentWindow } from '@tauri-apps/api/window';
import { TauriIcon as Icon } from "./TauriIcon";

interface LoginTopBarProps {
  onMinimize?: () => void;
  onMaximize?: () => void;
  onClose?: () => void;
}

export const LoginTopBar: React.FC<LoginTopBarProps> = ({
  onMinimize,
  onMaximize,
  onClose
}) => {
  const [isMaximized, setIsMaximized] = useState(false);
  const [appWindow, setAppWindow] = useState<any>(null);

  useEffect(() => {
    const initWindow = async () => {
      const window = getCurrentWindow();
      
      setAppWindow(window);

      // Check initial maximized state
      try {
        const maximized = await window.isMaximized();
        setIsMaximized(maximized);
      } catch (error) {
        console.error('Failed to get window maximized state:', error);
      }
    };

    initWindow();
  }, []);

  const handleMinimize = useCallback(async () => {
    try {
      if (appWindow) {
        await appWindow.minimize();
        onMinimize?.();
      }
    } catch (error) {
      console.error('Failed to minimize window:', error);
    }
  }, [appWindow, onMinimize]);

  const handleMaximize = useCallback(async () => {
    try {
      if (appWindow) {
        if (isMaximized) {
          await appWindow.unmaximize();
          setIsMaximized(false);
        } else {
          await appWindow.maximize();
          setIsMaximized(true);
        }
        onMaximize?.();
      }
    } catch (error) {
      console.error('Failed to maximize/unmaximize window:', error);
    }
  }, [appWindow, isMaximized, onMaximize]);

  const handleClose = useCallback(async () => {
    try {
      if (appWindow) {
        await appWindow.close();
        onClose?.();
      }
    } catch (error) {
      console.error('Failed to close window:', error);
    }
  }, [appWindow, onClose]);

  const handleStartDragging = useCallback(async () => {
    try {
      if (appWindow) {
        await appWindow.startDragging();
      }
    } catch (error) {
      console.error('Failed to start dragging:', error);
    }
  }, [appWindow]);

  return (
    <div className="h-10 bg-custom-sidebar/80 backdrop-blur-md border-b border-custom-border flex items-center justify-between px-4 relative z-50">
      {/* Left Section - Logo and Title */}
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-6 h-6 bg-gradient-to-br from-primary to-blue-600 rounded-lg">
          <span className="text-white text-xs font-bold">TB</span>
        </div>
        <span className="text-white text-sm font-medium">TeamBy Desktop</span>
      </div>

      {/* Center Section - Draggable Area */}
      <div 
        className="flex-1 h-full flex items-center justify-center cursor-move"
        onMouseDown={handleStartDragging}
      >
        <div className="px-4 py-1 rounded-full bg-white/5 backdrop-blur-sm border border-white/10">
          <span className="text-white/70 text-xs font-medium">Authentication</span>
        </div>
      </div>

      {/* Right Section - Window Controls */}
      <div className="flex items-center gap-1">
        {/* Minimize Button */}
        <Button
          isIconOnly
          size="sm"
          variant="light"
          className="w-8 h-8 min-w-8 text-custom-muted hover:text-white hover:bg-white/10 transition-colors"
          onPress={handleMinimize}
        >
          <Icon icon="lucide:minus" className="w-4 h-4" />
        </Button>

        {/* Maximize/Restore Button */}
        <Button
          isIconOnly
          size="sm"
          variant="light"
          className="w-8 h-8 min-w-8 text-custom-muted hover:text-white hover:bg-white/10 transition-colors"
          onPress={handleMaximize}
        >
          <Icon 
            icon={isMaximized ? "lucide:copy" : "lucide:square"} 
            className="w-4 h-4" 
          />
        </Button>

        {/* Close Button */}
        <Button
          isIconOnly
          size="sm"
          variant="light"
          className="w-8 h-8 min-w-8 text-custom-muted hover:text-white hover:bg-red-500/20 hover:text-red-400 transition-colors"
          onPress={handleClose}
        >
          <Icon icon="lucide:x" className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};
