import React from "react";
import { TimeTracker } from "./time-tracker";
import { WeeklySummary } from "./weekly-summary";
import { WorkHoursReport } from "./work-hours-report";
import { Icon } from "@iconify/react";

export const MainContent = () => {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-white text-2xl font-semibold">Time Tracking</h1>
        <div className="flex items-center">
          <span className="text-white">Today, 14 November</span>
          <Icon icon="lucide:chevron-down" className="text-custom-text ml-2" />
        </div>
      </div>

      <div className="grid grid-cols-12 gap-6">
        {/* Left side - Timer */}
        <div className="col-span-12 md:col-span-5 lg:col-span-4">
          <TimeTracker />
        </div>
        
        {/* Right side - Weekly summary */}
        <div className="col-span-12 md:col-span-7 lg:col-span-8">
          <WeeklySummary />
        </div>
      </div>
      
      {/* Work hours reports */}
      <WorkHoursReport />
    </div>
  );
};