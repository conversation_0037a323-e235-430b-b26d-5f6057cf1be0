import React, { useMemo, useState } from "react";
import { Tooltip } from "@heroui/react";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";


interface ActivityCalendarProps {
  userId: number;
}

// utility helpers
const pad = (n: number) => n.toString().padStart(2, "0");

export const ActivityCalendar: React.FC<ActivityCalendarProps> = ({ userId }) => {
  const timeSlots = Array.from({ length: 12 }, (_, i) => 8 + i); // 8-19

  const year = new Date().getFullYear();

  /**
   * Build a map keyed by YYYY-MM-DD → { intensity, tooltip }
   */
  const yearData = useMemo(() => {
    const map = new Map<string, { intensity: number; tooltip: string }>();

    const to12Hour = (h: number) => {
      const isPM = h >= 12;
      const hr = h % 12 || 12;
      return `${hr}:00 ${isPM ? "PM" : "AM"}`;
    };

    // iterate months
    for (let month = 0; month < 12; month++) {
      const daysInMonth = new Date(year, month + 1, 0).getDate();
      for (let day = 1; day <= daysInMonth; day++) {
        const activities = timeSlots.map((t, idx) => {
          let lvl = (userId + month + day + idx) % 5;
          if (idx < 2 || idx > 9) lvl = Math.max(0, lvl - 1);
          return { hour: t, lvl };
        });

        const active = activities.filter(a => a.lvl > 0);
        let tooltip = "Typically offline";
        if (active.length) {
          const start = active[0].hour;
          const end = active[active.length - 1].hour + 1;
          tooltip = `${to12Hour(start)} - ${to12Hour(end % 24)}`;
        }
        const intensity = Math.max(...activities.map(a => a.lvl));
        const key = `${year}-${pad(month + 1)}-${pad(day)}`;
        map.set(key, { intensity, tooltip });
      }
    }
    return map;
  }, [userId, year]);

  // Map intensity (0-4) to background shades for better visual distinction
  const getColor = (lvl: number) => {
    switch (lvl) {
      case 0:
        return "bg-custom-border";
      case 1:
        return "bg-custom-primary/20";
      case 2:
        return "bg-custom-primary/40";
      case 3:
        return "bg-custom-primary/60";
      case 4:
        return "bg-custom-primary";
      default:
        return "bg-custom-border";
    }
  };

  const [startDate, setStartDate] = useState(new Date(year, new Date().getMonth(), 1));

  // state for selected date to display hourly activities
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  // helper to get hourly activities for a specific date
  const getDayActivities = (date: Date) => {
    const m = date.getMonth();
    const d = date.getDate();
    return timeSlots.map((t, idx) => {
      let lvl = (userId + m + d + idx) % 5;
      if (idx < 2 || idx > 9) lvl = Math.max(0, lvl - 1);
      return { hour: t, lvl };
    });
  };

  const to12HourLabel = (h: number) => {
    const isPM = h >= 12;
    const hr = h % 12 || 12;
    return `${hr}:${"00"} ${isPM ? "PM" : "AM"}`;
  };

  const getActiveRanges = (date: Date) => {
    const activities = getDayActivities(date);
    const ranges: { start: number; end: number }[] = [];
    let currentStart: number | null = null;
    activities.forEach((slot, idx) => {
      if (slot.lvl > 0) {
        if (currentStart === null) currentStart = slot.hour;
      } else {
        if (currentStart !== null) {
          ranges.push({ start: currentStart, end: slot.hour });
          currentStart = null;
        }
      }
      if (idx === activities.length - 1 && currentStart !== null) {
        ranges.push({ start: currentStart, end: slot.hour + 1 });
      }
    });
    return ranges;
  };

  return (
    <div className="flex flex-col md:flex-row gap-4">
      <style>{`
        .react-calendar {
          background: none;
          border: none;
        }
        .react-calendar__tile--now {
          background: none !important;
        }
        .react-calendar__tile:enabled:hover {
          background: #1087ff !important;
  }
      `}</style>
      <Calendar
        showNeighboringMonth={false}
        activeStartDate={startDate}
        onActiveStartDateChange={({ activeStartDate }) => activeStartDate && setStartDate(activeStartDate)}
        view="month"
        calendarType="iso8601"
        onClickDay={(value) => setSelectedDate(value)}
        tileClassName={({ date, view }) => {
          if (view !== "month") return "";
          const key = `${year}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}`;
          const data = yearData.get(key);
          const base = data ? getColor(data.intensity) : "bg-custom-border";
          return ` bg-none text-transparent relative rounded-md ${date.getTime()=== (selectedDate?.getTime()||0) ? 'ring-2 ring-custom-primary' : ''}`;
        }}
        tileContent={({ date, view }) => {
          if (view !== "month") return null;
          const key = `${year}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}`;
          const data = yearData.get(key);
          return (
            <Tooltip
              content={data ? data.tooltip : ""}
              isDisabled={!data}
              classNames={{ base: "bg-custom-sidebar border border-custom-border text-white" }}
            >
              <span className="absolute inset-0 flex items-center justify-center text-[10px] sm:text-xs text-white bg-none pointer-events-none">
                {date.getDate()}
              </span>
            </Tooltip>
          );
        }}
      />

      {selectedDate && (
        <div className="flex-1 md:max-w-xs">
          <div className="bg-custom-sidebar  border-custom-muted p-4 space-y-3">
            <h4 className="text-white font-medium text-sm text-center">
              {selectedDate.toLocaleDateString([], { month: 'long', day: 'numeric', year: 'numeric' })}
            </h4>

            <div className="space-y-1 text-[11px] sm:text-xs">
              <table className="w-full text-custom-text  ">
                <thead>
                  <tr className="mb-4">
                    <th className="text-center text-xs sm:text-sm py-1">Start</th>
                    <th className="text-center text-xs sm:text-sm py-1">End</th>
                  </tr>
                </thead>
                <tbody>
                  {getActiveRanges(selectedDate).length === 0 && (
                    <tr>
                      <td colSpan={2} className="text-custom-muted text-center py-2">No active periods</td>
                    </tr>
                  )}
                  {getActiveRanges(selectedDate).map((r, idx) => (
                    <tr key={idx}>
                      <td className="py-1 text-center text-custom-muted">{to12HourLabel(r.start)}</td>
                      <td className="py-1 text-center text-custom-muted">{to12HourLabel(r.end)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};