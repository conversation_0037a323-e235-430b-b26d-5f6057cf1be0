import React from "react";
import { Card, CardBody, Button, Select, SelectItem, DateRangePicker, Dropdown, DropdownTrigger, 
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  DropdownMenu, DropdownItem, Tooltip, Chip } from "@heroui/react";
import { parseDate } from "@internationalized/date";
import { Icon } from "@iconify/react";

interface TimeEntry {
  id: number;
  duration: string;
  timeDetail: string;
  description: string;
}

export const WorkHoursReport = () => {
  const timeEntries: TimeEntry[] = [
    {
      id: 1,
      duration: "07:20:53",
      timeDetail: "7:05:20 - 14:20:32",
      description: "Lorem ipsum dolor sit amet, cons..."
    },
    {
      id: 2,
      duration: "07:20:53",
      timeDetail: "7:05:20 - 14:20:32",
      description: "Lorem ipsum dolor sit amet, cons..."
    },
    {
      id: 3,
      duration: "07:20:53",
      timeDetail: "7:05:20 - 14:20:32",
      description: "Lorem ipsum dolor sit amet, cons..."
    },
    {
      id: 4,
      duration: "07:20:53",
      timeDetail: "7:05:20 - 14:20:32",
      description: "Lorem ipsum dolor sit amet, cons..."
    }
  ];

  const [selectedProject, setSelectedProject] = React.useState(new Set(["all"]));
  const [dateRange, setDateRange] = React.useState({
    start: parseDate("2023-11-01"),
    end: parseDate("2023-11-14")
  });

  const projects = [
    { id: "all", name: "All Projects" },
    { id: "design", name: "Design System" },
    { id: "timee", name: "Timee App" },
    { id: "clientx", name: "Client X" },
    { id: "clienty", name: "Client Y" }
  ];

  const [sortBy, setSortBy] = React.useState<string>("date");
  const [sortDirection, setSortDirection] = React.useState<"asc" | "desc">("asc");

  const formatDate = (date: Date) =>
    date.toLocaleDateString(undefined, { year: "numeric", month: "short", day: "numeric" });
  const formatTime = (date: Date) =>
    date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  const formatDuration = (duration: string) => {
    const [h, m] = duration.split(":" ).map(Number);
    return `${h}h ${m.toString().padStart(2, "0")}m`;
  };

  const filteredLogs = React.useMemo(() => {
    return timeEntries.map((t) => ({
      id: t.id,
      startTime: new Date(),
      endTime: new Date(),
      project: "—",
      task: t.description,
      duration: t.duration,
      notes: t.description,
      calculatedIncome: 0,
      hourlyRate: 0,
    }));
  }, [timeEntries]);

  const handleExport = () => {
    // Here you would typically handle the export to Excel
    // For now, we'll just show a message
    alert("Exporting data to Excel...");
  };

  return (
    <Card className="bg-custom-card border-custom-border shadow-none mt-8">
      <CardBody className="p-6">
        <div className="flex items-center justify-between mb-4">
        <h2 className="text-white text-xl font-medium">Work hours report</h2>
        <div className="text-custom-text text-sm">Today, 14 November</div>
      </div>
      <div className="space-y-4">
         {/* Table with report data */}
         <Table 
            aria-label="Time logs table"
            removeWrapper
            classNames={{
              base: "mt-6 overflow-x-auto",
              th: "bg-custom-card text-custom-muted border-b border-custom-border",
              td: "border-b border-custom-border py-3"
            }}
          >
            <TableHeader>
              <TableColumn 
                onClick={() => {
                  setSortBy("date");
                  setSortDirection(prev => sortBy === "date" ? (prev === "asc" ? "desc" : "asc") : "desc");
                }}
                className="cursor-pointer"
              >
                <div className="flex items-center gap-1">
                  DATE
                  {sortBy === "date" && (
                    <Icon icon={sortDirection === "asc" ? "lucide:arrow-up" : "lucide:arrow-down"} className="w-3 h-3" />
                  )}
                </div>
              </TableColumn>
              <TableColumn 
                onClick={() => {
                  setSortBy("project");
                  setSortDirection(prev => sortBy === "project" ? (prev === "asc" ? "desc" : "asc") : "asc");
                }}
                className="cursor-pointer"
              >
                <div className="flex items-center gap-1">
                  PROJECT
                  {sortBy === "project" && (
                    <Icon icon={sortDirection === "asc" ? "lucide:arrow-up" : "lucide:arrow-down"} className="w-3 h-3" />
                  )}
                </div>
              </TableColumn>
              <TableColumn 
                onClick={() => {
                  setSortBy("task");
                  setSortDirection(prev => sortBy === "task" ? (prev === "asc" ? "desc" : "asc") : "asc");
                }}
                className="cursor-pointer"
              >
                <div className="flex items-center gap-1">
                  TASK
                  {sortBy === "task" && (
                    <Icon icon={sortDirection === "asc" ? "lucide:arrow-up" : "lucide:arrow-down"} className="w-3 h-3" />
                  )}
                </div>
              </TableColumn>
              <TableColumn 
                onClick={() => {
                  setSortBy("duration");
                  setSortDirection(prev => sortBy === "duration" ? (prev === "asc" ? "desc" : "asc") : "desc");
                }}
                className="cursor-pointer"
              >
                <div className="flex items-center gap-1">
                  TIME LOGGED
                  {sortBy === "duration" && (
                    <Icon icon={sortDirection === "asc" ? "lucide:arrow-up" : "lucide:arrow-down"} className="w-3 h-3" />
                  )}
                </div>
              </TableColumn>
              <TableColumn>NOTES</TableColumn>
              <TableColumn 
                onClick={() => {
                  setSortBy("income");
                  setSortDirection(prev => sortBy === "income" ? (prev === "asc" ? "desc" : "asc") : "desc");
                }}
                className="cursor-pointer"
              >
                <div className="flex items-center gap-1">
                  INCOME
                  {sortBy === "income" && (
                    <Icon icon={sortDirection === "asc" ? "lucide:arrow-up" : "lucide:arrow-down"} className="w-3 h-3" />
                  )}
                </div>
              </TableColumn>

              <TableColumn>ACTIONS</TableColumn>
            </TableHeader>
            <TableBody emptyContent="No time logs found">
              {filteredLogs.map((log) => (
                <TableRow key={log.id}>
                  <TableCell>
                    <div className="flex flex-col">
                      <span className="font-medium text-sm">{formatDate(log.startTime)}</span>
                      <span className="text-xs text-custom-muted">
                        {formatTime(log.startTime)} - {formatTime(log.endTime)}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                      <span className="font-medium">{log.project}</span>
                    
                  </TableCell>
                  <TableCell>
                    <Tooltip content={log.task}>
                      <span className="truncate block max-w-[150px]">{log.task}</span>
                    </Tooltip>
                  </TableCell>
                  <TableCell>
                    <Chip
                      variant="flat" 
                      color="primary"
                      radius="sm"
                      className="bg-primary/10 border-none"
                    >
                      {formatDuration(log.duration)}
                    </Chip>
                  </TableCell>
                  <TableCell>
                    {log.notes ? (
                      <Tooltip content={log.notes}>
                        <span className="truncate block max-w-[120px]">{log.notes}</span>
                      </Tooltip>
                    ) : (
                      <span className="text-custom-muted text-xs">No notes</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">${log.calculatedIncome?.toFixed(2)}</span>
                    <div className="text-xs text-custom-muted">@${log.hourlyRate}/hr</div>
                  </TableCell>

                  <TableCell>
                    <div className="flex items-center gap-1">

                      <Dropdown>
                        <DropdownTrigger>
                          <Button isIconOnly size="sm" variant="light">
                            <Icon icon="lucide:more-vertical" className="text-custom-muted" />
                          </Button>
                        </DropdownTrigger>
                        <DropdownMenu 
                          aria-label="Time log actions"
                          className="bg-custom-card text-custom-text"
                        >
                          <DropdownItem key="edit" startContent={<Icon icon="lucide:edit-2" />}>Edit</DropdownItem>
                          <DropdownItem key="duplicate" startContent={<Icon icon="lucide:copy" />}>Duplicate</DropdownItem>
                          <DropdownItem key="export" startContent={<Icon icon="lucide:download" />}>Export</DropdownItem>
                          <DropdownItem 
                            key="delete" 
                            className="text-danger" 
                            color="danger"
                          >
                            Delete
                          </DropdownItem>
                        </DropdownMenu>
                      </Dropdown>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
      </div>
      </CardBody>
    </Card>
  );
};