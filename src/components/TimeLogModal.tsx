import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
  Select,
  SelectItem,
} from "@heroui/react";
import { TauriIcon as Icon } from "./TauriIcon";
import { invoke } from "@tauri-apps/api/core";

// Types
interface TimeLogData {
  duration: string;
  notes: string;
  taskId: string;
  project?: string;
  timestamp: Date;
  activityId?: number;
}

interface Project {
  id: number;
  name: string;
}

interface Task {
  id: number;
  name: string;
}

export type TimeLogModalType = 'overview' | 'complete';

export interface TimeLogModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: TimeLogModalType;
  timeLog: TimeLogData | null;
  
  // Modal configuration
  isMandatory?: boolean;
  validationError?: string | null;
  
  // Form data
  modalNotes: string;
  modalTaskId: string;
  modalProjectId: string;
  
  // Form handlers
  onNotesChange: (notes: string) => void;
  onTaskChange: (taskId: string) => void;
  onProjectChange: (projectId: string) => void;
  
  // Actions
  onSave: () => Promise<void>;
  
  // Data loading
  projects: Project[];
  tasks: Task[];
  isLoadingProjects?: boolean;
  isLoadingTasks?: boolean;
  onProjectDropdownOpen?: () => void;
  onTaskDropdownOpen?: (projectId: string) => void;
}

const TimeLogModal: React.FC<TimeLogModalProps> = ({
  isOpen,
  onClose,
  type,
  timeLog,
  isMandatory = false,
  validationError,
  modalNotes,
  modalTaskId,
  modalProjectId,
  onNotesChange,
  onTaskChange,
  onProjectChange,
  onSave,
  projects,
  tasks,
  isLoadingProjects = false,
  isLoadingTasks = false,
  onProjectDropdownOpen,
  onTaskDropdownOpen,
}) => {
  const [isSaving, setIsSaving] = useState(false);

  // Handle save with loading state
  const handleSave = async () => {
    console.log('🔄 [MODAL] Starting save process...');
    setIsSaving(true);
    try {
      console.log('🔄 [MODAL] Calling onSave...');
      await onSave();
      console.log('✅ [MODAL] onSave completed successfully');
    } catch (error) {
      console.error('❌ [MODAL] onSave failed:', error);
      throw error; // Re-throw to maintain error handling
    } finally {
      console.log('🔄 [MODAL] Setting isSaving to false');
      setIsSaving(false);
    }
  };

  // Format time range for overview type
  const formatTimeRange = () => {
    if (!timeLog?.timestamp) return "";
    
    const startTime = timeLog.timestamp.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
    
    // For overview, we need end time calculation
    // This is a simplified version - in real app you'd have actual end time
    const endTime = new Date(timeLog.timestamp.getTime() + 3600000).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
    
    return `${startTime} to ${endTime}`;
  };

  // Format date for display
  const formatDate = () => {
    if (!timeLog?.timestamp) return "";
    return timeLog.timestamp.toLocaleDateString([], { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  // Get modal configuration based on type
  const getModalConfig = () => {
    switch (type) {
      case 'complete':
        return {
          title: "Complete Time Log",
          subtitle: isMandatory ? "Please select a project or add notes to complete your time log." : undefined,
          saveButtonText: "Complete Time Log",
          showCancel: !isMandatory,
          isDismissable: !isMandatory,
          hideCloseButton: isMandatory,
        };
      case 'overview':
      default:
        return {
          title: "Time Log Overview",
          subtitle: undefined,
          saveButtonText: "Save Time Log",
          showCancel: true,
          isDismissable: true,
          hideCloseButton: false,
        };
    }
  };

  const config = getModalConfig();

  return (
    <Modal
      isOpen={isOpen}
      onOpenChange={config.isDismissable ? onClose : undefined}
      isDismissable={config.isDismissable}
      hideCloseButton={config.hideCloseButton}
      placement="center"
      classNames={{
        base: 'bg-custom-card border-custom-border z-[9000]',
        header: 'border-b border-custom-border',
        body: 'py-6',
        footer: 'border-t border-custom-border',
        wrapper: 'z-[9000]',
      }}
    >
      <ModalContent>
        {(onModalClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1">
              <h3 className="text-white text-xl">{config.title}</h3>
              {config.subtitle && (
                <p className="text-custom-muted text-sm">{config.subtitle}</p>
              )}
            </ModalHeader>
            
            <ModalBody>
              <div className="space-y-4">
                {/* Validation Error */}
                {validationError && (
                  <div className="bg-red-500/10 border border-red-500/20 rounded-md p-3">
                    <p className="text-red-400 text-sm">{validationError}</p>
                  </div>
                )}

                {/* Duration Display */}
                <div className="bg-custom-sidebar p-3 rounded-md">
                  <p className="text-custom-muted text-sm">Duration</p>
                  <p className="text-white text-lg font-medium">{timeLog?.duration}</p>
                </div>

                {/* Time Range and Date for Overview */}
                {type === 'overview' && (
                  <div className="bg-custom-sidebar p-3 rounded-md space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-custom-muted text-sm">Time Range:</span>
                      <span className="text-white font-semibold">{formatTimeRange()}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-custom-muted text-sm">Date:</span>
                      <span className="text-white">{formatDate()}</span>
                    </div>
                  </div>
                )}

                {/* Project Selection */}
                <Select
                  label="Project"
                  placeholder="Select a project"
                  selectedKeys={modalProjectId ? [modalProjectId] : []}
                  onSelectionChange={(keys) => {
                    const selectedKey = Array.from(keys)[0] as string;
                    onProjectChange(selectedKey || "");
                  }}
                  onOpenChange={(isOpen) => {
                    if (isOpen && onProjectDropdownOpen) {
                      onProjectDropdownOpen();
                    }
                  }}
                  isLoading={isLoadingProjects}
                  classNames={{
                    trigger: 'bg-custom-sidebar border-custom-border',
                    value: 'text-custom-text',
                    label: 'text-custom-muted',
                  }}
                >
                  {projects.map((project) => (
                    <SelectItem key={project.id.toString()} value={project.id.toString()}>
                      {project.name}
                    </SelectItem>
                  ))}
                </Select>

                {/* Task Selection */}
                <Select
                  label="Task"
                  placeholder="Select a task"
                  selectedKeys={modalTaskId ? [modalTaskId] : []}
                  onSelectionChange={(keys) => {
                    const selectedKey = Array.from(keys)[0] as string;
                    onTaskChange(selectedKey || "");
                  }}
                  onOpenChange={(isOpen) => {
                    if (isOpen && onTaskDropdownOpen && modalProjectId) {
                      onTaskDropdownOpen(modalProjectId);
                    }
                  }}
                  isLoading={isLoadingTasks}
                  isDisabled={!modalProjectId}
                  classNames={{
                    trigger: 'bg-custom-sidebar border-custom-border',
                    value: 'text-custom-text',
                    label: 'text-custom-muted',
                  }}
                >
                  {tasks.map((task) => (
                    <SelectItem key={task.id.toString()} value={task.id.toString()}>
                      {task.name}
                    </SelectItem>
                  ))}
                </Select>

                {/* Notes Input */}
                <Input
                  label="Notes"
                  placeholder="What were you working on?"
                  value={modalNotes}
                  onValueChange={onNotesChange}
                  classNames={{
                    inputWrapper: 'bg-custom-sidebar border-custom-border',
                    input: 'text-custom-text',
                    label: 'text-custom-muted',
                  }}
                />
              </div>
            </ModalBody>
            
            <ModalFooter>
              {config.showCancel && (
                <Button variant="bordered" onPress={onModalClose}>
                  Cancel
                </Button>
              )}
              <Button
                color="primary"
                onPress={handleSave}
                isLoading={isSaving}
                className={!config.showCancel ? "w-full" : ""}
              >
                {config.saveButtonText}
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};

export default TimeLogModal;
