import React, { useState, useEffect } from "react";
import { Button, Avatar, Input } from "@heroui/react";
import { TauriIcon as Icon } from "./TauriIcon";
import type { TeamMember } from "../types/TeamMember";
import type { CallWindowData, CallParticipant } from "../types/CallTypes";
import { invoke } from "@tauri-apps/api/core";
import { getCurrentWindow } from '@tauri-apps/api/window';

type CallStatus = "ringing" | "connected" | "rejected";

export const CallDesktop: React.FC = () => {
  const [callData, setCallData] = useState<CallWindowData | null>(null);
  const [callStatus, setCallStatus] = useState<CallStatus>("ringing");
  const [participants, setParticipants] = useState<CallParticipant[]>([]);
  const [isMuted, setIsMuted] = useState(false);
  const [isSpeakerOn, setIsSpeakerOn] = useState(true);
  const [showAddUser, setShowAddUser] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [callDuration, setCallDuration] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [appWindow, setAppWindow] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Team members excluding current participants (mock data for now)
  const teamMembers: TeamMember[] = [
    { 
      id: 2, 
      name: "Muhammad", 
      role: "Product Owner", 
      avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=2", 
      isOnline: true 
    },
    { 
      id: 3, 
      name: "Ali Sina", 
      role: "Front end developer", 
      avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=3" 
    },
    { 
      id: 4, 
      name: "Sajjad Ghorbanioo", 
      role: "Front end developer", 
      avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=4", 
      isOnline: true 
    },
    { 
      id: 5, 
      name: "Ali Gopal Pour", 
      role: "HR", 
      avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=5" 
    },
    { 
      id: 6, 
      name: "Mr. Azimi", 
      role: "Co-founder", 
      avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=6" 
    }
  ];

  const filteredUsers = teamMembers.filter(
    member => 
      !participants.some(p => p.user.id === member.id) && 
      callData && member.id !== callData.callee.id &&
      member.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Initialize window and get call data
  useEffect(() => {
    const initWindow = async () => {
      try {
        console.log('📞 [CALL_DESKTOP] Initializing call window...');
        setIsLoading(true);

        // Initialize Tauri window
        try {
          const window = getCurrentWindow();
          setAppWindow(window);
        } catch (error) {
          console.error('Failed to get current window:', error);
        }

        // Wait for DOM to be fully ready
        await new Promise(resolve => {
          if (document.readyState === 'complete') {
            resolve(null);
          } else {
            window.addEventListener('load', () => resolve(null), { once: true });
          }
        });

        // Initialize Tauri window controls
        try {
          if (typeof window !== 'undefined' && (window as any).__TAURI__) {
            console.log('📞 [CALL_DESKTOP] Tauri Available');
            
            // Try multiple times to get callData (sometimes it takes time to inject)
            let windowCallData = null;
            let attempts = 0;
            const maxAttempts = 10;
            
            while (!windowCallData && attempts < maxAttempts) {
              await new Promise(resolve => setTimeout(resolve, 100));
              windowCallData = (window as any).callData;
              attempts++;
              console.log(`📞 [CALL_DESKTOP] Attempt ${attempts}: callData =`, windowCallData);
            }
            
            console.log('📞 [CALL_DESKTOP] Final callData after', attempts, 'attempts:', windowCallData);
            
            if (windowCallData) {
              // Transform data if needed
              const transformedData: CallWindowData = {
                caller: {
                  id: windowCallData.caller?.id || 0,
                  name: windowCallData.caller?.name || windowCallData.caller?.fullName || 'Unknown',
                  fullName: windowCallData.caller?.fullName || windowCallData.caller?.name,
                  role: windowCallData.caller?.role || 'Member',
                  avatar: windowCallData.caller?.avatar || windowCallData.caller?.avata || 'https://img.heroui.chat/image/avatar?w=200&h=200&u=1',
                  isOnline: windowCallData.caller?.isOnline || true
                },
                callee: {
                  id: windowCallData.callee?.id || 1,
                  name: windowCallData.callee?.name || windowCallData.callee?.fullName || 'Unknown',
                  fullName: windowCallData.callee?.fullName || windowCallData.callee?.name,
                  role: windowCallData.callee?.role || 'Member',
                  avatar: windowCallData.callee?.avatar || windowCallData.callee?.avata || 'https://img.heroui.chat/image/avatar?w=200&h=200&u=2',
                  isOnline: windowCallData.callee?.isOnline || true
                },
                timestamp: windowCallData.timestamp || Date.now(),
                source: 'team-sidebar'
              };
              
              console.log('📞 [CALL_DESKTOP] Transformed call data:', transformedData);
              setCallData(transformedData);
            } else {
              console.error('❌ [CALL_DESKTOP] No call data found in window');
              // For development, create mock data
              const mockCallData: CallWindowData = {
                caller: {
                  id: 1,
                  name: "Current User",
                  role: "Developer",
                  avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=1",
                  isOnline: true
                },
                callee: {
                  id: 2,
                  name: "Test User",
                  role: "Designer",
                  avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=2",
                  isOnline: true
                },
                timestamp: Date.now(),
                source: 'team-sidebar'
              };
              setCallData(mockCallData);
            }
          } else {
            console.error('❌ [CALL_DESKTOP] Tauri not available');
            // Still provide mock data for development
            const mockCallData: CallWindowData = {
              caller: {
                id: 1,
                name: "Current User",
                role: "Developer",
                avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=1",
                isOnline: true
              },
              callee: {
                id: 2,
                name: "Test User",
                role: "Designer",
                avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=2",
                isOnline: true
              },
              timestamp: Date.now(),
              source: 'team-sidebar'
            };
            setCallData(mockCallData);
          }
        } catch (error) {
          console.error('❌ [CALL_DESKTOP] Failed to initialize Tauri:', error);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('❌ [CALL_DESKTOP] Failed to initialize call window:', error);
        setError(error instanceof Error ? error.message : 'Failed to initialize call window');
        setIsLoading(false);
      }
    };

    initWindow();
  }, []);

  // Simulate call answer after 5 seconds
  useEffect(() => {
    if (callData && callStatus === "ringing") {
      const timer = setTimeout(() => {
        setCallStatus("connected");
        setParticipants([
          { user: callData.caller, status: "connected" },
          { user: callData.callee, status: "connected" }
        ]);
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [callData, callStatus]);

  // Handle call duration timer
  useEffect(() => {
    let interval: number;
    
    if (callStatus === "connected") {
      interval = window.setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
    }
    
    return () => {
      if (interval) window.clearInterval(interval);
    };
  }, [callStatus]);

  // Format call duration
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  // Handle user selection to add to call
  const handleUserSelect = (userId: number) => {
    const selectedUser = teamMembers.find(u => u.id === userId);
    if (selectedUser) {
      // Add user with "ringing" status
      setParticipants([...participants, { user: selectedUser, status: "ringing" }]);
      setShowAddUser(false);
      setSearchQuery("");
      
      // Simulate user accepting or rejecting the call
      setTimeout(() => {
        setParticipants(prev => 
          prev.map(p => 
            p.user.id === userId 
              ? { ...p, status: Math.random() > 0.3 ? "connected" : "rejected" } 
              : p
          )
        );
      }, 4000);
    }
  };

  // Handle close call
  const handleClose = async () => {
    try {
      await invoke('close_call_window');
    } catch (error) {
      console.error('❌ [CALL_DESKTOP] Failed to close call window:', error);
    }
  };

  // Handle minimize window
  const handleMinimize = async () => {
    try {
      if (appWindow) {
        await appWindow.minimize();
      }
    } catch (error) {
      console.error('Failed to minimize window:', error);
    }
  };

  // Handle drag start
  const handleDragStart = async () => {
    try {
      if (appWindow && typeof appWindow.startDragging === 'function') {
        await appWindow.startDragging();
      }
    } catch (error) {
      console.warn('⚠️ [CALL_DESKTOP] Drag failed:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="h-[350px] w-[400px] bg-gradient-to-br from-[#1A1D2B] to-[#16213E] flex items-center justify-center">
        <div className="text-white text-sm">Loading Call...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-[350px] w-[400px] bg-gradient-to-br from-[#1A1D2B] to-[#16213E] flex flex-col items-center justify-center p-4">
        <div className="text-red-400 text-sm mb-2">Error loading call</div>
        <div className="text-white text-xs text-center">{error}</div>
      </div>
    );
  }

  if (!callData) {
    return (
      <div className="h-[350px] w-[400px] bg-gradient-to-br from-[#1A1D2B] to-[#16213E] flex items-center justify-center">
        <div className="text-white text-sm">No call data available</div>
      </div>
    );
  }

  return (
    <div className="h-[350px] w-[400px] bg-gradient-to-br from-[#1A1D2B] to-[#16213E] flex flex-col overflow-hidden">
      {/* Top Bar */}
      <div
        className="flex items-center justify-between h-8 bg-[#1A1D2B] border-b border-[#2A2D3C] px-3 select-none cursor-move"
        onMouseDown={handleDragStart}
      >
        {/* Title */}
        <div className="flex items-center">
          <Icon icon="lucide:phone" className="text-[#1B84FF] text-sm mr-2" />
          <span className="text-white text-xs font-medium">TeamBy - Call</span>
        </div>

        {/* Window Controls */}
        <div 
          className="flex items-center space-x-1"
          onMouseDown={(e) => e.stopPropagation()}
        >
          <Button
            isIconOnly
            size="sm"
            variant="light"
            className="text-[#7D8597] hover:text-white hover:bg-[#2A2D3C]/50 w-6 h-6 min-w-6"
            onPress={handleMinimize}
          >
            <Icon icon="lucide:minus" className="text-xs" />
          </Button>
          
          <Button
            isIconOnly
            size="sm"
            variant="light"
            className="text-[#7D8597] hover:text-white hover:bg-red-500/20 w-6 h-6 min-w-6"
            onPress={handleClose}
          >
            <Icon icon="lucide:x" className="text-xs" />
          </Button>
        </div>
      </div>

      {/* Call Content */}
      <div className="flex-1 h-[318px] border border-[#2A2D3C] border-t-0">{callStatus === "ringing" ? (
        // Ringing state
        <div className="p-8 h-full">
          <div className="flex flex-col items-center h-full">
            <h4 className="text-white text-sm font-medium mb-4">Calling...</h4>
            
            <div className="flex justify-center flex-grow items-center mb-4 relative flex-col">
              <div className="text-[#C0C4CC] text-sm mt-2 mb-4 flex justify-center items-center">
                {/* Caller avatar */}
                <Avatar
                  src={callData.caller.avatar}
                  className="h-12 w-12 absolute -left-2"
                />
                
                {/* Callee avatar */}
                <Avatar
                  src={callData.callee.avatar}
                  className="h-12 w-12 absolute left-12"
                />
              </div>
              <div className="text-[#C0C4CC] text-sm mt-6 mb-6">
                Calling {callData.callee.name}...
              </div>
            </div>
            
            {/* Cancel button */}
            <Button
              color="danger"
              variant="flat"
              size="sm"
              className="w-full"
              startContent={<Icon icon="lucide:phone-off" className="text-sm" />}
              onPress={handleClose}
            >
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        // Connected state - will be implemented in next part
        <div className="p-4 h-full">
          <div className="flex flex-col h-full">
            <div className="flex justify-between items-center mb-6">
              <h4 className="text-white text-sm font-medium">On Call</h4>
              <span className="text-[#1B84FF] text-sm">{formatTime(callDuration)}</span>
            </div>
            
            {/* Participants */}
            <div className="mb-4 flex-grow overflow-y-auto">
              {/* Initial participants */}
              <div className="flex items-center mb-2">
                <Avatar src={callData.callee.avatar} className="h-8 w-8 mr-2" />
                <div className="flex-1">
                  <p className="text-white text-xs">{callData.callee.name}</p>
                  <p className="text-[#7D8597] text-xs">{callData.callee.role}</p>
                </div>
                <div className="w-2 h-2 rounded-full bg-green-500 mr-1"></div>
              </div>
              
              {/* Additional participants */}
              {participants.map((participant) => (
                participant.user.id !== callData.caller.id && participant.user.id !== callData.callee.id && (
                  <div key={participant.user.id} className="flex items-center mb-2">
                    <Avatar src={participant.user.avatar} className="h-8 w-8 mr-2" />
                    <div className="flex-1">
                      <p className="text-white text-xs">{participant.user.name}</p>
                      <p className="text-[#7D8597] text-xs">
                        {participant.status === "ringing" 
                          ? "Ringing..." 
                          : participant.status === "rejected"
                          ? "Declined"
                          : participant.user.role}
                      </p>
                    </div>
                    <div 
                      className={`w-2 h-2 rounded-full mr-1 
                        ${participant.status === "connected" 
                          ? "bg-green-500" 
                          : participant.status === "ringing"
                          ? "bg-yellow-500"
                          : "bg-red-500"}`}
                    ></div>
                  </div>
                )
              ))}
            </div>
            
            {/* Add user section */}
            {showAddUser && (
              <div className="mb-4">
                {searchQuery && filteredUsers.length > 0 && (
                  <div className="mb-2 bg-[#12141F] border border-[#2A2D3C] rounded-lg max-h-[120px] overflow-y-auto">
                    {filteredUsers.map(user => (
                      <div 
                        key={user.id}
                        className="flex items-center justify-between p-2 hover:bg-[#2A2D3C]/30 cursor-pointer"
                        onClick={() => handleUserSelect(user.id)}
                      >
                        <div className="flex items-center">
                          <Avatar src={user.avatar} size="sm" className="mr-2" />
                          <span className="text-white text-xs">{user.name}</span>
                        </div>
                        <Button 
                          isIconOnly 
                          size="sm" 
                          variant="flat" 
                          className="text-[#7D8597]"
                          onPress={() => handleUserSelect(user.id)}
                        >
                          <Icon icon="lucide:plus" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
                <Input
                  placeholder="Search users..."
                  value={searchQuery}
                  onValueChange={setSearchQuery}
                  variant="flat"
                  size="sm"
                  startContent={<Icon icon="lucide:search" className="text-[#7D8597]" />}
                  classNames={{
                    inputWrapper: "bg-[#12141F] border-[#2A2D3C]"
                  }}
                />
              </div>
            )}
            
            {/* Call controls */}
            <div className="flex justify-between items-center">
              <Button
                isIconOnly
                variant="flat"
                size="lg"
                className="text-white"
              >
                <Icon icon="lucide:expand" className="text-lg" />
              </Button>
              
              <Button
                isIconOnly
                variant="flat"
                size="lg"
                className={isMuted ? "bg-[#2A2D3C]/50 text-[#7D8597]" : "text-white"}
                onPress={() => setIsMuted(!isMuted)}
              >
                <Icon icon={isMuted ? "lucide:mic-off" : "lucide:mic"} className="text-lg" />
              </Button>
              
              <Button
                isIconOnly
                variant="flat"
                size="lg"
                className={isSpeakerOn ? "text-white" : "bg-[#2A2D3C]/50 text-[#7D8597]"}
                onPress={() => setIsSpeakerOn(!isSpeakerOn)}
              >
                <Icon icon={isSpeakerOn ? "lucide:volume-2" : "lucide:volume-x"} className="text-lg" />
              </Button>
              
              <Button
                isIconOnly
                variant="flat"
                color={showAddUser ? "primary" : "default"}
                size="lg"
                onPress={() => setShowAddUser(!showAddUser)}
              >
                <Icon icon="lucide:user-plus" className="text-lg" />
              </Button>
              
              <Button
                isIconOnly
                color="danger"
                variant="flat"
                size="lg"
                onPress={handleClose}
              >
                <Icon icon="lucide:phone-off" className="text-lg" />
              </Button>
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
};
