import React from 'react';
import { motion } from 'framer-motion';

interface ProgressRingProps {
  size?: number;
  strokeWidth?: number;
  progress?: number;
  isLoading?: boolean;
  children?: React.ReactNode;
  className?: string;
  color?: string;
  backgroundColor?: string;
}

export const ProgressRing: React.FC<ProgressRingProps> = ({
  size = 80,
  strokeWidth = 3,
  progress = 0,
  isLoading = false,
  children,
  className = '',
  color = '#3b82f6',
  backgroundColor = '#e5e7eb'
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <div className={`relative inline-flex items-center justify-center ${className}`}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="transparent"
          className="opacity-20"
        />
        
        {/* Progress circle */}
        {!isLoading && (
          <motion.circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={color}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeLinecap="round"
            strokeDasharray={strokeDasharray}
            initial={{ strokeDashoffset: circumference }}
            animate={{
              strokeDashoffset: strokeDashoffset,
            }}
            transition={{
              duration: 0.5,
              ease: "easeOut",
            }}
          />
        )}

        {/* Loading animation circle - one complete rotation */}
        {isLoading && (
          <motion.circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={color}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeLinecap="round"
            strokeDasharray={strokeDasharray}
            initial={{ strokeDashoffset: circumference }}
            animate={{
              strokeDashoffset: 0,
            }}
            transition={{
              duration: 1.0,
              ease: "easeInOut",
            }}
            style={{
              filter: 'drop-shadow(0 0 8px currentColor)',
            }}
          />
        )}

      </svg>
      
      {/* Content in center */}
      <div className="absolute inset-0 flex items-center justify-center">
        {children}
      </div>
    </div>
  );
};

// Preset configurations for common use cases
export const LoadingRing: React.FC<{ size?: number; children?: React.ReactNode }> = ({ 
  size = 80, 
  children 
}) => (
  <ProgressRing
    size={size}
    isLoading={true}
    color="#3b82f6"
    backgroundColor="#e5e7eb"
  >
    {children}
  </ProgressRing>
);

export const SuccessRing: React.FC<{ size?: number; children?: React.ReactNode }> = ({ 
  size = 80, 
  children 
}) => (
  <ProgressRing
    size={size}
    progress={100}
    color="#10b981"
    backgroundColor="#e5e7eb"
  >
    {children}
  </ProgressRing>
);

export const ErrorRing: React.FC<{ size?: number; children?: React.ReactNode }> = ({ 
  size = 80, 
  children 
}) => (
  <ProgressRing
    size={size}
    progress={100}
    color="#ef4444"
    backgroundColor="#e5e7eb"
  >
    {children}
  </ProgressRing>
);
