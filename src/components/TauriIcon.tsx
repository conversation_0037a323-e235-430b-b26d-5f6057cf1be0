import React from 'react';
import { LocalIcon } from './LocalIcons';

interface TauriIconProps {
  icon: string;
  className?: string;
  style?: React.CSSProperties;
}

export const TauriIcon: React.FC<TauriIconProps> = ({ icon, className, style }) => {
  // Use local SVG icons for Tauri compatibility
  return (
    <LocalIcon
      icon={icon}
      className={className}
      style={style}
    />
  );
};

// Export as default for easy replacement
export default TauriIcon;
