import React from "react";
import { Icon } from "@iconify/react";
import { Divider } from "@heroui/react";
import { motion, AnimatePresence } from "framer-motion";
import { ChatSidebar } from "../apps/chat/components/ChatSidebar";
import type { ChatConversation } from "../apps/chat/types";
import { MeetSidebar } from "../apps/meet/components/MeetSidebar";
import type { Call } from "../apps/meet/types";
import { useNavigate } from "react-router-dom";

// Structure for parent apps and their submenus
const parentApps = [
  {
    id: "time",
    label: "Time Tracking",
    icon: "lucide:clock",
    sections: [
      {
        title: "Main Menu",
        items: [
          { id: "home", label: "Home", icon: "lucide:home", path: "/time-tracking" },
          { id: "time", label: "Time", icon: "lucide:timer" },
          { id: "reports", label: "Reports", icon: "lucide:bar-chart-2", path: "/time-tracking/reports" },
          { id: "ai-reports", label: "AI Reports", icon: "lucide:sparkles", path: "/time-tracking/ai-reports" },
        ],
      },
      {
        title: "Management",
        items: [
          { id: "projects", label: "Projects", icon: "lucide:folder" },
          { id: "tasks", label: "Tasks", icon: "lucide:check-circle" },
          { id: "calendar", label: "Calendar", icon: "lucide:calendar" },
        ],
      },
    ],
  },
  {
    id: "chat",
    label: "Chat App",
    icon: "lucide:message-circle",
    sections: [
      {
        title: "Messages",
        items: [
          { id: "chats", label: "Chats", icon: "lucide:message-square" },
          { id: "contacts", label: "Contacts", icon: "lucide:users" },
          { id: "channels", label: "Channels", icon: "lucide:hash" },
        ],
      },
      {
        title: "Settings",
        items: [
          { id: "notifications", label: "Notifications", icon: "lucide:bell" },
          { id: "appearance", label: "Appearance", icon: "lucide:palette" },
        ],
      },
    ],
  },
  {
    id: "project",
    label: "Project Management",
    icon: "lucide:layout-kanban",
    sections: [
      {
        title: "Workspaces",
        items: [
          { id: "projects", label: "Projects", icon: "lucide:folder", path: "/project/projects" },
          { id: "boards", label: "Boards", icon: "lucide:trello", path: "/project/boards" },
          { id: "backlog", label: "Backlog", icon: "lucide:list", path: "/project/backlog" },
        ],
      },
      {
        title: "Tools",
        items: [
          { id: "gantt", label: "Gantt Chart", icon: "lucide:bar-chart", path: "/project/gantt" },
          { id: "analytics", label: "Analytics", icon: "lucide:pie-chart" },
        ],
      },
    ],
  },
  {
    id: "file",
    label: "File Manager",
    icon: "lucide:file",
    sections: [
      {
        title: "Files",
        items: [
          { id: "my-files", label: "My Files", icon: "lucide:file-text" },
          { id: "shared", label: "Shared", icon: "lucide:share-2" },
          { id: "recent", label: "Recent", icon: "lucide:clock" },
          {
            id: "categories",
            label: "Categories",
            icon: "lucide:folder",
            hasChildren: true,
            children: [
              { id: "documents", label: "Documents", icon: "lucide:file-text" },
              { id: "images", label: "Images", icon: "lucide:image" },
              { id: "videos", label: "Videos", icon: "lucide:film" },
            ],
          },
        ],
      },
      {
        title: "Storage",
        items: [
          { id: "cloud", label: "Cloud Storage", icon: "lucide:cloud" },
          { id: "trash", label: "Trash", icon: "lucide:trash" },
        ],
      },
    ],
  },
  {
    id: "requests",
    label: "Organizational Requests",
    icon: "lucide:file-text",
    sections: [
      {
        title: "Requests",
        items: [
          { id: "my-requests", label: "My Requests", icon: "lucide:list" },
          { id: "new-request", label: "New Request", icon: "lucide:plus" },
        ],
      },
      {
        title: "Request Types",
        items: [
          { id: "leave", label: "Leave Requests", icon: "lucide:calendar" },
          { id: "loan", label: "Loan Requests", icon: "lucide:credit-card" },
          { id: "overtime", label: "Overtime Requests", icon: "lucide:clock" },
          { id: "other", label: "Other Requests", icon: "lucide:file-plus" },
        ],
      },
    ],
  },
  {
    id: "pulse",
    label: "PulseBoard",
    icon: "lucide:zap",
    sections: [
      {
        title: "Main Menu",
        items: [
          { id: "dashboard", label: "Dashboard", icon: "lucide:layout-dashboard", path: "/pulse-board/dashboard" },
          { id: "challenges", label: "Challenges History", icon: "lucide:trophy", path: "/pulse-board/challenges" },
          { id: "kudos", label: "Kudos Tracker", icon: "lucide:heart", path: "/pulse-board/kudos" },
          { id: "recommendations", label: "Peer Recommendations", icon: "lucide:message-square-quote", path: "/pulse-board/recommendations" },
        ],
      },
      {
        title: "Settings",
        items: [
          { id: "profile", label: "My Profile", icon: "lucide:user" },
          { id: "preferences", label: "Preferences", icon: "lucide:settings" },
        ],
      },
    ],
  },
  {
    id: "meet",
    label: "Meet App",
    icon: "lucide:video",
    sections: [
      {
        title: "Meetings",
        items: [
          { id: "all-calls", label: "All Calls", icon: "lucide:list" },
          { id: "scheduled", label: "Scheduled", icon: "lucide:calendar" },
          { id: "recordings", label: "Recordings", icon: "lucide:film" },
        ],
      },
      {
        title: "Settings",
        items: [
          { id: "meet-preferences", label: "Preferences", icon: "lucide:settings" },
          { id: "devices", label: "Audio & Video", icon: "lucide:headphones" },
        ],
      },
    ],
  },
  {
    id: "livekit",
    label: "LiveKit Test",
    icon: "lucide:video",
    sections: [
      {
        title: "Test",
        items: [
          { id: "connection", label: "Connection Test", icon: "lucide:wifi", path: "/livekit" },
        ],
      },
    ],
  },
];

interface ExpandedSidebarProps {
  /** Which parent app's navigation to display (may come from hover) */
  activeApp: string;
  /** The currently selected app in the whole UI */
  globalActiveApp: string;
  chatConversation?: ChatConversation | null;
  onSelectChatConversation?: (conv: ChatConversation) => void;
  chatSearchQuery?: string;
  onChatSearchChange?: (query: string) => void;
  meetCall?: Call | null;
  onSelectMeetCall?: (call: Call) => void;
}

export const ExpandedSidebar: React.FC<ExpandedSidebarProps> = ({ 
  activeApp,
  globalActiveApp,
  chatConversation,
  onSelectChatConversation,
  chatSearchQuery = "",
  onChatSearchChange = () => {},
  meetCall,
  onSelectMeetCall
}) => {
  // Add state for expanded items
  const [expandedItems, setExpandedItems] = React.useState<Record<string, boolean>>({});
  
  // Add toggle function for expanding/collapsing items
  const navigate = useNavigate();
  
  const toggleExpandItem = (itemId: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };
  
  // Render ChatSidebar only when chat is the active app in main layout (not just hover)
  if (activeApp === "chat" && globalActiveApp === "chat") {
    return (
      <aside className="w-[320px] bg-custom-sidebar flex flex-col h-screen border-r border-custom-border">
        <div className="p-4 flex items-center">
          <Icon icon="lucide:message-circle" className="text-primary text-xl" />
          <span className="text-white text-lg font-medium ml-3">Chats</span>
        </div>
        <div className="flex-1 overflow-hidden">
          <ChatSidebar 
            activeConversationId={chatConversation?.id}
            onSelectConversation={(conv) => onSelectChatConversation && onSelectChatConversation(conv)}
            searchQuery={chatSearchQuery}
            onSearchChange={onChatSearchChange}
          />
        </div>
      </aside>
    );
  }

  // Render MeetSidebar only when meet is the active app in main layout (not just hover)
  if (activeApp === "meet" && globalActiveApp === "meet") {
    return (
      <aside className="w-[320px] bg-custom-sidebar flex flex-col h-screen border-r border-custom-border">
        <div className="p-4 flex items-center">
          <Icon icon="lucide:video" className="text-primary text-xl" />
          <span className="text-white text-lg font-medium ml-3">Meet</span>
        </div>
        <div className="flex-1 overflow-hidden">
          <MeetSidebar
            selectedCallId={meetCall?.id}
            onSelectCall={(call) => onSelectMeetCall && onSelectMeetCall(call)}
          />
        </div>
      </aside>
    );
  }

  // Find the active app data from parentApps
  const activeAppData = parentApps.find(app => app.id === activeApp) || parentApps[0];

  return (
    <aside className="w-[240px] bg-custom-sidebar flex flex-col h-screen border-r border-custom-border">
      <div className="p-4 flex items-center">
        <Icon icon={activeAppData.icon} className="text-primary text-xl" />
        <span className="text-white text-lg font-medium ml-3">{activeAppData.label}</span>
      </div>
      
      <div className="overflow-y-auto flex-grow custom-scrollbar px-2">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeApp}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.2 }}
            className="h-full"
          >
            {activeAppData.sections.map((section, idx) => (
              <div key={`${activeApp}-section-${idx}`} className="mb-6">
                <p className="text-custom-muted text-xs font-semibold uppercase px-5 py-2">
                  {section.title}
                </p>
                
                <div className="space-y-1 px-3">
                  {section.items.map((item) => (
                    <React.Fragment key={item.id}>
                      <div 
                        className="flex items-center px-3 py-2 rounded-md cursor-pointer hover:bg-custom-card"
                        onClick={() => {
                          if (item.hasChildren) {
                            toggleExpandItem(item.id);
                          } else if (item.path) {
                            navigate(item.path);
                          } else if (item.onClick) {
                            item.onClick();
                          }
                        }}
                      >
                        <Icon 
                          icon={item.icon} 
                          className="text-custom-text text-base" 
                        />
                        <span className="text-custom-text text-sm ml-3">
                          {item.label}
                        </span>
                        {item.hasChildren && (
                          <Icon 
                            icon={expandedItems[item.id] ? "lucide:chevron-up" : "lucide:chevron-down"} 
                            className="text-custom-muted text-sm ml-auto"
                          />
                        )}
                      </div>
                      
                      {/* Nested submenu */}
                      {item.hasChildren && item.children && (
                        <AnimatePresence>
                          {expandedItems[item.id] && (
                            <motion.div
                              initial={{ height: 0, opacity: 0 }}
                              animate={{ height: "auto", opacity: 1 }}
                              exit={{ height: 0, opacity: 0 }}
                              transition={{ duration: 0.2 }}
                              className="overflow-hidden pl-6"
                            >
                              {item.children.map((child) => (
                                <div
                                  key={child.id}
                                  className="flex items-center px-3 py-2 rounded-md cursor-pointer hover:bg-custom-card"
                                >
                                  <Icon
                                    icon={child.icon}
                                    className="text-custom-text text-sm"
                                  />
                                  <span className="text-custom-text text-xs ml-3">
                                    {child.label}
                                  </span>
                                </div>
                              ))}
                            </motion.div>
                          )}
                        </AnimatePresence>
                      )}
                    </React.Fragment>
                  ))}
                </div>
                
                {idx < activeAppData.sections.length - 1 && (
                  <Divider className="my-3 mx-2" />
                )}
              </div>
            ))}
          </motion.div>
        </AnimatePresence>
      </div>
    </aside>
  );
};