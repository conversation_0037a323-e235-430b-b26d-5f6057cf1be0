import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, act } from '../../test/test-utils';
import { TopBar } from '../topbar';

// Mock Tauri APIs
const mockWindow = {
  minimize: vi.fn(),
  maximize: vi.fn(),
  unmaximize: vi.fn(),
  toggleMaximize: vi.fn(),
  close: vi.fn(),
  isMaximized: vi.fn(),
};

vi.mock('@tauri-apps/api/window', () => ({
  getCurrentWindow: () => mockWindow,
}));

// Mock TimeTrackingContext
const mockTimeTrackingContext = {
  userStatus: 'available' as const,
  setUserStatus: vi.fn(),
  isTracking: false,
  toggleTracking: vi.fn(),
  currentTimeEntry: null,
  setCurrentTimeEntry: vi.fn(),
  showOverviewModal: false,
  setShowOverviewModal: vi.fn(),
  timeLogs: [],
  addTimeLog: vi.fn(),
  updateTimeLog: vi.fn(),
};

vi.mock('../../context/TimeTrackingContext', () => ({
  useTimeTracking: () => mockTimeTrackingContext,
  TimeTrackingProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock UserContext
const mockUserContext = {
  user: {
    full_name: 'John Doe',
    email: '<EMAIL>',
    avatar: null,
    position: 'Developer',
    company: 'Test Company',
    is_admin: false,
    screen_active: false,
  },
  isLoading: false,
  error: null,
  fetchUserProfile: vi.fn(),
  refreshUserProfile: vi.fn(),
  clearUser: vi.fn(),
  toggleScreenActive: vi.fn(),
  updateScreenActive: vi.fn(),
};

vi.mock('../../context/UserContext', () => ({
  useUser: () => mockUserContext,
  UserProvider: ({ children }: { children: React.ReactNode }) => children,
}));

describe('TopBar Component', () => {
  const defaultProps = {
    showEmployeeList: false,
    toggleEmployeeList: vi.fn(),
    activeApp: 'time',
    onMinimize: vi.fn(),
    onMaximize: vi.fn(),
    onClose: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockWindow.isMaximized.mockResolvedValue(false);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Modern Window Controls Layout', () => {
    it('should render window controls in top-right corner', async () => {
      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      const windowControls = screen.getByTestId('window-controls');
      expect(windowControls).toBeInTheDocument();

      // Check all three buttons are present
      expect(screen.getByTestId('minimize-button')).toBeInTheDocument();
      expect(screen.getByTestId('maximize-button')).toBeInTheDocument();
      expect(screen.getByTestId('close-button')).toBeInTheDocument();
    });

    it('should have compact modern button styling', async () => {
      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      const minimizeButton = screen.getByTestId('minimize-button');
      const maximizeButton = screen.getByTestId('maximize-button');
      const closeButton = screen.getByTestId('close-button');

      // Check compact size
      expect(minimizeButton).toHaveClass('w-6', 'h-6', 'min-w-6');
      expect(maximizeButton).toHaveClass('w-6', 'h-6', 'min-w-6');
      expect(closeButton).toHaveClass('w-6', 'h-6', 'min-w-6');

      // Check modern styling
      expect(minimizeButton).toHaveClass('rounded-full');
      expect(maximizeButton).toHaveClass('rounded-full');
      expect(closeButton).toHaveClass('rounded-full');
    });

    it('should render action buttons below window controls', async () => {
      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      const actionButtons = screen.getByTestId('action-buttons');
      expect(actionButtons).toBeInTheDocument();

      // Check that both action buttons are present
      expect(screen.getByTitle('View options')).toBeInTheDocument();
      expect(screen.getByTitle('Toggle employee list')).toBeInTheDocument();
    });
  });

  describe('Window Control Functionality', () => {
    it('should handle minimize button click', async () => {
      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      const minimizeButton = screen.getByTestId('minimize-button');

      await act(async () => {
        fireEvent.click(minimizeButton);
      });

      await waitFor(() => {
        expect(mockWindow.minimize).toHaveBeenCalledTimes(1);
        expect(defaultProps.onMinimize).toHaveBeenCalledTimes(1);
      });
    });

    it('should handle maximize/restore button click', async () => {
      mockWindow.isMaximized.mockResolvedValue(false);

      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      const maximizeButton = screen.getByTestId('maximize-button');

      await act(async () => {
        fireEvent.click(maximizeButton);
      });

      await waitFor(() => {
        expect(mockWindow.toggleMaximize).toHaveBeenCalledTimes(1);
        expect(defaultProps.onMaximize).toHaveBeenCalledTimes(1);
      });
    });

    it('should handle close button click', async () => {
      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      const closeButton = screen.getByTestId('close-button');

      await act(async () => {
        fireEvent.click(closeButton);
      });

      await waitFor(() => {
        expect(mockWindow.close).toHaveBeenCalledTimes(1);
        expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
      });
    });

    it('should show correct maximize/restore icon based on window state', async () => {
      // Test maximized state
      mockWindow.isMaximized.mockResolvedValue(true);

      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      await waitFor(() => {
        const maximizeButton = screen.getByTestId('maximize-button');
        expect(maximizeButton).toHaveAttribute('title', 'Restore');
      });
    });
  });

  describe('Layout and Drag Regions', () => {
    it('should have proper drag region layout', async () => {
      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      const centerDragRegion = screen.getByTestId('center-drag-region');
      expect(centerDragRegion).toBeInTheDocument();
      expect(centerDragRegion).toHaveClass('flex-1', 'cursor-move');
      expect(centerDragRegion).toHaveAttribute('data-tauri-drag-region');
    });

    it('should have proper spacing for window controls', async () => {
      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      const centerDragRegion = screen.getByTestId('center-drag-region');
      expect(centerDragRegion).toHaveClass('pr-32'); // Right padding to avoid window controls
    });
  });

  describe('User Interface Elements', () => {
    it('should render user info section', async () => {
      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      expect(screen.getByText('Mirhafez')).toBeInTheDocument();
      expect(screen.getByText('Online & Available')).toBeInTheDocument();
    });

    it('should render action buttons', async () => {
      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      expect(screen.getByTitle('Toggle employee list')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper button titles for accessibility', async () => {
      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      expect(screen.getByTestId('minimize-button')).toHaveAttribute('title', 'Minimize');
      expect(screen.getByTestId('maximize-button')).toHaveAttribute('title', 'Maximize');
      expect(screen.getByTestId('close-button')).toHaveAttribute('title', 'Close');
    });

    it('should be keyboard accessible', async () => {
      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      const minimizeButton = screen.getByTestId('minimize-button');
      const maximizeButton = screen.getByTestId('maximize-button');
      const closeButton = screen.getByTestId('close-button');

      expect(minimizeButton.tagName).toBe('BUTTON');
      expect(maximizeButton.tagName).toBe('BUTTON');
      expect(closeButton.tagName).toBe('BUTTON');
    });
  });

  describe('Eye Button - Screen Visibility', () => {
    it('should render eye button with correct initial state', async () => {
      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      const eyeButton = screen.getByRole('button', { name: /screen sharing/i });
      expect(eyeButton).toBeInTheDocument();
      expect(eyeButton).toHaveAttribute('title', 'Show screen sharing');
    });

    it('should show eye-off icon when screen is inactive', async () => {
      mockUserContext.user.screen_active = false;

      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      const eyeButton = screen.getByRole('button', { name: /screen sharing/i });
      expect(eyeButton).toHaveAttribute('title', 'Show screen sharing');
    });

    it('should show eye icon when screen is active', async () => {
      mockUserContext.user.screen_active = true;

      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      const eyeButton = screen.getByRole('button', { name: /screen sharing/i });
      expect(eyeButton).toHaveAttribute('title', 'Hide screen sharing');
    });

    it('should call toggleScreenActive when eye button is clicked', async () => {
      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      const eyeButton = screen.getByRole('button', { name: /screen sharing/i });

      await act(async () => {
        fireEvent.click(eyeButton);
      });

      expect(mockUserContext.toggleScreenActive).toHaveBeenCalledTimes(1);
    });

    it('should be disabled when user is loading', async () => {
      mockUserContext.isLoading = true;

      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      const eyeButton = screen.getByRole('button', { name: /screen sharing/i });
      expect(eyeButton).toBeDisabled();
    });

    it('should be disabled when user is null', async () => {
      mockUserContext.user = null;

      await act(async () => {
        render(<TopBar {...defaultProps} />);
      });

      const eyeButton = screen.getByRole('button', { name: /screen sharing/i });
      expect(eyeButton).toBeDisabled();
    });

    it('should have different styling based on screen_active state', async () => {
      // Test inactive state
      mockUserContext.user.screen_active = false;

      const { rerender } = render(<TopBar {...defaultProps} />);

      let eyeButton = screen.getByRole('button', { name: /screen sharing/i });
      expect(eyeButton).toHaveClass('text-custom-text/80');

      // Test active state
      mockUserContext.user.screen_active = true;

      await act(async () => {
        rerender(<TopBar {...defaultProps} />);
      });

      eyeButton = screen.getByRole('button', { name: /screen sharing/i });
      expect(eyeButton).toHaveClass('text-custom-primary');
    });
  });
});