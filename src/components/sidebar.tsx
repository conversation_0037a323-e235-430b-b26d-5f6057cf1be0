import React from "react";
import { TauriIcon as Icon } from "./TauriIcon";
import { Toolt<PERSON>, Divider } from "@heroui/react";
import { motion, AnimatePresence } from "framer-motion";

const parentApps = [
  {
    id: "time",
    label: "Time Tracking - HMR Test",
    icon: "lucide:clock",
    submenus: [
      { id: "home", label: "Home", icon: "lucide:home" },
      { id: "time", label: "Time", icon: "lucide:timer" },
      { id: "reports", label: "Reports", icon: "lucide:bar-chart-2" },
    ],
  },
  {
    id: "chat",
    label: "Chat App",
    icon: "lucide:message-circle",
    submenus: [
      { id: "chats", label: "Chats", icon: "lucide:message-square" },
      { id: "contacts", label: "Contacts", icon: "lucide:users" },
      { id: "channels", label: "Channels", icon: "lucide:hash" },
    ],
  },
  {
    id: "project",
    label: "Project Management",
    icon: "lucide:layout-kanban",
    submenus: [
      { id: "projects", label: "Projects", icon: "lucide:folder" },
      { id: "boards", label: "Boards", icon: "lucide:trello" },
      { id: "backlog", label: "Backlog", icon: "lucide:list" },
    ],
  },
  {
    id: "file",
    label: "File Manager",
    icon: "lucide:file",
    submenus: [
      { id: "my-files", label: "My Files", icon: "lucide:file-text" },
      { id: "shared", label: "Shared", icon: "lucide:share-2" },
      { id: "recent", label: "Recent", icon: "lucide:clock" },
    ],
  },
];

interface SidebarProps {
  activeApp: string;
  onChangeApp: (appId: string) => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ activeApp, onChangeApp }) => {
  const [expandedApp, setExpandedApp] = React.useState<string | null>(activeApp);

  const toggleApp = (appId: string) => {
    if (expandedApp === appId) {
      setExpandedApp(null);
    } else {
      setExpandedApp(appId);
      onChangeApp(appId);
    }
  };

  return (
    <aside className="w-[240px] bg-custom-sidebar flex flex-col h-screen border-r border-custom-border">
      {/* Logo area */}
      <div className="p-4 flex items-center">
        <div className="w-10 h-10 bg-custom-primary rounded-md flex items-center justify-center">
          <Icon icon="lucide:layout-dashboard" className="text-white text-xl" />
        </div>
        <span className="text-white text-lg font-medium ml-3">Dashboard</span>
      </div>
      
      <Divider className="my-3" />
      
      {/* App Navigation */}
      <div className="px-2 overflow-y-auto flex-grow custom-scrollbar">
        {parentApps.map((app) => (
          <div key={app.id} className="mb-1">
            {/* Parent App Button */}
            <div 
              className={`flex items-center px-3 py-2.5 rounded-lg cursor-pointer
              ${expandedApp === app.id ? "bg-primary/10 text-primary" : "hover:bg-primary/5 text-custom-text"}`}
              onClick={() => toggleApp(app.id)}
            >

              <Icon 
                icon={app.icon} 
                className={`text-xl ${expandedApp === app.id ? "text-primary" : "text-custom-text"}`} 
              />
              <span className={`ml-3 text-sm font-medium ${expandedApp === app.id ? "text-primary" : ""}`}>
                {app.label}
              </span>
              <Icon 
                icon={expandedApp === app.id ? "lucide:chevron-up" : "lucide:chevron-down"} 
                className="text-custom-muted text-sm ml-auto" 
              />
            </div>
            
            {/* Submenu */}
            <AnimatePresence>
              {expandedApp === app.id && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className="pl-8 pr-2 py-1 space-y-1">
                    {app.submenus.map((submenu) => (
                      <Tooltip key={submenu.id} content={submenu.label} placement="right">
                        <div 
                          className="flex items-center px-3 py-2 rounded-lg cursor-pointer hover:bg-primary/5 text-custom-text"
                        >
                          <Icon icon={submenu.icon} className="text-lg text-custom-text" />
                          <span className="ml-3 text-xs">{submenu.label}</span>
                        </div>
                      </Tooltip>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        ))}
      </div>
      
      <div className="mt-auto p-4">
        <div className="w-full p-3 rounded-lg border border-custom-primary/30 bg-custom-primary/5 flex items-center justify-between">
          <span className="text-white text-sm font-medium">Upgrade Plan</span>
          <Icon icon="lucide:sparkles" className="text-custom-primary" />
        </div>
      </div>
    </aside>
  );
};

interface NavItemProps {
  icon: string;
  label: string;
  active?: boolean;
  hasSubmenu?: boolean;
}

const NavItem: React.FC<NavItemProps> = ({ icon, label, active, hasSubmenu }) => {
  return (
    <Tooltip content={label} placement="right">
      <div 
        className={`flex items-center px-3 py-2.5 rounded-lg mb-1 cursor-pointer hover:bg-primary/5 
        ${active ? "active-link" : ""}`}
      >
        <Icon icon={icon} className={`text-xl ${active ? "text-primary" : "text-custom-text"}`} />
        <span className={`ml-3 text-sm font-medium ${active ? "text-primary" : "text-custom-text"}`}>
          {label}
        </span>
        {hasSubmenu && (
          <Icon 
            icon="lucide:chevron-down" 
            className="text-custom-muted text-sm ml-auto"
          />
        )}
      </div>
    </Tooltip>
  );
};