import React from "react";
import { Tabs, Tab, Card, CardBody } from "@heroui/react";

interface DaySummary {
  day: string;
  hours: string;
  isActive?: boolean;
}

export const WeeklySummary = () => {
  const [selected, setSelected] = React.useState<string>("thu");
  
  const days: DaySummary[] = [
    { day: "Tue", hours: "7:32" },
    { day: "Wed", hours: "5:30" },
    { day: "Thu", hours: "6:12", isActive: true },
    { day: "Fri", hours: "8:50" },
    { day: "Sat", hours: "7:20" },
    { day: "Sun", hours: "7:20" },
  ];
  
  return (
    <Card className="bg-custom-card border-custom-border shadow-none">
      <CardBody className="p-0">
        <Tabs 
          aria-label="Weekly time summary"
          selectedKey={selected}
          onSelectionChange={(key) => setSelected(key as string)}
          classNames={{
            base: "w-full",
            tabList: "bg-custom-card border-b border-custom-border p-0",
            tab: "h-14 py-2 px-4",
            tabContent: "text-center group-data-[selected=true]:text-primary",
            cursor: "bg-primary",
            panel: "p-6",
          }}
          color="primary"
          variant="underlined"
        >
          {days.map((day) => (
            <Tab
              key={day.day.toLowerCase()}
              title={
                <div className="flex flex-col items-center">
                  <span className="text-sm">{day.day}</span>
                  <div className="flex items-baseline mt-1">
                    <span className="text-lg font-medium">{day.hours}</span>
                    <span className="text-xs text-custom-muted ml-1">Total</span>
                  </div>
                </div>
              }
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center">
                    <span className="text-primary text-lg">02:</span>
                    <span className="text-primary text-lg">32:</span>
                    <span className="text-primary text-lg">56</span>
                  </div>
                  <div className="flex flex-col">
                    <div className="flex items-center gap-2">
                      <span className="px-2 py-1 bg-primary/10 text-primary text-xs rounded">
                        Timee Project
                      </span>
                      <span className="px-2 py-1 bg-primary/10 text-primary text-xs rounded">
                        Jira Task
                      </span>
                      <span className="px-2 py-1 bg-primary/10 text-primary text-xs rounded">
                        at Company
                      </span>
                    </div>
                  </div>
                </div>
                <div className="cursor-pointer">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 6.75V12L16.5 16.5M7.8 21H16.2C17.8802 21 18.7202 21 19.362 20.673C19.9265 20.3854 20.3854 19.9265 20.673 19.362C21 18.7202 21 17.8802 21 16.2V7.8C21 6.11984 21 5.27976 20.673 4.63803C20.3854 4.07354 19.9265 3.6146 19.362 3.32698C18.7202 3 17.8802 3 16.2 3H7.8C6.11984 3 5.27976 3 4.63803 3.32698C4.07354 3.6146 3.6146 4.07354 3.32698 4.63803C3 5.27976 3 6.11984 3 7.8V16.2C3 17.8802 3 18.7202 3.32698 19.362C3.6146 19.9265 4.07354 20.3854 4.63803 20.673C5.27976 21 6.11984 21 7.8 21Z" stroke="#1B84FF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              </div>
            </Tab>
          ))}
        </Tabs>
      </CardBody>
    </Card>
  );
};