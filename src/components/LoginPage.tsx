import React, { useState } from "react";
import { invoke } from "@tauri-apps/api/core";
import { Card, CardBody, Input, Button } from "@heroui/react";
import { Icon } from "@iconify/react";
import { LoginTopBar } from "./LoginTopBar";
// import { LiveKitConnectionTest } from "./LiveKitConnectionTest";

interface AuthResponse {
  success: boolean;
  token?: string;
  message: string;
}

interface LoginPageProps {
  onLoginSuccess: (token: string) => void;
}

export const LoginPage: React.FC<LoginPageProps> = ({ onLoginSuccess }) => {
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("12345678");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [showPassword, setShowPassword] = useState(false);

  // Core login logic
  const performLogin = async () => {
    setIsLoading(true);
    setError("");

    try {
      const response = await invoke<AuthResponse>("login", {
        email,
        password,
      });

      if (response.success && response.token) {
        onLoginSuccess(response.token);
      } else {
        const errorMessage = response.message || "Login failed";
        setError(errorMessage);
      }
    } catch (err) {
      const errorMessage = typeof err === 'string' ? err : "An error occurred during login";
      setError(errorMessage);
      console.error("Login error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form submission
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    await performLogin();
  };

  // Handle Enter key press for form submission
  const handleKeyDown = (e: React.KeyboardEvent) => {
    console.log('Key pressed:', e.key); // Debug log
    if (e.key === 'Enter' && !isLoading) {
      console.log('Enter key detected, submitting form...'); // Debug log
      e.preventDefault();
      performLogin();
    }
  };

  return (
    <div className="min-h-screen bg-custom-background flex flex-col">
      {/* TopBar */}
      <LoginTopBar />

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-6">
        {/* Background Effects */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 w-full max-w-md">
          {/* Logo Section */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-blue-600 rounded-2xl mb-4 shadow-lg">
              <span className="text-white text-2xl font-bold">TB</span>
            </div>
            <h1 className="text-3xl font-bold text-white mb-2">Welcome Back</h1>
            <p className="text-custom-muted">Sign in to your TeamBy account</p>
          </div>

          {/* Login Form */}
          <Card className="bg-custom-card border-custom-border shadow-2xl">
            <CardBody className="p-8">
              <form onSubmit={handleLogin} onKeyDown={handleKeyDown} className="space-y-6">
                {/* Email Input */}
                <Input
                  type="email"
                  label="Email Address"
                  placeholder="Enter your email"
                  value={email}
                  onValueChange={setEmail}
                  onKeyDown={handleKeyDown}
                  isRequired
                  startContent={
                    <Icon icon="lucide:mail" className="text-custom-muted" />
                  }
                  classNames={{
                    inputWrapper: "bg-custom-sidebar border-custom-border",
                    input: "text-custom-text",
                    label: "text-custom-muted",
                  }}
                />

                {/* Password Input */}
                <Input
                  type={showPassword ? "text" : "password"}
                  label="Password"
                  placeholder="Enter your password"
                  value={password}
                  onValueChange={setPassword}
                  onKeyDown={handleKeyDown}
                  isRequired
                  startContent={
                    <Icon icon="lucide:lock" className="text-custom-muted" />
                  }
                  endContent={
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="text-custom-muted hover:text-custom-text transition-colors"
                    >
                      <Icon
                        icon={showPassword ? "lucide:eye-off" : "lucide:eye"}
                      />
                    </button>
                  }
                  classNames={{
                    inputWrapper: "bg-custom-sidebar border-custom-border",
                    input: "text-custom-text",
                    label: "text-custom-muted",
                  }}
                />

                {/* Error Message */}
                {error && (
                  <div className="flex items-center gap-2 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                    <Icon icon="lucide:alert-circle" className="text-red-500" />
                    <span className="text-red-500 text-sm">{error}</span>
                  </div>
                )}



                {/* Login Button */}
                <Button
                  type="submit"
                  color="primary"
                  size="lg"
                  className="w-full font-medium"
                  isLoading={isLoading}
                  disabled={!email || !password}
                >
                  {isLoading ? "Signing In..." : "Sign In"}
                </Button>
              </form>

              {/* Footer */}
              <div className="mt-6 text-center">
                <p className="text-custom-muted text-sm">
                  Secure authentication powered by TeamBy
                </p>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* LiveKit Connection Test */}
        {/* <div className="mt-8">
          <LiveKitConnectionTest />
        </div> */}
      </div>
    </div>
  );
};