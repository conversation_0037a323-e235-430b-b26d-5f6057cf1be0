// User Profile Types
export interface IncompleteActivity {
  activity_id: number;
  duration: string;
}

export interface UserProfile {
  full_name: string;
  email: string;
  avatar: string | null;
  position: string | null;
  company: string | null;
  is_admin: boolean;
  screen_active: boolean;
  incomplete_activity: IncompleteActivity | null;
}

export interface UserProfileResponse {
  success: boolean;
  data: UserProfile;
  message: string;
}

export interface UserProfileError {
  success: boolean;
  message: string;
}
