// Types for Screen View functionality

export interface ScreenViewEmployeeData {
  id: number;
  full_name: string;
  position_name: string | null;
  avatar: string;
  isonline: boolean;
  screan_active: boolean;
  email?: string;
  phone?: string;
  location?: string;
  bio?: string;
  skills?: string[];
  projects?: {
    name: string;
    role: string;
  }[];
}

export interface ScreenViewUserProfile {
  full_name: string;
  email: string;
  avatar: string | null;
  position: string | null;
  company: string | null;
  is_admin: boolean;
  screen_active: boolean;
}

export interface ScreenViewWindowData {
  currentUser: ScreenViewUserProfile;
  selectedEmployee: ScreenViewEmployeeData;
  timestamp: number;
  source: 'team-sidebar';
}
