export interface TeamMember {
      id: number;
      name: string;
      fullName?: string;
      role: string;
      avatar: string;
      isOnline?: boolean;
      lastSeen?: string;
      bio?: string;
      email?: string;
      phone?: string;
      location?: string;
      yearsAtCompany?: string;
      slogan?: string;
      skills?: string[];
      projects?: {
        name: string;
        role: string;
        color?: string;
      }[];
      achievements?: {
        id: number;
        title: string;
        date: string;
        badge: string;
        description: string;
      }[];
      twitter?: string;
      linkedin?: string;
      github?: string;
    }