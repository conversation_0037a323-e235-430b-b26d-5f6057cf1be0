/**
 * Visibility utility functions for managing screen visibility states
 * These functions provide reusable methods for handling visibility toggles
 * across different components in the application.
 */

export type VisibilityState = 'visible' | 'hidden';

/**
 * Icon mapping for visibility states using Lucide icons
 */
export const VISIBILITY_ICONS = {
  visible: 'lucide:eye',
  hidden: 'lucide:eye-off'
} as const;

/**
 * Get the appropriate icon for a visibility state
 * @param isVisible - Boolean indicating if the item is visible
 * @returns The corresponding Lucide icon string
 */
export const getVisibilityIcon = (isVisible: boolean): string => {
  return isVisible ? VISIBILITY_ICONS.visible : VISIBILITY_ICONS.hidden;
};

/**
 * Get the visibility state from a boolean value
 * @param isVisible - Boolean indicating if the item is visible
 * @returns The corresponding visibility state
 */
export const getVisibilityState = (isVisible: boolean): VisibilityState => {
  return isVisible ? 'visible' : 'hidden';
};

/**
 * Toggle visibility state
 * @param currentState - Current visibility state
 * @returns The opposite visibility state
 */
export const toggleVisibilityState = (currentState: VisibilityState): VisibilityState => {
  return currentState === 'visible' ? 'hidden' : 'visible';
};

/**
 * Toggle boolean visibility
 * @param isVisible - Current boolean visibility state
 * @returns The opposite boolean state
 */
export const toggleVisibility = (isVisible: boolean): boolean => {
  return !isVisible;
};

/**
 * Get tooltip text for visibility state
 * @param isVisible - Boolean indicating if the item is visible
 * @param context - Optional context for the tooltip (e.g., 'screen', 'item')
 * @returns Appropriate tooltip text
 */
export const getVisibilityTooltip = (isVisible: boolean, context: string = 'visibility'): string => {
  const action = isVisible ? 'Hide' : 'Show';
  return `${action} ${context}`;
};

/**
 * Get accessibility label for visibility button
 * @param isVisible - Boolean indicating if the item is visible
 * @param context - Optional context for the label
 * @returns Appropriate accessibility label
 */
export const getVisibilityAriaLabel = (isVisible: boolean, context: string = 'visibility'): string => {
  const state = isVisible ? 'visible' : 'hidden';
  const action = isVisible ? 'hide' : 'show';
  return `${context} is currently ${state}, click to ${action}`;
};

/**
 * Screen visibility specific utilities
 */
export const ScreenVisibility = {
  /**
   * Get icon for screen visibility state
   */
  getIcon: (screenActive: boolean): string => getVisibilityIcon(screenActive),
  
  /**
   * Get tooltip for screen visibility
   */
  getTooltip: (screenActive: boolean): string => getVisibilityTooltip(screenActive, 'screen sharing'),
  
  /**
   * Get aria label for screen visibility
   */
  getAriaLabel: (screenActive: boolean): string => getVisibilityAriaLabel(screenActive, 'screen sharing'),
  
  /**
   * Toggle screen visibility state
   */
  toggle: (screenActive: boolean): boolean => toggleVisibility(screenActive)
};

/**
 * Generic visibility utilities for reuse across components
 */
export const VisibilityUtils = {
  getIcon: getVisibilityIcon,
  getState: getVisibilityState,
  getTooltip: getVisibilityTooltip,
  getAriaLabel: getVisibilityAriaLabel,
  toggle: toggleVisibility,
  toggleState: toggleVisibilityState,
  icons: VISIBILITY_ICONS
};

/**
 * Hook-like utility for managing visibility state
 * This is not a React hook but provides similar functionality
 */
export const createVisibilityManager = (initialState: boolean = false) => {
  let state = initialState;
  
  return {
    get isVisible() {
      return state;
    },
    get icon() {
      return getVisibilityIcon(state);
    },
    get tooltip() {
      return getVisibilityTooltip(state);
    },
    get ariaLabel() {
      return getVisibilityAriaLabel(state);
    },
    toggle() {
      state = !state;
      return state;
    },
    setState(newState: boolean) {
      state = newState;
      return state;
    }
  };
};
