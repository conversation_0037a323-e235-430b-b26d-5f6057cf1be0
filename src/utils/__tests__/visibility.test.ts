import { describe, it, expect } from 'vitest';
import {
  getVisibilityIcon,
  getVisibilityState,
  toggleVisibilityState,
  toggleVisibility,
  getVisibilityTooltip,
  getVisibilityAriaLabel,
  ScreenVisibility,
  VisibilityUtils,
  createVisibilityManager,
  VISIBILITY_ICONS
} from '../visibility';

describe('Visibility Utilities', () => {
  describe('getVisibilityIcon', () => {
    it('should return eye icon for visible state', () => {
      expect(getVisibilityIcon(true)).toBe('lucide:eye');
    });

    it('should return eye-off icon for hidden state', () => {
      expect(getVisibilityIcon(false)).toBe('lucide:eye-off');
    });
  });

  describe('getVisibilityState', () => {
    it('should return "visible" for true', () => {
      expect(getVisibilityState(true)).toBe('visible');
    });

    it('should return "hidden" for false', () => {
      expect(getVisibilityState(false)).toBe('hidden');
    });
  });

  describe('toggleVisibilityState', () => {
    it('should toggle from visible to hidden', () => {
      expect(toggleVisibilityState('visible')).toBe('hidden');
    });

    it('should toggle from hidden to visible', () => {
      expect(toggleVisibilityState('hidden')).toBe('visible');
    });
  });

  describe('toggleVisibility', () => {
    it('should toggle true to false', () => {
      expect(toggleVisibility(true)).toBe(false);
    });

    it('should toggle false to true', () => {
      expect(toggleVisibility(false)).toBe(true);
    });
  });

  describe('getVisibilityTooltip', () => {
    it('should return hide tooltip for visible state', () => {
      expect(getVisibilityTooltip(true)).toBe('Hide visibility');
    });

    it('should return show tooltip for hidden state', () => {
      expect(getVisibilityTooltip(false)).toBe('Show visibility');
    });

    it('should use custom context', () => {
      expect(getVisibilityTooltip(true, 'screen')).toBe('Hide screen');
      expect(getVisibilityTooltip(false, 'screen')).toBe('Show screen');
    });
  });

  describe('getVisibilityAriaLabel', () => {
    it('should return appropriate aria label for visible state', () => {
      expect(getVisibilityAriaLabel(true)).toBe('visibility is currently visible, click to hide');
    });

    it('should return appropriate aria label for hidden state', () => {
      expect(getVisibilityAriaLabel(false)).toBe('visibility is currently hidden, click to show');
    });

    it('should use custom context', () => {
      expect(getVisibilityAriaLabel(true, 'screen')).toBe('screen is currently visible, click to hide');
      expect(getVisibilityAriaLabel(false, 'screen')).toBe('screen is currently hidden, click to show');
    });
  });

  describe('ScreenVisibility', () => {
    it('should provide correct icon for screen active state', () => {
      expect(ScreenVisibility.getIcon(true)).toBe('lucide:eye');
      expect(ScreenVisibility.getIcon(false)).toBe('lucide:eye-off');
    });

    it('should provide correct tooltip for screen active state', () => {
      expect(ScreenVisibility.getTooltip(true)).toBe('Hide screen sharing');
      expect(ScreenVisibility.getTooltip(false)).toBe('Show screen sharing');
    });

    it('should provide correct aria label for screen active state', () => {
      expect(ScreenVisibility.getAriaLabel(true)).toBe('screen sharing is currently visible, click to hide');
      expect(ScreenVisibility.getAriaLabel(false)).toBe('screen sharing is currently hidden, click to show');
    });

    it('should toggle screen active state', () => {
      expect(ScreenVisibility.toggle(true)).toBe(false);
      expect(ScreenVisibility.toggle(false)).toBe(true);
    });
  });

  describe('VisibilityUtils', () => {
    it('should provide all utility functions', () => {
      expect(VisibilityUtils.getIcon).toBe(getVisibilityIcon);
      expect(VisibilityUtils.getState).toBe(getVisibilityState);
      expect(VisibilityUtils.getTooltip).toBe(getVisibilityTooltip);
      expect(VisibilityUtils.getAriaLabel).toBe(getVisibilityAriaLabel);
      expect(VisibilityUtils.toggle).toBe(toggleVisibility);
      expect(VisibilityUtils.toggleState).toBe(toggleVisibilityState);
      expect(VisibilityUtils.icons).toBe(VISIBILITY_ICONS);
    });
  });

  describe('createVisibilityManager', () => {
    it('should create manager with default false state', () => {
      const manager = createVisibilityManager();
      expect(manager.isVisible).toBe(false);
      expect(manager.icon).toBe('lucide:eye-off');
      expect(manager.tooltip).toBe('Show visibility');
      expect(manager.ariaLabel).toBe('visibility is currently hidden, click to show');
    });

    it('should create manager with custom initial state', () => {
      const manager = createVisibilityManager(true);
      expect(manager.isVisible).toBe(true);
      expect(manager.icon).toBe('lucide:eye');
      expect(manager.tooltip).toBe('Hide visibility');
      expect(manager.ariaLabel).toBe('visibility is currently visible, click to hide');
    });

    it('should toggle state correctly', () => {
      const manager = createVisibilityManager(false);
      expect(manager.toggle()).toBe(true);
      expect(manager.isVisible).toBe(true);
      expect(manager.icon).toBe('lucide:eye');
    });

    it('should set state correctly', () => {
      const manager = createVisibilityManager(false);
      expect(manager.setState(true)).toBe(true);
      expect(manager.isVisible).toBe(true);
      expect(manager.setState(false)).toBe(false);
      expect(manager.isVisible).toBe(false);
    });
  });

  describe('VISIBILITY_ICONS', () => {
    it('should have correct icon mappings', () => {
      expect(VISIBILITY_ICONS.visible).toBe('lucide:eye');
      expect(VISIBILITY_ICONS.hidden).toBe('lucide:eye-off');
    });
  });
});
