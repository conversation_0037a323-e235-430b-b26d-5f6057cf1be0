import { format } from 'date-fns';

export function formatDate(dateInput: string | number | Date, dateFormat: string = 'MMM dd, yyyy'): string {
  if (!dateInput) return '';
  try {
    const date = typeof dateInput === 'string' || typeof dateInput === 'number' ? new Date(dateInput) : dateInput;
    return format(date, dateFormat);
  } catch (err) {
    console.error('formatDate error:', err);
    return '';
  }
}

// Attach to global scope so legacy code using the global function continues to work
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
if (typeof window !== 'undefined') window.formatDate = formatDate as any; 