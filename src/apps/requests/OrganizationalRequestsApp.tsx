import React from "react";
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>,
  <PERSON>b,
  <PERSON><PERSON>,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Input,
  Select,
  SelectItem,
  Chip,
  useDisclosure,
  Modal,
  ModalContent
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { RequestList } from "./components/RequestList";
import { NewRequestForm } from "./components/NewRequestForm";
import { RequestDetails } from "./components/RequestDetails";
import { mockRequests } from "./data/mock-data";
import type { Request, RequestStatus, RequestType } from "./types";

export const OrganizationalRequestsApp = () => {
  const [activeTab, setActiveTab] = React.useState<string>("my-requests");
  const { isOpen, onOpen, onClose, onOpenChange } = useDisclosure();
  const [requests, setRequests] = React.useState<Request[]>(mockRequests);
  const [selectedRequest, setSelectedRequest] = React.useState<Request | null>(null);
  const [showRequestDetails, setShowRequestDetails] = React.useState(false);
  const [filterStatus, setFilterStatus] = React.useState<RequestStatus | "all">("all");
  const [filterType, setFilterType] = React.useState<RequestType | "all">("all");
  const [searchQuery, setSearchQuery] = React.useState("");
  const [dateRange, setDateRange] = React.useState({
    from: null as Date | null,
    to: null as Date | null
  });

  // Handle tab change
  const handleTabChange = (key: React.Key) => {
    setActiveTab(key as string);
    setSelectedRequest(null);
    setShowRequestDetails(false);
  };

  // Handle view request details
  const handleViewRequest = (request: Request) => {
    setSelectedRequest(request);
    setShowRequestDetails(true);
  };

  // Handle creating a new request
  const handleCreateRequest = (newRequest: Omit<Request, "id" | "status" | "statusHistory" | "createdAt">) => {
    const now = new Date().toISOString();
    const id = `req-${Math.random().toString(36).substring(2, 10)}`;

    const request: Request = {
      id,
      ...newRequest,
      status: "pending",
      statusHistory: [
        {
          status: "pending",
          date: now,
          note: "Request submitted and pending review.",
          updatedBy: "system"
        }
      ],
      createdAt: now
    };

    setRequests([request, ...requests]);
    setActiveTab("my-requests");
  };

  // Filter requests based on search query, status, type, and date range
  const filteredRequests = requests.filter((request) => {
    // Filter by search query
    if (searchQuery && !request.title.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !request.description.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }

    // Filter by status
    if (filterStatus !== "all" && request.status !== filterStatus) {
      return false;
    }

    // Filter by type
    if (filterType !== "all" && request.type !== filterType) {
      return false;
    }

    // Filter by date range
    if (dateRange.from && dateRange.to) {
      const requestDate = new Date(request.createdAt);
      if (requestDate < dateRange.from || requestDate > dateRange.to) {
        return false;
      }
    }

    return true;
  });

  // Update request status (for admin/HR role)
  const handleUpdateStatus = (requestId: string, newStatus: RequestStatus, note: string) => {
    setRequests(requests.map(request => {
      if (request.id === requestId) {
        const now = new Date().toISOString();
        return {
          ...request,
          status: newStatus,
          statusHistory: [
            ...request.statusHistory,
            {
              status: newStatus,
              date: now,
              note: note,
              updatedBy: "Admin User" // In a real app, this would be the current user
            }
          ]
        };
      }
      return request;
    }));

    if (selectedRequest && selectedRequest.id === requestId) {
      setSelectedRequest({
        ...selectedRequest,
        status: newStatus,
        statusHistory: [
          ...selectedRequest.statusHistory,
          {
            status: newStatus,
            date: new Date().toISOString(),
            note: note,
            updatedBy: "Admin User"
          }
        ]
      });
    }
  };

  // Render status badge with appropriate color
  const renderStatusBadge = (status: RequestStatus) => {
    let color = "default";
    
    switch (status) {
      case "approved":
        color = "success";
        break;
      case "pending":
        color = "warning";
        break;
      case "rejected":
        color = "danger";
        break;
      default:
        color = "default";
    }

    return (
      <Chip color={color as any} variant="flat" size="sm">
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Chip>
    );
  };

  // Decide what to render based on active tab and view state
  const renderContent = () => {
    if (showRequestDetails && selectedRequest) {
      return (
        <RequestDetails 
          request={selectedRequest} 
          onClose={() => setShowRequestDetails(false)}
          onUpdateStatus={handleUpdateStatus}
          renderStatusBadge={renderStatusBadge}
        />
      );
    }

    switch (activeTab) {
      case "my-requests":
      default:
        return (
          <RequestList 
            requests={filteredRequests} 
            onViewRequest={handleViewRequest}
            renderStatusBadge={renderStatusBadge}
          />
        );
      case "leave":
        return (
          <RequestList 
            requests={filteredRequests.filter(req => req.type === "leave")} 
            onViewRequest={handleViewRequest}
            renderStatusBadge={renderStatusBadge}
          />
        );
      case "loan":
        return (
          <RequestList 
            requests={filteredRequests.filter(req => req.type === "loan")} 
            onViewRequest={handleViewRequest}
            renderStatusBadge={renderStatusBadge}
          />
        );
      case "overtime":
        return (
          <RequestList 
            requests={filteredRequests.filter(req => req.type === "overtime")} 
            onViewRequest={handleViewRequest}
            renderStatusBadge={renderStatusBadge}
          />
        );
      case "other":
        return (
          <RequestList 
            requests={filteredRequests.filter(req => req.type === "other")} 
            onViewRequest={handleViewRequest}
            renderStatusBadge={renderStatusBadge}
          />
        );
    }
  };

  return (
    <div className="p-6 max-w-[1600px] mx-auto">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-white text-2xl font-semibold">Organizational Requests</h1>
          <p className="text-custom-muted">Manage your leave, loan, overtime and other requests</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button 
            variant="flat" 
            color="primary"
            startContent={<Icon icon="lucide:plus" />}
            onPress={onOpen}
          >
            New Request
          </Button>
        </div>
      </div>
      
      {/* Filter section */}
      <Card className="mb-6 bg-custom-card border-custom-border">
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Input
                label="Search"
                placeholder="Search requests..."
                value={searchQuery}
                onValueChange={setSearchQuery}
                startContent={<Icon icon="lucide:search" className="text-custom-muted" />}
                variant="bordered"
              />
            </div>
            <div>
              <Select 
                label="Status"
                placeholder="Filter by status"
                selectedKeys={[filterStatus]}
                onChange={(e) => setFilterStatus(e.target.value as RequestStatus | "all")}
                variant="bordered"
              >
                <SelectItem key="all" value="all">All Statuses</SelectItem>
                <SelectItem key="pending" value="pending">Pending</SelectItem>
                <SelectItem key="approved" value="approved">Approved</SelectItem>
                <SelectItem key="rejected" value="rejected">Rejected</SelectItem>
              </Select>
            </div>
            <div>
              <Select 
                label="Type"
                placeholder="Filter by type"
                selectedKeys={[filterType]}
                onChange={(e) => setFilterType(e.target.value as RequestType | "all")}
                variant="bordered"
              >
                <SelectItem key="all" value="all">All Types</SelectItem>
                <SelectItem key="leave" value="leave">Leave Request</SelectItem>
                <SelectItem key="loan" value="loan">Loan Request</SelectItem>
                <SelectItem key="overtime" value="overtime">Overtime Request</SelectItem>
                <SelectItem key="other" value="other">Other Request</SelectItem>
              </Select>
            </div>
            <div className="flex items-end">
              <Button 
                onPress={() => {
                  setFilterStatus("all");
                  setFilterType("all");
                  setSearchQuery("");
                  setDateRange({ from: null, to: null });
                }}
                variant="light"
                color="primary"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      
      {/* New Request Modal */}
      <Modal 
        isOpen={isOpen} 
        onOpenChange={onOpenChange} 
        size="3xl"
      >
        <ModalContent>
          {(onClose) => (
            <NewRequestForm 
              onCreateRequest={(newRequest) => {
                handleCreateRequest(newRequest);
                onClose();
              }} 
            />
          )}
        </ModalContent>
      </Modal>
      
      {renderContent()}
    </div>
  );
};