export type RequestType = "leave" | "loan" | "overtime" | "other";
export type RequestStatus = "pending" | "approved" | "rejected";

export interface StatusHistoryItem {
  status: RequestStatus;
  date: string;
  note: string;
  updatedBy: string;
}

export interface Request {
  id: string;
  type: RequestType;
  title: string;
  description: string;
  status: RequestStatus;
  createdAt: string;
  statusHistory: StatusHistoryItem[];
  dateRange?: {
    from: string;
    to: string;
  };
  amount?: number;
  hours?: number;
  attachments?: {
    name: string;
    url: string;
    type: string;
  }[];
  createdBy?: {
    id: string;
    name: string;
    avatar: string;
  };
}