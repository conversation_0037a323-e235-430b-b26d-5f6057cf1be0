import React, { useState, useMemo } from "react";
import { 
  <PERSON>, 
  TableHeader, 
  TableColumn, 
  TableBody, 
  TableRow, 
  TableCell,
  Button,
  Tooltip,
  Card,
  CardBody,
  Badge,
  ButtonGroup,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Input,
  Select,
  SelectItem,
  Pagination,
  DateRangePicker
} from "@heroui/react";
import { Icon } from "@iconify/react";
import type { Request, RequestStatus, RequestType } from "../types";
import { format } from 'date-fns';
import { parseDate, CalendarDate } from "@internationalized/date";
import type { RangeValue } from "@heroui/react";

interface RequestListProps {
  requests: Request[];
  onViewRequest: (request: Request) => void;
  renderStatusBadge: (status: RequestStatus) => React.ReactNode;
}

export const RequestList: React.FC<RequestListProps> = ({ 
  requests, 
  onViewRequest,
  renderStatusBadge
}) => {
  /* -------------------- Utility Functions -------------------- */
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (e) {
      return dateString;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "leave": return <Icon icon="lucide:calendar" />;
      case "loan": return <Icon icon="lucide:credit-card" />;
      case "overtime": return <Icon icon="lucide:clock" />;
      default: return <Icon icon="lucide:file-text" />;
    }
  };

  /* -------------------- Initial Date Range -------------------- */
  const initialDateRange = useMemo<RangeValue<CalendarDate>>(() => {
    if (requests.length === 0) {
      const oneYearAgo = new Date();
      oneYearAgo.setDate(oneYearAgo.getDate() - 365);
      return {
        start: parseDate(oneYearAgo.toISOString().split("T")[0]),
        end: parseDate(new Date().toISOString().split("T")[0]),
      };
    }
    const earliest = requests.reduce((min, r) => {
      const date = new Date(r.createdAt);
      return date < min ? date : min;
    }, new Date(requests[0].createdAt));

    return {
      start: parseDate(earliest.toISOString().split("T")[0]),
      end: parseDate(new Date().toISOString().split("T")[0]),
    };
  }, [requests]);

  /* -------------------- Local State -------------------- */
  const [dateRange, setDateRange] = useState<RangeValue<CalendarDate>>(initialDateRange);
  const [statusFilter, setStatusFilter] = useState<RequestStatus | "all">("all");
  const [typeFilter, setTypeFilter] = useState<RequestType | "all">("all");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [showFilters, setShowFilters] = useState<boolean>(false);
  const [sortBy, setSortBy] = useState<string>("date");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  /* -------------------- Filtering & Sorting -------------------- */
  const filteredRequests = useMemo(() => {
    let filtered = [...requests];

    // Date range filter
    if (dateRange) {
      const startDate = new Date(dateRange.start.year, dateRange.start.month - 1, dateRange.start.day);
      const endDate = new Date(dateRange.end.year, dateRange.end.month - 1, dateRange.end.day);
      endDate.setHours(23, 59, 59, 999);

      filtered = filtered.filter((req) => {
        const reqDate = new Date(req.createdAt);
        return reqDate >= startDate && reqDate <= endDate;
      });
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((req) => req.status === statusFilter);
    }

    // Type filter
    if (typeFilter !== "all") {
      filtered = filtered.filter((req) => req.type === typeFilter);
    }

    // Search filter
    if (searchQuery) {
      const q = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (req) =>
          req.title.toLowerCase().includes(q) ||
          req.description.toLowerCase().includes(q)
      );
    }

    // Sorting
    const sortFn = (a: Request, b: Request) => {
      let res = 0;
      switch (sortBy) {
        case "date":
          res = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        case "title":
          res = a.title.localeCompare(b.title);
          break;
        case "type":
          res = a.type.localeCompare(b.type);
          break;
        case "status":
          res = a.status.localeCompare(b.status);
          break;
        default:
          break;
      }
      return sortDirection === "asc" ? res : -res;
    };

    return filtered.sort(sortFn);
  }, [requests, dateRange, statusFilter, typeFilter, searchQuery, sortBy, sortDirection]);

  /* -------------------- Render -------------------- */

  if (filteredRequests.length === 0) {
    return (
      <Card className="bg-custom-card border-custom-border">
        <div className="flex flex-col items-center justify-center py-12">
          <Icon icon="lucide:file-question" className="text-5xl text-custom-muted mb-4" />
          <h3 className="text-xl font-medium mb-2">No requests found</h3>
          <p className="text-custom-muted mb-6">We couldn't find any requests that match your criteria</p>
        </div>
      </Card>
    );
  }

  return (
    <div className="flex flex-col gap-6">
      {/* Filters */}
      <Card className="bg-custom-card border-custom-border shadow-none">
        <CardBody>
          <div className="flex flex-col gap-4">
            {/* Primary Filters */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-1">
                <DateRangePicker
                  label="Date Range"
                  value={dateRange}
                  onChange={setDateRange}
                  className="w-full"
                  labelPlacement="outside-left"
                  classNames={{ base: "bg-custom-card border-custom-border" }}
                />
              </div>
              <div className="flex items-center gap-2 md:col-span-2">
                <Input
                  placeholder="Search requests..."
                  value={searchQuery}
                  onValueChange={setSearchQuery}
                  startContent={<Icon icon="lucide:search" className="text-custom-muted" />}
                  classNames={{ base: "flex-1", inputWrapper: "bg-custom-sidebar border-custom-border" }}
                />
                <Button
                  variant="flat"
                  startContent={<Icon icon="lucide:filter" />}
                  className="bg-custom-sidebar"
                  onPress={() => setShowFilters(!showFilters)}
                >
                  Filter
                </Button>
              </div>
            </div>

            {/* Advanced Filters */}
            {showFilters && (
              <div className="grid grid-cols-2 gap-3 bg-custom-sidebar p-4 rounded-lg mt-4">
                {/* Status Select */}
                <Select
                  label="Status"
                  selectedKeys={[statusFilter] as any}
                  onSelectionChange={(keys) => {
                    const key = (keys as Set<string>).values().next().value;
                    setStatusFilter(key || "all");
                  }}
                  classNames={{ trigger: "bg-custom-card border-custom-border" }}
                >
                  {(["all", "pending", "approved", "rejected"] as (RequestStatus | "all")[]).map((status) => (
                    <SelectItem key={status} textValue={status}>
                      {status === "all" ? "All Statuses" : status.charAt(0).toUpperCase() + status.slice(1)}
                    </SelectItem>
                  ))}
                </Select>

                {/* Type Select */}
                <Select
                  label="Type"
                  selectedKeys={[typeFilter] as any}
                  onSelectionChange={(keys) => {
                    const key = (keys as Set<string>).values().next().value;
                    setTypeFilter(key || "all");
                  }}
                  classNames={{ trigger: "bg-custom-card border-custom-border" }}
                >
                  {(["all", "leave", "loan", "overtime", "other"] as (RequestType | "all")[]).map((type) => (
                    <SelectItem key={type} textValue={type}>
                      {type === "all" ? "All Types" : type.charAt(0).toUpperCase() + type.slice(1)}
                    </SelectItem>
                  ))}
                </Select>

                {/* Action Buttons */}
                <Button
                  color="danger"
                  variant="flat"
                  onPress={() => {
                    setStatusFilter("all");
                    setTypeFilter("all");
                    setSearchQuery("");
                    setShowFilters(false);
                  }}
                >
                  Reset Filters
                </Button>
                <Button color="primary" onPress={() => setShowFilters(false)}>
                  Apply Filters
                </Button>
              </div>
            )}
          </div>
        </CardBody>
      </Card>

      {/* Requests Table */}
      <Card className="bg-custom-card border-custom-border">
        <Table aria-label="Request List static collection table" removeWrapper>
          <TableHeader>
            {/* DATE */}
            <TableColumn
              onClick={() => {
                setSortBy("date");
                setSortDirection((prev) => (sortBy === "date" ? (prev === "asc" ? "desc" : "asc") : "desc"));
              }}
              className="cursor-pointer"
            >
              <div className="flex items-center gap-1">
                DATE
                {sortBy === "date" && (
                  <Icon icon={sortDirection === "asc" ? "lucide:arrow-up" : "lucide:arrow-down"} className="w-3 h-3" />
                )}
              </div>
            </TableColumn>
            {/* TYPE */}
            <TableColumn
              onClick={() => {
                setSortBy("type");
                setSortDirection((prev) => (sortBy === "type" ? (prev === "asc" ? "desc" : "asc") : "asc"));
              }}
              className="cursor-pointer"
            >
              <div className="flex items-center gap-1">
                TYPE
                {sortBy === "type" && (
                  <Icon icon={sortDirection === "asc" ? "lucide:arrow-up" : "lucide:arrow-down"} className="w-3 h-3" />
                )}
              </div>
            </TableColumn>
            {/* TITLE */}
            <TableColumn
              onClick={() => {
                setSortBy("title");
                setSortDirection((prev) => (sortBy === "title" ? (prev === "asc" ? "desc" : "asc") : "asc"));
              }}
              className="cursor-pointer"
            >
              <div className="flex items-center gap-1">
                TITLE
                {sortBy === "title" && (
                  <Icon icon={sortDirection === "asc" ? "lucide:arrow-up" : "lucide:arrow-down"} className="w-3 h-3" />
                )}
              </div>
            </TableColumn>
            {/* STATUS */}
            <TableColumn
              onClick={() => {
                setSortBy("status");
                setSortDirection((prev) => (sortBy === "status" ? (prev === "asc" ? "desc" : "asc") : "asc"));
              }}
              className="cursor-pointer"
            >
              <div className="flex items-center gap-1">
                STATUS
                {sortBy === "status" && (
                  <Icon icon={sortDirection === "asc" ? "lucide:arrow-up" : "lucide:arrow-down"} className="w-3 h-3" />
                )}
              </div>
            </TableColumn>
            <TableColumn width={200}>ACTIONS</TableColumn>
          </TableHeader>
          <TableBody>
            {filteredRequests.map((request) => (
              <TableRow key={request.id} className="hover:bg-custom-border/20">
                <TableCell>{formatDate(request.createdAt)}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <span className="p-1.5 rounded bg-custom-sidebar text-primary">
                      {getTypeIcon(request.type)}
                    </span>
                    <span className="capitalize">{request.type}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium text-white">{request.title}</div>
                    <div className="text-custom-muted text-sm truncate max-w-xs">
                      {request.description.substring(0, 80)}
                      {request.description.length > 80 ? "..." : ""}
                    </div>
                  </div>
                </TableCell>
                <TableCell>{renderStatusBadge(request.status)}</TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <ButtonGroup>
                      <Button
                        size="sm"
                        variant="flat"
                        color="primary"
                        onPress={() => onViewRequest(request)}
                      >
                        <Icon icon="lucide:eye" className="mr-1" />
                        View
                      </Button>
                      <Dropdown>
                        <DropdownTrigger>
                          <Button size="sm" variant="flat" color="primary">
                            <Icon icon="lucide:more-vertical" />
                          </Button>
                        </DropdownTrigger>
                        <DropdownMenu aria-label="Request Actions">
                          <DropdownItem
                            key="edit"
                            startContent={<Icon icon="lucide:edit" />}
                            description="Modify this request"
                          >
                            Edit Request
                          </DropdownItem>
                          <DropdownItem
                            key="duplicate"
                            startContent={<Icon icon="lucide:copy" />}
                            description="Create a copy"
                          >
                            Duplicate
                          </DropdownItem>
                          <DropdownItem
                            key="delete"
                            className="text-danger"
                            color="danger"
                            startContent={<Icon icon="lucide:trash" />}
                            description="Remove this request"
                          >
                            Delete
                          </DropdownItem>
                        </DropdownMenu>
                      </Dropdown>
                    </ButtonGroup>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>

      {/* Pagination (UI Only) */}
      <Pagination isCompact showControls initialPage={1} total={10} className="mb-4" />
    </div>
  );
};