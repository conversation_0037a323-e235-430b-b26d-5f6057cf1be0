import React from "react";
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  In<PERSON>,
  Textarea,
  Select,
  SelectItem,
  <PERSON>ton,
  DatePicker,
  NumberInput,
  Di<PERSON>r,
  ModalHeader,
  ModalBody,
  ModalFooter
} from "@heroui/react";
import { Icon } from "@iconify/react";
import type { RequestType } from "../types";

interface NewRequestFormProps {
  onCreateRequest: (request: {
    type: RequestType;
    title: string;
    description: string;
    dateRange?: { from: string; to: string };
    amount?: number;
    hours?: number;
    attachments?: { name: string; url: string; type: string }[];
  }) => void;
}

export const NewRequestForm: React.FC<NewRequestFormProps> = ({ onCreateRequest }) => {
  const [requestType, setRequestType] = React.useState<RequestType>("leave");
  const [title, setTitle] = React.useState("");
  const [description, setDescription] = React.useState("");
  const [startDate, setStartDate] = React.useState<Date | null>(null);
  const [endDate, setEndDate] = React.useState<Date | null>(null);
  const [loanAmount, setLoanAmount] = React.useState<number>(0);
  const [overtimeHours, setOvertimeHours] = React.useState<number>(0);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [validationErrors, setValidationErrors] = React.useState<Record<string, string>>({});

  // Set default description based on request type
  React.useEffect(() => {
    switch (requestType) {
      case "leave":
        setDescription("Leave day(s): \nReason for leave: ");
        setTitle("Leave Request");
        break;
      case "loan":
        setDescription("Purpose of loan: ");
        setTitle("Loan Request");
        break;
      case "overtime":
        setDescription("Reason for overtime: ");
        setTitle("Overtime Request");
        break;
      case "other":
        setDescription("");
        setTitle("");
        break;
    }
  }, [requestType]);

  // Form validation
  const validateForm = () => {
    const errors: Record<string, string> = {};
    
    if (!title.trim()) {
      errors.title = "Title is required";
    }
    
    if (!description.trim()) {
      errors.description = "Description is required";
    }
    
    if (requestType === "leave") {
      if (!startDate) {
        errors.startDate = "Start date is required";
      }
      if (!endDate) {
        errors.endDate = "End date is required";
      }
      if (startDate && endDate && startDate > endDate) {
        errors.dateRange = "End date must be after start date";
      }
    }
    
    if (requestType === "loan" && loanAmount <= 0) {
      errors.loanAmount = "Loan amount must be greater than 0";
    }
    
    if (requestType === "overtime" && overtimeHours <= 0) {
      errors.overtimeHours = "Overtime hours must be greater than 0";
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    
    const requestData = {
      type: requestType,
      title,
      description,
      ...(requestType === "leave" && startDate && endDate && {
        dateRange: {
          from: startDate.toISOString(),
          to: endDate.toISOString()
        }
      }),
      ...(requestType === "loan" && {
        amount: loanAmount
      }),
      ...(requestType === "overtime" && {
        hours: overtimeHours
      })
    };
    
    // Simulate API delay
    setTimeout(() => {
      onCreateRequest(requestData);
      setIsSubmitting(false);
    }, 800);
  };

  // Render dynamic form fields based on request type
  const renderTypeSpecificFields = () => {
    switch (requestType) {
      case "leave":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <DatePicker
                label="Start Date"
                placeholder="Select start date"
                value={startDate}
                onChange={setStartDate}
                isInvalid={!!validationErrors.startDate}
                errorMessage={validationErrors.startDate}
                isRequired
              />
            </div>
            <div>
              <DatePicker
                label="End Date"
                placeholder="Select end date"
                value={endDate}
                onChange={setEndDate}
                isInvalid={!!validationErrors.endDate || !!validationErrors.dateRange}
                errorMessage={validationErrors.endDate || validationErrors.dateRange}
                isRequired
              />
            </div>
          </div>
        );
      case "loan":
        return (
          <div className="mb-4">
            <NumberInput
              label="Loan Amount"
              placeholder="Enter amount"
              value={loanAmount}
              onValueChange={setLoanAmount}
              startContent={<div className="pointer-events-none">$</div>}
              isInvalid={!!validationErrors.loanAmount}
              errorMessage={validationErrors.loanAmount}
              isRequired
            />
          </div>
        );
      case "overtime":
        return (
          <div className="mb-4">
            <NumberInput
              label="Overtime Hours"
              placeholder="Enter hours"
              value={overtimeHours}
              onValueChange={setOvertimeHours}
              endContent={<div className="pointer-events-none">hours</div>}
              isInvalid={!!validationErrors.overtimeHours}
              errorMessage={validationErrors.overtimeHours}
              isRequired
            />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <ModalHeader className="flex justify-between">
        <h3 className="text-xl font-semibold">Create New Request</h3>
      </ModalHeader>
      
      <Divider />
      
      <ModalBody>
        <div className="space-y-4">
          <Select
            label="Request Type"
            placeholder="Select request type"
            selectedKeys={[requestType]}
            onChange={(e) => setRequestType(e.target.value as RequestType)}
            variant="bordered"
            isRequired
          >
            <SelectItem 
              key="leave" 
              value="leave"
              startContent={<Icon icon="lucide:calendar" className="text-primary" />}
            >
              Leave Request
            </SelectItem>
            <SelectItem 
              key="loan" 
              value="loan"
              startContent={<Icon icon="lucide:credit-card" className="text-primary" />}
            >
              Loan Request
            </SelectItem>
            <SelectItem 
              key="overtime" 
              value="overtime"
              startContent={<Icon icon="lucide:clock" className="text-primary" />}
            >
              Overtime Request
            </SelectItem>
            <SelectItem 
              key="other" 
              value="other"
              startContent={<Icon icon="lucide:file-text" className="text-primary" />}
            >
              Other Request
            </SelectItem>
          </Select>
          
          {renderTypeSpecificFields()}
          
          <Input
            label="Title"
            placeholder="Request title"
            value={title}
            onValueChange={setTitle}
            variant="bordered"
            isInvalid={!!validationErrors.title}
            errorMessage={validationErrors.title}
            isRequired
          />
          
          <Textarea
            label="Description"
            placeholder="Enter details about your request"
            value={description}
            onValueChange={setDescription}
            variant="bordered"
            minRows={5}
            isInvalid={!!validationErrors.description}
            errorMessage={validationErrors.description}
            isRequired
          />
          
          <div className="border border-dashed border-custom-border rounded-md p-4">
            <div className="flex flex-col items-center justify-center py-6">
              <Icon icon="lucide:upload-cloud" className="text-4xl text-custom-muted mb-2" />
              <p className="text-custom-muted mb-1">Drag & drop files or click to browse</p>
              <p className="text-xs text-custom-muted mb-4">Supported formats: PDF, DOCX, JPG, PNG (Max 5MB)</p>
              <Button 
                variant="flat" 
                color="primary"
                startContent={<Icon icon="lucide:upload" />}
              >
                Upload Attachments
              </Button>
            </div>
          </div>
        </div>
      </ModalBody>
      
      <Divider />
      
      <ModalFooter className="flex justify-end gap-2">
        <Button variant="flat">Cancel</Button>
        <Button 
          color="primary" 
          onPress={handleSubmit}
          isLoading={isSubmitting}
        >
          Submit Request
        </Button>
      </ModalFooter>
    </>
  );
};