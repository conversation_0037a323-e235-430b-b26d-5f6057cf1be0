import React from "react";
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>vider,
  Ava<PERSON>,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Textarea,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure
} from "@heroui/react";
import { Icon } from "@iconify/react";
import type { Request, RequestStatus, StatusHistoryItem } from "../types";
import { format } from 'date-fns';

interface RequestDetailsProps {
  request: Request;
  onClose: () => void;
  onUpdateStatus: (requestId: string, status: RequestStatus, note: string) => void;
  renderStatusBadge: (status: RequestStatus) => React.ReactNode;
}

export const RequestDetails: React.FC<RequestDetailsProps> = ({ 
  request, 
  onClose, 
  onUpdateStatus,
  renderStatusBadge
}) => {
  const [isAdmin, setIsAdmin] = React.useState(true); // In a real app, this would be determined by user role
  const [statusNote, setStatusNote] = React.useState("");
  const [selectedStatus, setSelectedStatus] = React.useState<RequestStatus | null>(null);
  const { isOpen, onOpen, onOpenChange, onClose: closeModal } = useDisclosure();

  // Format date nicely
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (e) {
      return dateString;
    }
  };
  
  // Handle status update
  const handleStatusUpdate = (status: RequestStatus) => {
    setSelectedStatus(status);
    onOpen();
  };
  
  // Submit status update
  const handleSubmitStatusUpdate = () => {
    if (selectedStatus && statusNote.trim()) {
      onUpdateStatus(request.id, selectedStatus, statusNote);
      closeModal();
      setStatusNote("");
      setSelectedStatus(null);
    }
  };

  // Get type icon
  const getTypeIcon = (type: string) => {
    switch (type) {
      case "leave": return <Icon icon="lucide:calendar" />;
      case "loan": return <Icon icon="lucide:credit-card" />;
      case "overtime": return <Icon icon="lucide:clock" />;
      default: return <Icon icon="lucide:file-text" />;
    }
  };
  
  // Get status color
  const getStatusColor = (status: RequestStatus) => {
    switch (status) {
      case "approved": return "text-success";
      case "pending": return "text-warning";
      case "rejected": return "text-danger";
      default: return "text-default";
    }
  };

  return (
    <>
      <Modal isOpen={true} onOpenChange={(open) => { if (!open) onClose(); }} size="5xl" placement="center">
        <ModalContent>
          {(close) => (
            <Card className="bg-custom-card border-custom-border">
              <CardHeader className="flex justify-between items-center">
                <div className="flex items-center gap-3">
                  <Button 
                    isIconOnly 
                    variant="light" 
                    onPress={() => { close(); onClose(); }}
                    className="mr-1"
                  >
                    <Icon icon="lucide:arrow-left" />
                  </Button>
                  <h3 className="text-xl font-semibold">{request.title}</h3>
                  {renderStatusBadge(request.status)}
                </div>
                
                {isAdmin && (
                  <Dropdown>
                    <DropdownTrigger>
                      <Button 
                        variant="flat"
                        color="primary"
                      >
                        Update Status
                      </Button>
                    </DropdownTrigger>
                    <DropdownMenu aria-label="Update Status Options">
                      <DropdownItem 
                        key="approved" 
                        description="Mark this request as approved"
                        startContent={<Icon icon="lucide:check-circle" className="text-success" />}
                        onPress={() => handleStatusUpdate("approved")}
                      >
                        Approve Request
                      </DropdownItem>
                      <DropdownItem 
                        key="rejected" 
                        description="Mark this request as rejected"
                        startContent={<Icon icon="lucide:x-circle" className="text-danger" />}
                        onPress={() => handleStatusUpdate("rejected")}
                      >
                        Reject Request
                      </DropdownItem>
                      <DropdownItem 
                        key="pending" 
                        description="Mark this request as pending"
                        startContent={<Icon icon="lucide:clock" className="text-warning" />}
                        onPress={() => handleStatusUpdate("pending")}
                      >
                        Set as Pending
                      </DropdownItem>
                    </DropdownMenu>
                  </Dropdown>
                )}
              </CardHeader>
              
              <Divider />
              
              <CardBody>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="md:col-span-2 space-y-6">
                    <div>
                      <h4 className="text-lg font-medium mb-3">Request Details</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex gap-4">
                          <div className="flex items-center gap-2">
                            <span className="text-custom-muted text-xs">Type:</span>
                            <div className="flex items-center gap-1">
                              <span className="p-1 rounded bg-custom-sidebar text-primary">
                                {getTypeIcon(request.type)}
                              </span>
                              <span className="capitalize">{request.type}</span>
                            </div>
                          </div>
                          
                          <div>
                            <span className="text-custom-muted text-xs">Submitted:</span>
                            <span className="ml-2 text-sm">{formatDate(request.createdAt)}</span>
                          </div>
                        </div>
                        
                        {request.type === "leave" && request.dateRange && (
                          <div>
                            <span className="text-custom-muted text-xs">Leave Period:</span>
                            <span className="ml-2">
                              {formatDate(request.dateRange.from)} - {formatDate(request.dateRange.to)}
                            </span>
                          </div>
                        )}
                        
                        {request.type === "loan" && request.amount && (
                          <div>
                            <span className="text-custom-muted text-xs">Loan Amount:</span>
                            <span className="ml-2">${request.amount.toLocaleString()}</span>
                          </div>
                        )}
                        
                        {request.type === "overtime" && request.hours && (
                          <div>
                            <span className="text-custom-muted text-xs">Overtime Hours:</span>
                            <span className="ml-2">{request.hours} hours</span>
                          </div>
                        )}
                        
                        <div>
                          <h5 className="text-custom-muted mb-2 text-xs">Description:</h5>
                          <div className="bg-custom-sidebar p-3 rounded-md whitespace-pre-line text-sm">
                            {request.description}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {request.attachments && request.attachments.length > 0 && (
                      <div>
                        <h4 className="text-lg font-medium mb-3">Attachments</h4>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                          {request.attachments.map((attachment, index) => (
                            <Card key={index} className="border border-custom-border">
                              <CardBody className="flex items-center gap-3 p-3">
                                <div className="p-2 rounded bg-custom-sidebar">
                                  <Icon 
                                    icon={
                                      attachment.type.includes("image") 
                                        ? "lucide:image" 
                                        : attachment.type.includes("pdf") 
                                        ? "lucide:file-text"
                                        : "lucide:file"
                                    } 
                                    className="text-xl text-primary"
                                  />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="text-sm font-medium truncate">{attachment.name}</div>
                                </div>
                                <Button 
                                  isIconOnly 
                                  variant="light"
                                  size="sm"
                                >
                                  <Icon icon="lucide:download" />
                                </Button>
                              </CardBody>
                            </Card>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <h4 className="text-lg font-medium mb-3">Status History</h4>
                    <div className="relative pl-6 border-l border-custom-border">
                      {request.statusHistory.map((historyItem: StatusHistoryItem, index) => (
                        <div key={index} className="mb-6">
                          <div className="absolute -left-2 mt-1">
                            <div className={`rounded-full p-1 ${getStatusColor(historyItem.status)} bg-opacity-20`}>
                              {historyItem.status === "approved" ? (
                                <Icon icon="lucide:check" className={getStatusColor(historyItem.status)} width={12} height={12} />
                              ) : historyItem.status === "rejected" ? (
                                <Icon icon="lucide:x" className={getStatusColor(historyItem.status)} width={12} height={12} />
                              ) : (
                                <Icon icon="lucide:clock" className={getStatusColor(historyItem.status)} width={12} height={12} />
                              )}
                            </div>
                          </div>
                          <div>
                            <div className="flex justify-between">
                              <span className="capitalize font-medium">
                                {historyItem.status}
                              </span>
                              <span className="text-xs text-custom-muted">
                                {formatDate(historyItem.date)}
                              </span>
                            </div>
                            <p className="text-sm mt-1">{historyItem.note}</p>
                            {historyItem.updatedBy !== "system" && (
                              <div className="mt-2 flex items-center gap-2 text-xs text-custom-muted">
                                <Icon icon="lucide:user" width={12} height={12} />
                                <span>Updated by {historyItem.updatedBy}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardBody>
              
              <Divider />
              
              <CardFooter className="flex justify-between">
                <Button 
                  variant="flat" 
                  startContent={<Icon icon="lucide:arrow-left" />}
                  onPress={() => { close(); onClose(); }}
                >
                  Back to Requests
                </Button>
                
                {isAdmin && (
                  <Button 
                    color="primary"
                    startContent={<Icon icon="lucide:edit" />}
                  >
                    Add Comment
                  </Button>
                )}
              </CardFooter>
            </Card>
          )}
        </ModalContent>
      </Modal>
      
      {/* Status Update Modal */}
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} placement="center">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                {selectedStatus === "approved"
                  ? "Approve Request"
                  : selectedStatus === "rejected"
                  ? "Reject Request"
                  : "Update Status"}
              </ModalHeader>
              <ModalBody>
                <p className="text-custom-muted mb-2">
                  Please provide a reason for this status update:
                </p>
                <Textarea
                  placeholder="Enter a note for this status change..."
                  value={statusNote}
                  onValueChange={setStatusNote}
                  variant="bordered"
                  minRows={3}
                />
              </ModalBody>
              <ModalFooter>
                <Button variant="flat" onPress={onClose}>
                  Cancel
                </Button>
                <Button 
                  color={
                    selectedStatus === "approved"
                      ? "success"
                      : selectedStatus === "rejected"
                      ? "danger"
                      : "primary"
                  }
                  onPress={handleSubmitStatusUpdate}
                  isDisabled={!statusNote.trim()}
                >
                  Update Status
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};