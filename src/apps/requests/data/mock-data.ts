import type { Request } from "../types";

export const mockRequests: Request[] = [
  {
    id: "req-001",
    type: "leave",
    title: "Annual Leave Request",
    description: "Leave day(s): December 24-31, 2023\nReason for leave: End of year family vacation",
    status: "approved",
    createdAt: "2023-12-01T10:30:00.000Z",
    statusHistory: [
      {
        status: "pending",
        date: "2023-12-01T10:30:00.000Z",
        note: "Request submitted and pending review.",
        updatedBy: "system"
      },
      {
        status: "approved",
        date: "2023-12-02T14:45:00.000Z",
        note: "Leave request approved. Enjoy your vacation!",
        updatedBy: "Admin User"
      }
    ],
    dateRange: {
      from: "2023-12-24T00:00:00.000Z",
      to: "2023-12-31T00:00:00.000Z"
    },
    createdBy: {
      id: "user-1",
      name: "<PERSON>",
      avatar: "https://img.heroui.chat/image/avatar?w=40&h=40&u=user1"
    }
  },
  {
    id: "req-002",
    type: "loan",
    title: "Salary Advance Request",
    description: "Purpose of loan: Home repair emergency due to water damage.",
    status: "pending",
    createdAt: "2023-11-15T09:20:00.000Z",
    statusHistory: [
      {
        status: "pending",
        date: "2023-11-15T09:20:00.000Z",
        note: "Request submitted and pending review.",
        updatedBy: "system"
      }
    ],
    amount: 2500,
    createdBy: {
      id: "user-1",
      name: "John Doe",
      avatar: "https://img.heroui.chat/image/avatar?w=40&h=40&u=user1"
    }
  },
  {
    id: "req-003",
    type: "overtime",
    title: "Weekend Project Completion",
    description: "Reason for overtime: Need to complete the project delivery before Monday deadline.",
    status: "rejected",
    createdAt: "2023-10-20T16:45:00.000Z",
    statusHistory: [
      {
        status: "pending",
        date: "2023-10-20T16:45:00.000Z",
        note: "Request submitted and pending review.",
        updatedBy: "system"
      },
      {
        status: "rejected",
        date: "2023-10-21T11:30:00.000Z",
        note: "Overtime not approved as project deadline has been extended by 1 week.",
        updatedBy: "Admin User"
      }
    ],
    hours: 8,
    createdBy: {
      id: "user-1",
      name: "John Doe",
      avatar: "https://img.heroui.chat/image/avatar?w=40&h=40&u=user1"
    }
  },
  {
    id: "req-004",
    type: "leave",
    title: "Sick Leave Request",
    description: "Leave day(s): November 10-12, 2023\nReason for leave: Down with flu and doctor recommended rest.",
    status: "approved",
    createdAt: "2023-11-10T08:15:00.000Z",
    statusHistory: [
      {
        status: "pending",
        date: "2023-11-10T08:15:00.000Z",
        note: "Request submitted and pending review.",
        updatedBy: "system"
      },
      {
        status: "approved",
        date: "2023-11-10T09:30:00.000Z",
        note: "Sick leave approved. Get well soon!",
        updatedBy: "Admin User"
      }
    ],
    dateRange: {
      from: "2023-11-10T00:00:00.000Z",
      to: "2023-11-12T00:00:00.000Z"
    },
    attachments: [
      {
        name: "medical_certificate.pdf",
        url: "#",
        type: "application/pdf"
      }
    ],
    createdBy: {
      id: "user-1",
      name: "John Doe",
      avatar: "https://img.heroui.chat/image/avatar?w=40&h=40&u=user1"
    }
  },
  {
    id: "req-005",
    type: "other",
    title: "Equipment Request - New Laptop",
    description: "Requesting a new laptop as current one is showing performance issues and is over 4 years old. Needed for development work on new project.",
    status: "approved",
    createdAt: "2023-09-05T14:20:00.000Z",
    statusHistory: [
      {
        status: "pending",
        date: "2023-09-05T14:20:00.000Z",
        note: "Request submitted and pending review.",
        updatedBy: "system"
      },
      {
        status: "approved",
        date: "2023-09-07T10:15:00.000Z",
        note: "Equipment request approved. IT department will contact you to arrange delivery.",
        updatedBy: "Admin User"
      }
    ],
    createdBy: {
      id: "user-1",
      name: "John Doe",
      avatar: "https://img.heroui.chat/image/avatar?w=40&h=40&u=user1"
    }
  },
  {
    id: "req-006",
    type: "loan",
    title: "Education Loan Request",
    description: "Purpose of loan: To pay for advanced certification course in project management.",
    status: "rejected",
    createdAt: "2023-08-15T11:45:00.000Z",
    statusHistory: [
      {
        status: "pending",
        date: "2023-08-15T11:45:00.000Z",
        note: "Request submitted and pending review.",
        updatedBy: "system"
      },
      {
        status: "rejected",
        date: "2023-08-17T15:30:00.000Z",
        note: "Loan request rejected due to budget constraints this quarter.",
        updatedBy: "Admin User"
      },
      {
        status: "approved",
        date: "2023-08-20T09:15:00.000Z",
        note: "After reconsideration, loan approved with revised terms. Please see HR for details.",
        updatedBy: "Finance Director"
      }
    ],
    amount: 3000,
    createdBy: {
      id: "user-1",
      name: "John Doe",
      avatar: "https://img.heroui.chat/image/avatar?w=40&h=40&u=user1"
    }
  },
  {
    id: "req-007",
    type: "overtime",
    title: "System Migration Support",
    description: "Reason for overtime: Need to support the system migration scheduled for this weekend.",
    status: "approved",
    createdAt: "2023-10-02T09:10:00.000Z",
    statusHistory: [
      {
        status: "pending",
        date: "2023-10-02T09:10:00.000Z",
        note: "Request submitted and pending review.",
        updatedBy: "system"
      },
      {
        status: "approved",
        date: "2023-10-03T14:25:00.000Z",
        note: "Overtime approved for the weekend system migration.",
        updatedBy: "Admin User"
      }
    ],
    hours: 12,
    createdBy: {
      id: "user-1",
      name: "John Doe",
      avatar: "https://img.heroui.chat/image/avatar?w=40&h=40&u=user1"
    }
  }
];