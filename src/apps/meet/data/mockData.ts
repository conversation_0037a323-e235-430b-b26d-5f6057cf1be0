import type { Call, CallType, CallStatus } from "../types";

    // <PERSON><PERSON> calls data
    export const mockCalls: Call[] = [
      {
        id: "call-1",
        title: "Weekly Team Standup",
        type: "video",
        status: "active",
        date: new Date(),
        participants: [
          { id: "user1", name: "<PERSON>", isHost: true },
          { id: "user2", name: "<PERSON>", role: "Product Manager" },
          { id: "user3", name: "<PERSON>", role: "Frontend Developer" },
          { id: "user4", name: "<PERSON>", role: "Designer" }
        ],
        hasRecording: false
      },
      {
        id: "call-2",
        title: "Product Design Review",
        type: "video",
        status: "scheduled",
        date: new Date(Date.now() + 2 * 3600000), // 2 hours from now
        participants: [
          { id: "user2", name: "<PERSON>", isHost: true },
          { id: "user4", name: "<PERSON>", role: "Designer" },
          { id: "user5", name: "<PERSON>", role: "UX Researcher" }
        ],
        hasRecording: false,
        meetingId: "meet-abc-123-xyz",
        joinLink: "https://meet.example.com/abc-xyz-123"
      },
      {
        id: "call-3",
        title: "Client Presentation",
        type: "video",
        status: "completed",
        date: new Date(Date.now() - 1 * 86400000), // 1 day ago
        duration: 3540, // 59 minutes
        participants: [
          { id: "user1", name: "<PERSON> <PERSON>", isHost: true },
          { id: "user2", name: "<PERSON> <PERSON>" },
          { id: "user6", name: "<PERSON> <PERSON>", role: "Client" },
          { id: "user7", name: "<PERSON> <PERSON>", role: "Client" }
        ],
        hasRecording: true,
        summary: `Meeting with client to present the new dashboard design.

Key discussion points:
- Walked through the new analytics dashboard design
- Client appreciated the improved data visualization
- Requested minor tweaks to the filtering system
- Discussed timeline for implementation

Action items:
1. Update designs based on feedback by Friday
2. Schedule technical follow-up with client's IT team
3. Prepare cost estimate for additional features requested`
      },
      {
        id: "call-4",
        title: "Quick Sync with Mike",
        type: "audio",
        status: "completed",
        date: new Date(Date.now() - 5 * 3600000), // 5 hours ago
        duration: 845, // 14 minutes 5 seconds
        participants: [
          { id: "user1", name: "John Smith" },
          { id: "user3", name: "Mike Williams" }
        ],
        hasRecording: true,
        summary: `Quick sync with Mike to discuss frontend implementation timeline.

Discussed:
- Current progress on the user profile component
- Challenges with the new API integration
- Revised timeline for the next sprint

Mike will follow up with backend team about API changes needed.`
      },
      {
        id: "call-5",
        title: "Marketing Strategy",
        type: "video",
        status: "scheduled",
        date: new Date(Date.now() + 1 * 86400000), // 1 day from now
        participants: [
          { id: "user8", name: "Alex Turner", isHost: true },
          { id: "user9", name: "Lisa Wang" },
          { id: "user10", name: "Robert Johnson" }
        ],
        hasRecording: false,
        meetingId: "meet-def-456-uvw",
        joinLink: "https://meet.example.com/def-456-uvw"
      },
      {
        id: "call-6",
        title: "Budget Review Q3",
        type: "video",
        status: "completed",
        date: new Date(Date.now() - 2 * 86400000), // 2 days ago
        duration: 5400, // 1 hour 30 minutes
        participants: [
          { id: "user11", name: "Jennifer Adams", isHost: true, role: "Finance Director" },
          { id: "user1", name: "John Smith" },
          { id: "user12", name: "Daniel Wilson", role: "CFO" }
        ],
        hasRecording: true,
        summary: `Quarterly budget review meeting for Q3.

Key points:
- Reviewed Q3 financial performance across departments
- Marketing expenses exceeded budget by 12% due to new campaign
- Development team under budget by 8%
- Discussed reallocation of resources for Q4

Action items:
1. Finance to send detailed breakdown by department
2. All teams to submit Q4 budget requests by next Friday
3. Schedule follow-up with marketing to review ROI on campaign`
      },
      {
        id: "call-7",
        title: "Call with Sarah",
        type: "audio",
        status: "missed",
        date: new Date(Date.now() - 1 * 3600000), // 1 hour ago
        participants: [
          { id: "user1", name: "John Smith" },
          { id: "user2", name: "Sarah Johnson" }
        ],
        hasRecording: false
      }
    ];