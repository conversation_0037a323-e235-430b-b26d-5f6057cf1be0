import React from "react";
import { MeetDetails } from "./components/MeetDetails";
import type { Call } from "./types";

interface MeetAppProps {
  call: Call | null;
  onSelectCall: (call: Call | null) => void;
}

export const MeetApp: React.FC<MeetAppProps> = ({ call, onSelectCall }) => {
  return (
    <div className="h-full overflow-y-auto flex flex-col">
      <MeetDetails 
        call={call}
        onClose={() => onSelectCall(null)}
      />
    </div>
  );
};