import React from "react";
import { Avatar, AvatarGroup, Tooltip, Chip } from "@heroui/react";
import { Icon } from "@iconify/react";
import { motion } from "framer-motion";
import type { Call } from "../types";

interface CallListProps {
  calls: Call[];
  selectedCallId?: string;
  onSelectCall: (call: Call) => void;
}

export const CallList: React.FC<CallListProps> = ({ 
  calls, 
  selectedCallId, 
  onSelectCall 
}) => {
  // Format call duration from seconds
  const formatDuration = (seconds: number) => {
    if (!seconds) return '';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    }
    return `${secs}s`;
  };
  
  // Format call time
  const formatTime = (date: Date) => {
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    
    if (isToday) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday = date.toDateString() === yesterday.toDateString();
    
    if (isYesterday) {
      return 'Yesterday';
    }
    
    // If it's within the last 7 days, show the day name
    const daysDiff = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    if (daysDiff < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    }
    
    // Otherwise show the date
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  };

  if (calls.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-40 text-custom-muted p-4">
        <Icon icon="lucide:phone-off" className="text-3xl mb-2" />
        <p className="text-center">No calls found matching your search</p>
      </div>
    );
  }

  return (
    <div>
      {calls.map((call) => (
        <motion.div
          key={call.id}
          whileHover={{ backgroundColor: "rgba(42, 45, 60, 0.3)" }}
          className={`p-4 border-b border-custom-border cursor-pointer
            ${selectedCallId === call.id ? 'bg-custom-border/20' : ''}
          `}
          onClick={() => onSelectCall(call)}
        >
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <div 
                className={`w-8 h-8 rounded-full flex items-center justify-center mr-3
                  ${call.type === 'video' ? 'bg-primary/20 text-primary' : 'bg-success/20 text-success'}
                `}
              >
                <Icon 
                  icon={call.type === 'video' ? 'lucide:video' : 'lucide:phone'} 
                  className="text-lg" 
                />
              </div>
              <div>
                <h3 className="text-white text-sm font-medium">{call.title}</h3>
                <div className="flex items-center text-xs text-custom-muted mt-1">
                  <span>{formatTime(call.date)}</span>
                  {call.status === 'completed' && (
                    <span className="ml-2">• {formatDuration(call.duration || 0)}</span>
                  )}
                </div>
              </div>
            </div>
            
          </div>
          
          <div className="flex items-center justify-between">
            <AvatarGroup isGrid max={7} size="sm" className="justify-start gap-1">
              {call.participants.map((participant) => (
                <Tooltip key={participant.id} content={participant.name}>
                  <Avatar
                    src={participant.avatar || `https://img.heroui.chat/image/avatar?w=200&h=200&u=${participant.id}`}
                  />
                </Tooltip>
              ))}
            </AvatarGroup>
            

          </div>
        </motion.div>
      ))}
    </div>
  );
};