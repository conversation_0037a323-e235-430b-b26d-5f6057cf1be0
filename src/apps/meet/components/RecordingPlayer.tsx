import React, { useState, useRef } from "react";
    import { <PERSON>, CardBody, <PERSON><PERSON>, Slide<PERSON>, Toolt<PERSON> } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import type { Call } from "../types";

    interface RecordingPlayerProps {
      call: Call;
    }

    export const RecordingPlayer: React.FC<RecordingPlayerProps> = ({ call }) => {
      const [isPlaying, setIsPlaying] = useState(false);
      const [currentTime, setCurrentTime] = useState(0);
      const [volume, setVolume] = useState(70);
      const [showVolumeControl, setShowVolumeControl] = useState(false);
      
      // Format time from seconds to MM:SS
      const formatTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
        const secs = Math.floor(seconds % 60).toString().padStart(2, '0');
        return `${mins}:${secs}`;
      };
      
      // Calculate total duration of the recording
      const totalDuration = call.duration || 0;
      
      // Toggle play/pause
      const togglePlayback = () => {
        setIsPlaying(!isPlaying);
        // In a real app, you would control the actual media playback here
      };
      
      // Handle seek in recording
      const handleSeek = (value: number) => {
        setCurrentTime(value);
        // In a real app, you would seek the actual media playback here
      };
      
      // Handle volume change
      const handleVolumeChange = (value: number) => {
        setVolume(value);
        // In a real app, you would change the actual media volume here
      };
      
      return (
        <Card className="bg-custom-card border-custom-border">
          <CardBody>
            <h2 className="text-white font-medium mb-4">Recording</h2>
            
            {call.type === 'video' ? (
              <div className="relative bg-custom-sidebar rounded-lg aspect-video mb-4 flex items-center justify-center">
                <img 
                  src={`https://img.heroui.chat/image/dashboard?w=800&h=450&u=${call.id}`}
                  alt="Video thumbnail" 
                  className="w-full h-full object-cover rounded-lg"
                />
                <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-lg">
                  <Button 
                    isIconOnly 
                    color="primary" 
                    size="lg"
                    className="w-16 h-16 rounded-full"
                    onPress={togglePlayback}
                  >
                    <Icon icon={isPlaying ? "lucide:pause" : "lucide:play"} className="text-3xl" />
                  </Button>
                </div>
              </div>
            ) : (
              <div className="bg-custom-sidebar rounded-lg p-6 mb-4 flex items-center">
                <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center mr-6">
                  <Icon icon="lucide:mic" className="text-3xl text-primary" />
                </div>
                <div>
                  <p className="text-white font-medium">{call.title}</p>
                  <p className="text-custom-muted text-sm">Audio Recording • {formatTime(totalDuration)}</p>
                </div>
                <Button 
                  isIconOnly 
                  color={isPlaying ? "danger" : "primary"}
                  size="lg"
                  className="ml-auto"
                  onPress={togglePlayback}
                >
                  <Icon icon={isPlaying ? "lucide:pause" : "lucide:play"} className="text-lg" />
                </Button>
              </div>
            )}
            
            <div className="flex items-center mb-6">
              <span className="text-custom-muted text-xs mr-2">
                {formatTime(currentTime)}
              </span>
              <Slider
                aria-label="Playback progress"
                value={currentTime}
                onChange={handleSeek}
                max={totalDuration}
                step={1}
                className="flex-1 mx-2"
                color="primary"
              />
              <span className="text-custom-muted text-xs ml-2">
                {formatTime(totalDuration)}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Button 
                  isIconOnly 
                  variant="light" 
                  className="text-custom-text"
                  onPress={() => setCurrentTime(Math.max(0, currentTime - 10))}
                >
                  <Icon icon="lucide:rewind" />
                </Button>
                <Button 
                  isIconOnly 
                  variant={isPlaying ? "solid" : "flat"}
                  color={isPlaying ? "danger" : "primary"}
                  className="mx-2"
                  onPress={togglePlayback}
                >
                  <Icon icon={isPlaying ? "lucide:pause" : "lucide:play"} />
                </Button>
                <Button 
                  isIconOnly 
                  variant="light" 
                  className="text-custom-text"
                  onPress={() => setCurrentTime(Math.min(totalDuration, currentTime + 10))}
                >
                  <Icon icon="lucide:fast-forward" />
                </Button>
                
                <div className="relative ml-4">
                  <Button 
                    isIconOnly 
                    variant="light" 
                    className="text-custom-text"
                    onPress={() => setShowVolumeControl(!showVolumeControl)}
                  >
                    <Icon icon={
                      volume === 0 ? "lucide:volume-x" :
                      volume < 30 ? "lucide:volume" :
                      volume < 70 ? "lucide:volume-1" : 
                      "lucide:volume-2"
                    } />
                  </Button>
                  
                  {showVolumeControl && (
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 bg-custom-card p-3 rounded-lg border border-custom-border shadow-lg">
                      <Slider
                        aria-label="Volume"
                        orientation="vertical"
                        value={volume}
                        onChange={handleVolumeChange}
                        className="h-24"
                        color="primary"
                      />
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex items-center">
                <Button 
                  variant="flat"
                  startContent={<Icon icon="lucide:download" />}
                >
                  Download
                </Button>
                
                <Tooltip content="Playback Speed">
                  <Button 
                    isIconOnly 
                    variant="light" 
                    className="text-custom-text ml-2"
                  >
                    <span className="text-sm font-medium">1x</span>
                  </Button>
                </Tooltip>
              </div>
            </div>
          </CardBody>
        </Card>
      );
    };