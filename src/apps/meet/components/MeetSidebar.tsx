import React, { useState } from "react";
    import { Input, <PERSON><PERSON>, Ta<PERSON>, Tab } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import type { Call } from "../types";
    import { mockCalls } from "../data/mockData";
    import { CallList } from "./CallList";

    interface MeetSidebarProps {
      selectedCallId?: string;
      onSelectCall: (call: Call) => void;
    }

    export const MeetSidebar: React.FC<MeetSidebarProps> = ({ 
      selectedCallId,
      onSelectCall 
    }) => {
      const [searchQuery, setSearchQuery] = useState("");
      const [filter, setFilter] = useState<'all' | 'video' | 'audio' | 'scheduled'>('all');
      
      // Filter calls based on search and filter type
      const filteredCalls = mockCalls.filter(call => {
        // Apply search filter
        if (searchQuery) {
          const query = searchQuery.toLowerCase();
          const matchesTitle = call.title.toLowerCase().includes(query);
          const matchesParticipant = call.participants.some(p => 
            p.name.toLowerCase().includes(query)
          );
          if (!matchesTitle && !matchesParticipant) return false;
        }
        
        // Apply call type filter
        if (filter === 'video' && call.type !== 'video') return false;
        if (filter === 'audio' && call.type !== 'audio') return false;
        if (filter === 'scheduled' && call.status !== 'scheduled') return false;
        
        return true;
      });
      
      return (
        <div className="flex flex-col h-full">
          <div className="p-4 border-b border-custom-border">
            
            <div className="mt-0">
              <Input
                placeholder="Search calls..."
                value={searchQuery}
                onValueChange={setSearchQuery}
                startContent={<Icon icon="lucide:search" className="text-custom-muted" />}
                endContent={
                  searchQuery ? (
                    <Button 
                      isIconOnly 
                      variant="light" 
                      size="sm" 
                      className="text-custom-muted"
                      onPress={() => setSearchQuery("")}
                    >
                      <Icon icon="lucide:x" className="text-xs" />
                    </Button>
                  ) : null
                }
                classNames={{
                  inputWrapper: "bg-custom-sidebar border-custom-border",
                }}
                radius="lg"
                variant="bordered"
              />
            </div>
            

          </div>
          
          
          <div className="flex-1 overflow-y-auto custom-scrollbar pr-2">
            <CallList 
              calls={filteredCalls} 
              selectedCallId={selectedCallId}
              onSelectCall={onSelectCall}
            />
          </div>
        </div>
      );
    };