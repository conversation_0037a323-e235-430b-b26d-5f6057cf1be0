import React from "react";
import { <PERSON>, CardBody, Button, Avatar, Too<PERSON><PERSON>, <PERSON>, Divider } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import type { Call } from "../types";

    interface MeetingInfoProps {
      call: Call;
    }

    export const MeetingInfo: React.FC<MeetingInfoProps> = ({ call }) => {
      // Format call duration from seconds to HH:MM:SS
      const formatDuration = (seconds: number) => {
        if (!seconds) return '00:00:00';
        const hours = Math.floor(seconds / 3600).toString().padStart(2, '0');
        const minutes = Math.floor((seconds % 3600) / 60).toString().padStart(2, '0');
        const secs = (seconds % 60).toString().padStart(2, '0');
        return `${hours}:${minutes}:${secs}`;
      };
      
      // Format date to full display
      const formatDate = (date: Date) => {
        return date.toLocaleString(undefined, {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      };
      
      return (
        <div className="space-y-6">
          <Card className="bg-custom-card border-custom-border">
            <CardBody>
              <h2 className="text-white font-medium mb-4">Meeting Details</h2>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full flex items-center justify-center bg-custom-sidebar mr-3">
                      <Icon 
                        icon={call.type === 'video' ? 'lucide:video' : 'lucide:phone'} 
                        className={`text-lg ${call.type === 'video' ? 'text-primary' : 'text-success'}`}
                      />
                    </div>
                    <div>
                      <p className="text-white">{call.type === 'video' ? 'Video Call' : 'Audio Call'}</p>
                      <p className="text-custom-muted text-xs">Type</p>
                    </div>
                  </div>
                  
              <Chip 
                    color={
                      call.status === 'active' ? 'success' : 
                      call.status === 'scheduled' ? 'warning' : 
                      call.status === 'missed' ? 'danger' : 
                      'default'
                    }
                    variant="flat"
                    size="sm"
                    className="capitalize"
                  >
                    {call.status}
              </Chip>
                </div>
                
                <Divider className="my-4" />
                
                <div>
                  <p className="text-custom-muted text-xs mb-1">Date & Time</p>
                  <p className="text-white">{formatDate(call.date)}</p>
                </div>
                
                {call.status === 'completed' && (
                  <>
                    <Divider className="my-4" />
                    <div>
                      <p className="text-custom-muted text-xs mb-1">Duration</p>
                      <p className="text-white">{formatDuration(call.duration || 0)}</p>
                    </div>
                  </>
                )}
                
                {call.meetingId && (
                  <>
                    <Divider className="my-4" />
                    <div>
                      <p className="text-custom-muted text-xs mb-1">Meeting ID</p>
                      <div className="flex items-center">
                        <p className="text-white mr-3">{call.meetingId}</p>
                        <Button 
                          size="sm" 
                          variant="light" 
                          isIconOnly 
                          className="text-custom-text"
                        >
                          <Icon icon="lucide:copy" className="text-sm" />
                        </Button>
                      </div>
                    </div>
                  </>
                )}
                
                {call.status === 'scheduled' && call.joinLink && (
                  <>
                    <Divider className="my-4" />
                    <div>
                      <p className="text-custom-muted text-xs mb-1">Join Link</p>
                      <div className="flex items-center">
                        <p className="text-primary truncate mr-3 text-sm">{call.joinLink}</p>
                        <Button 
                          size="sm" 
                          variant="light" 
                          isIconOnly 
                          className="text-custom-text"
                        >
                          <Icon icon="lucide:copy" className="text-sm" />
                        </Button>
                      </div>
                    </div>
                  </>
                )}
              </div>
              
              {(call.status === 'active' || call.status === 'scheduled') && (
                <>
                  <Divider className="my-6" />
                  
                  <div className="flex flex-wrap gap-2">
                    <Button 
                      color="primary" 
                      className="flex-1"
                      startContent={<Icon icon={call.type === 'video' ? 'lucide:video' : 'lucide:phone'} />}
                    >
                      {call.status === 'active' ? 'Join Now' : 'Start Meeting'}
                    </Button>
                    
                    {call.status === 'scheduled' && (
                      <Button 
                        variant="bordered" 
                        className="flex-1"
                        startContent={<Icon icon="lucide:calendar-plus" />}
                      >
                        Add to Calendar
                      </Button>
                    )}
                  </div>
                </>
              )}

          <Divider className="my-6" />
          
              <h2 className="text-white font-medium mb-4">Participants ({call.participants.length})</h2>
              
              <div className="space-y-3">
                {call.participants.map((participant, index) => (
                  <div 
                    key={participant.id}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center">
                      <Avatar 
                        src={participant.avatar || `https://img.heroui.chat/image/avatar?w=200&h=200&u=${participant.id}`} 
                        className="mr-3"
                      />
                      <div>
                        <p className="text-white">{participant.name}</p>
                        <p className="text-custom-muted text-xs">{participant.role || 'Participant'}</p>
                      </div>
                    </div>
                    
                    {participant.isHost && (
                  <Chip color="primary" variant="flat" size="sm">
                        Host
                  </Chip>
                    )}
                  </div>
                ))}
              </div>
              
              {call.status === 'scheduled' && (
                <Button 
                  variant="flat"
                  startContent={<Icon icon="lucide:user-plus" />}
                  className="w-full mt-4"
                >
                  Invite More Participants
                </Button>
              )}
            </CardBody>
          </Card>
        </div>
      );
    };