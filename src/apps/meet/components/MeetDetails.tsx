import React, { useState } from "react";
import { Card, CardBody, Button, Avatar, Divider, <PERSON>dal, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>dalBody, <PERSON>dal<PERSON>ooter, Input, Select, SelectItem } from "@heroui/react";
import { Icon } from "@iconify/react";
import { motion } from "framer-motion";
import type { Call } from "../types";
import { MeetingInfo } from "./MeetingInfo";
import { MeetingSummary } from "./MeetingSummary";
import { RecordingPlayer } from "./RecordingPlayer";
import type { Project, Task } from "../../project/types";
import { mockProjects, mockTasks } from "../../project/data/mock-data";

interface MeetDetailsProps {
  call: Call | null;
  onClose: () => void;
}

export const MeetDetails: React.FC<MeetDetailsProps> = ({ call, onClose }) => {
  if (!call) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-custom-muted p-6">
        <div className="w-16 h-16 bg-custom-sidebar rounded-full flex items-center justify-center mb-4">
          <Icon icon="lucide:video" className="text-3xl" />
        </div>
        <h3 className="text-white text-lg font-medium mb-2">Select a call</h3>
        <p className="text-center max-w-md">
          Choose a call from the list to see details, summary, and recordings
        </p>
      </div>
    );
  }
  
  const isActiveOrScheduled = call.status === 'active' || call.status === 'scheduled';

  const [editOpen, setEditOpen] = useState(false);
  const [tempTitle, setTempTitle] = useState(call?.title || "");
  
  const [assignProjectOpen, setAssignProjectOpen] = useState(false);
  const [selectedProjectId, setSelectedProjectId] = useState<string>("");
  const [selectedTaskId, setSelectedTaskId] = useState<string>("");
  
  const projectTasks = React.useMemo(() => {
    if (!selectedProjectId) return [];
    return Object.values(mockTasks).filter(
      task => task.project.id === selectedProjectId
    );
  }, [selectedProjectId]);

  const handleSaveTitle = () => {
    // @ts-ignore mutate for demo
    if (call) call.title = tempTitle;
    setEditOpen(false);
  };

  const handleAssignToTask = () => {
    // In a real application, you would save this association to your backend
    console.log(`Meeting ${call?.id} assigned to project ${selectedProjectId} and task ${selectedTaskId}`);
    
    // For demo purposes, we'll just show a success message
    alert(`Meeting successfully assigned to the selected task`);
    setAssignProjectOpen(false);
  };

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <button 
            className="mr-3 md:hidden text-custom-text"
            onClick={onClose}
          >
            <Icon icon="lucide:chevron-left" className="text-xl" />
          </button>
          <div>
            <div className="flex items-center">
              
              <h1 className="text-white text-lg font-medium flex items-center">
                {call.title}
                <Button
                  isIconOnly
                  variant="light"
                  size="sm"
                  className="text-custom-muted ml-2"
                  onPress={() => {
                    setTempTitle(call.title);
                    setEditOpen(true);
                  }}
                >
                  <Icon icon="lucide:pencil" className="w-4 h-4" />
                </Button>
              </h1>
            </div>
            <p className="text-custom-muted text-sm mt-1">
              {call.status === 'active' ? 'Ongoing call' : 
               call.status === 'scheduled' ? `Scheduled for ${call.date.toLocaleString()}` : 
               call.status === 'missed' ? 'Missed call' : 
               `Completed on ${call.date.toLocaleDateString()}`}
            </p>
          </div>
        </div>
        
        <div className="flex gap-2">
          {isActiveOrScheduled && (
            <Button 
              color="primary"
              startContent={<Icon icon={call.type === 'video' ? 'lucide:video' : 'lucide:phone'} />}
              size="sm"
            >
              {call.status === 'active' ? 'Join' : 'Start'}
            </Button>
          )}
          
          <Button
            variant="flat"
            color="default"
            size="sm"
            startContent={<Icon icon="lucide:link" className="w-4 h-4" />}
            onPress={() => setAssignProjectOpen(true)}
          >
            Assign to Task
          </Button>
        </div>
      </div>
      
      <div className="space-y-6">
        <MeetingInfo call={call} />

        {call.status !== 'scheduled' && call.summary && (
          <MeetingSummary call={call} />
        )}

        {call.hasRecording && (
          <RecordingPlayer call={call} />
        )}
      </div>

      {/* Project and Task Assignment Modal */}
      <Modal isOpen={assignProjectOpen} onClose={() => setAssignProjectOpen(false)}>
        <ModalContent className="bg-custom-card text-white">
          <ModalHeader>Assign Meeting to Task</ModalHeader>
          <ModalBody className="gap-4">
            <Select
              label="Select Project"
              placeholder="Choose a project"
              selectedKeys={selectedProjectId ? [selectedProjectId] : []}
              onChange={(e) => {
                setSelectedProjectId(e.target.value);
                setSelectedTaskId(""); // Reset task selection when project changes
              }}
              className="w-full"
            >
              {mockProjects.map((project) => (
                <SelectItem key={project.id} value={project.id}>
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: project.color }}
                    ></div>
                    {project.name}
                  </div>
                </SelectItem>
              ))}
            </Select>
            
            <Select
              label="Select Task"
              placeholder={selectedProjectId ? "Choose a task" : "Select a project first"}
              isDisabled={!selectedProjectId}
              selectedKeys={selectedTaskId ? [selectedTaskId] : []}
              onChange={(e) => setSelectedTaskId(e.target.value)}
              className="w-full"
            >
              {projectTasks.map((task) => (
                <SelectItem key={task.id} value={task.id}>
                  {task.title}
                </SelectItem>
              ))}
            </Select>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={() => setAssignProjectOpen(false)} size="sm">
              Cancel
            </Button>
            <Button 
              color="primary" 
              onPress={handleAssignToTask}
              isDisabled={!selectedProjectId || !selectedTaskId}
              size="sm"
            >
              Assign
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Edit Title Modal */}
      <Modal isOpen={editOpen} onClose={() => setEditOpen(false)}>
        <ModalContent className="bg-custom-card text-white">
          <ModalHeader>Edit Meeting Title</ModalHeader>
          <ModalBody>
            <Input
              fullWidth
              variant="bordered"
              value={tempTitle}
              onValueChange={setTempTitle}
            />
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={() => setEditOpen(false)} size="sm">
              Cancel
            </Button>
            <Button color="primary" onPress={handleSaveTitle} size="sm">
              Save
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};