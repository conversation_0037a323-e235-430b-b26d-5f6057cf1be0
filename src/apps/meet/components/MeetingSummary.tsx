import React, { useState } from "react";
    import { Card, CardBody, Button, Textarea, Tooltip } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import type { Call } from "../types";

    interface MeetingSummaryProps {
      call: Call;
    }

    export const MeetingSummary: React.FC<MeetingSummaryProps> = ({ call }) => {
      const [summary, setSummary] = useState(call.summary || '');
      const [isEditing, setIsEditing] = useState(false);
      const [isRegenerating, setIsRegenerating] = useState(false);
      
      const handleRegenerate = () => {
        // In a real app, you would call an AI service to regenerate
        setIsRegenerating(true);
        
        // Simulate API delay
        setTimeout(() => {
          setSummary(`Regenerated AI summary for the ${call.title} meeting held on ${call.date.toLocaleDateString()}.

Key discussion points:
- Team reviewed progress on the current sprint
- Design team presented new mockups for mobile app
- Backend team reported API completion ahead of schedule
- QA raised concerns about testing resources

Action items:
1. All team leads to submit resource forecasts by Friday
2. Schedule follow-up meeting with design and frontend teams
3. Create testing plan for new features`);
          setIsRegenerating(false);
        }, 2000);
      };
      
      const handleSave = () => {
        // In a real app, you would save this to the backend
        setIsEditing(false);
        // Also update the call object
      };
      
      return (
        <Card className="bg-custom-card border-custom-border">
          <CardBody>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-white font-medium">Meeting Summary</h2>
              
              <div className="flex gap-2">
                <Tooltip content={isEditing ? "Save" : "Edit Summary"}>
                  <Button 
                    isIconOnly 
                    variant="light" 
                    className="text-custom-text"
                    onPress={() => isEditing ? handleSave() : setIsEditing(true)}
                  >
                    <Icon icon={isEditing ? "lucide:save" : "lucide:edit-3"} />
                  </Button>
                </Tooltip>
                
                <Tooltip content="Regenerate with AI">
                  <Button 
                    isIconOnly 
                    variant="light" 
                    className="text-custom-text"
                    onPress={handleRegenerate}
                    isLoading={isRegenerating}
                  >
                    <Icon icon="lucide:refresh-cw" />
                  </Button>
                </Tooltip>
              </div>
            </div>
            
            {isEditing ? (
              <Textarea
                value={summary}
                onValueChange={setSummary}
                minRows={10}
                classNames={{
                  inputWrapper: "bg-custom-sidebar border-custom-border",
                }}
                placeholder="Enter or edit meeting summary..."
              />
            ) : (
              <div className="bg-custom-sidebar p-4 rounded-md whitespace-pre-wrap">
                {summary || "No summary available for this meeting."}
              </div>
            )}
            
            {isRegenerating && (
              <p className="text-custom-muted text-sm mt-4 text-center">
                Analyzing meeting recording and generating summary...
              </p>
            )}
            
            <div className="mt-6 text-right">
              {isEditing && (
                <>
                  <Button
                    variant="flat"
                    className="mr-2"
                    onPress={() => {
                      setIsEditing(false);
                      setSummary(call.summary || ''); // Revert changes
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    color="primary"
                    onPress={handleSave}
                  >
                    Save Changes
                  </Button>
                </>
              )}
            </div>
          </CardBody>
        </Card>
      );
    };