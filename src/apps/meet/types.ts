export type CallType = 'video' | 'audio';
    export type CallStatus = 'active' | 'completed' | 'scheduled' | 'missed';

    export interface Participant {
      id: string;
      name: string;
      avatar?: string;
      role?: string;
      isHost?: boolean;
    }

    export interface Call {
      id: string;
      title: string;
      type: CallType;
      status: CallStatus;
      date: Date;
      duration?: number; // in seconds
      participants: Participant[];
      hasRecording: boolean;
      summary?: string;
      meetingId?: string;
      joinLink?: string;
    }