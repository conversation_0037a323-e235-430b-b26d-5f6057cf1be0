export type MessageStatus = 'sent' | 'delivered' | 'read';

export type ChatType = 'private' | 'group' | 'channel';

export interface ChatMessage {
  id: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  content: string;
  timestamp: Date;
  status: MessageStatus;
  isOutgoing: boolean;
  replyTo?: {
    id: string;
    content: string;
    senderName: string;
  };
  attachments?: {
    type: 'image' | 'video' | 'file' | 'audio';
    url: string;
    name?: string;
    size?: number;
    preview?: string;
  }[];
}

export interface ChatConversation {
  id: string;
  title: string;
  avatar?: string;
  type: ChatType;
  lastMessage?: {
    content: string;
    timestamp: Date;
    senderId: string;
    senderName?: string;
    status: MessageStatus;
  };
  unreadCount: number;
  isOnline?: boolean;
  lastSeen?: Date;
  isTyping?: boolean;
  isPinned?: boolean;
  isMuted?: boolean;
  members?: {
    id: string;
    name: string;
    avatar?: string;
    isOnline?: boolean;
    role?: 'admin' | 'member';
  }[];
  description?: string;
  icon?: string;
}
