import type { ChatConversation, ChatMessage } from "../types";

// Mock conversations data
export const mockConversations: ChatConversation[] = [
  {
    id: "user1",
    title: "<PERSON>",
    type: "private",
    unreadCount: 3,
    isOnline: true,
    lastSeen: new Date(),
    isTyping: false,
    lastMessage: {
      content: "Can you send me the latest design files?",
      timestamp: new Date(Date.now() - 5 * 60000), // 5 minutes ago
      senderId: "user1",
      status: "read"
    }
  },
  {
    id: "group1",
    title: "Design Team",
    type: "group",
    unreadCount: 12,
    isPinned: true,
    members: [
      { id: "user1", name: "<PERSON>", isOnline: true },
      { id: "user2", name: "<PERSON>", isOnline: false },
      { id: "user3", name: "<PERSON>", isOnline: true },
      { id: "user4", name: "<PERSON>", isOnline: false },
    ],
    lastMessage: {
      content: "Let's discuss the new dashboard layout",
      timestamp: new Date(Date.now() - 30 * 60000), // 30 minutes ago
      senderId: "user3",
      senderName: "<PERSON>",
      status: "read"
    }
  },
  {
    id: "channel1",
    title: "Company Announcements",
    type: "channel",
    unreadCount: 1,
    description: "Official company news and updates",
    lastMessage: {
      content: "We're excited to announce our new product launch!",
      timestamp: new Date(Date.now() - 2 * 3600000), // 2 hours ago
      senderId: "admin",
      senderName: "Admin",
      status: "read"
    }
  },
  {
    id: "user2",
    title: "Sarah Johnson",
    type: "private",
    unreadCount: 0,
    isOnline: false,
    lastSeen: new Date(Date.now() - 24 * 3600000), // 1 day ago
    lastMessage: {
      content: "Thanks for your help with the project!",
      timestamp: new Date(Date.now() - 1 * 3600000), // 1 hour ago
      senderId: "me",
      status: "read"
    }
  },
  {
    id: "group2",
    title: "Marketing Team",
    type: "group",
    unreadCount: 0,
    members: [
      { id: "user5", name: "Alex Turner", isOnline: false },
      { id: "user6", name: "Jessica White", isOnline: true },
      { id: "user7", name: "David Brown", isOnline: false },
    ],
    lastMessage: {
      content: "The campaign metrics look promising so far",
      timestamp: new Date(Date.now() - 5 * 3600000), // 5 hours ago
      senderId: "user6",
      senderName: "Jessica",
      status: "read"
    }
  },
  {
    id: "channel2",
    title: "Tech Updates",
    type: "channel",
    unreadCount: 0,
    isPinned: true,
    description: "Technology news and updates",
    lastMessage: {
      content: "Check out these new developer tools we're considering",
      timestamp: new Date(Date.now() - 12 * 3600000), // 12 hours ago
      senderId: "user8",
      senderName: "Tech Lead",
      status: "read"
    }
  },
  {
    id: "user3",
    title: "Mike Williams",
    type: "private",
    unreadCount: 0,
    isOnline: true,
    isMuted: true,
    lastMessage: {
      content: "I'll send you the report by end of day",
      timestamp: new Date(Date.now() - 1 * 86400000), // 1 day ago
      senderId: "user3",
      status: "read"
    }
  },
  {
    id: "user4",
    title: "Emily Davis",
    type: "private",
    unreadCount: 0,
    isOnline: false,
    lastSeen: new Date(Date.now() - 30 * 60000), // 30 minutes ago
    lastMessage: {
      content: "Are we still meeting tomorrow at 10?",
      timestamp: new Date(Date.now() - 2 * 86400000), // 2 days ago
      senderId: "me",
      status: "read"
    }
  }
];

// Mock messages for each conversation
export const mockMessages: Record<string, ChatMessage[]> = {
  "user1": [
    {
      id: "msg1",
      senderId: "user1",
      senderName: "John Smith",
      content: "Hey there! How's the dashboard coming along?",
      timestamp: new Date(Date.now() - 30 * 60000), // 30 minutes ago
      status: "read",
      isOutgoing: false
    },
    {
      id: "msg2",
      senderId: "me",
      senderName: "Me",
      content: "It's going well! I'm just finishing up the chat module.",
      timestamp: new Date(Date.now() - 25 * 60000), // 25 minutes ago
      status: "read",
      isOutgoing: true
    },
    {
      id: "msg3",
      senderId: "user1",
      senderName: "John Smith",
      content: "Great! Can you send me the latest design files?",
      timestamp: new Date(Date.now() - 5 * 60000), // 5 minutes ago
      status: "read",
      isOutgoing: false
    }
  ],
  "group1": [
    {
      id: "gmsg1",
      senderId: "user1",
      senderName: "John Smith",
      content: "Hi team, I've updated the wireframes for the new project.",
      timestamp: new Date(Date.now() - 5 * 3600000), // 5 hours ago
      status: "read",
      isOutgoing: false,
      attachments: [
        {
          type: "image",
          url: "https://img.heroui.chat/image/dashboard?w=600&h=400&u=1",
          name: "wireframe.png",
          size: 1200000
        }
      ]
    },
    {
      id: "gmsg2",
      senderId: "user2",
      senderName: "Sarah Johnson",
      content: "These look great! I especially like the new sidebar design.",
      timestamp: new Date(Date.now() - 4 * 3600000), // 4 hours ago
      status: "read",
      isOutgoing: false
    },
    {
      id: "gmsg3",
      senderId: "me",
      senderName: "Me",
      content: "I agree. The navigation is much more intuitive now.",
      timestamp: new Date(Date.now() - 3 * 3600000), // 3 hours ago
      status: "read",
      isOutgoing: true
    },
    {
      id: "gmsg4",
      senderId: "user3",
      senderName: "Mike Williams",
      content: "Let's discuss the new dashboard layout in our meeting tomorrow.",
      timestamp: new Date(Date.now() - 30 * 60000), // 30 minutes ago
      status: "read",
      isOutgoing: false
    }
  ],
  "channel1": [
    {
      id: "cmsg1",
      senderId: "admin",
      senderName: "Admin",
      content: "Welcome to the Company Announcements channel! This is where you'll find all official company news and updates.",
      timestamp: new Date(Date.now() - 7 * 86400000), // 7 days ago
      status: "read",
      isOutgoing: false
    },
    {
      id: "cmsg2",
      senderId: "admin",
      senderName: "Admin",
      content: "We're excited to announce our new product launch next month! Stay tuned for more details.",
      timestamp: new Date(Date.now() - 2 * 3600000), // 2 hours ago
      status: "read",
      isOutgoing: false,
      attachments: [
        {
          type: "file",
          url: "#",
          name: "product_launch_details.pdf",
          size: 2500000
        }
      ]
    }
  ]
};

// Add more mock data for other conversations as needed
