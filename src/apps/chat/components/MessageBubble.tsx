import React, { useState } from "react";
import { Avatar, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from "@heroui/react";
import { Icon } from "@iconify/react";
import type { ChatMessage } from "../types";
import { motion } from "framer-motion";

interface MessageBubbleProps {
  message: ChatMessage;
  showSender: boolean;
  onReply: () => void;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({ message, showSender, onReply }) => {
  const [showActions, setShowActions] = useState(false);
  
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  const hasAttachments = message.attachments && message.attachments.length > 0;
  
  return (
    <motion.div 
      className={`flex items-start mb-6 group ${message.isOutgoing ? 'justify-end' : ''}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {!message.isOutgoing && showSender && (
        <Avatar
          src={message.senderAvatar || `https://img.heroui.chat/image/avatar?w=200&h=200&u=${message.senderId}`}
          className="h-8 w-8 mr-2 mt-1"
        />
      )}
      
      <div className={`max-w-[75%] ${!message.isOutgoing && !showSender ? 'ml-10' : ''}`}>

        
        <div className="flex items-end">
        {showActions && message.isOutgoing && (
            <Dropdown>
              <DropdownTrigger>
                <button className="text-custom-muted hover:text-custom-text mr-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Icon icon="lucide:more-vertical" className="text-md" />
                </button>
              </DropdownTrigger>
              <DropdownMenu aria-label="Message actions">
                <DropdownItem onPress={onReply} startContent={<Icon icon="lucide:reply" />}>
                  Reply
                </DropdownItem>
                <DropdownItem startContent={<Icon icon="lucide:edit-3" />}>
                  Edit
                </DropdownItem>
                <DropdownItem startContent={<Icon icon="lucide:copy" />}>
                  Copy
                </DropdownItem>
                <DropdownItem startContent={<Icon icon="lucide:forward" />}>
                  Forward
                </DropdownItem>
                <DropdownItem startContent={<Icon icon="lucide:pin" />}>
                  Pin
                </DropdownItem>
                <DropdownItem 
                  startContent={<Icon icon="lucide:trash-2" />}
                  className="text-danger"
                >
                  Delete
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          )}
          <div 
            className={`rounded-lg overflow-hidden ${
              message.isOutgoing 
                ? 'bg-primary/20 text-white' 
                : 'bg-custom-sidebar text-custom-text'
            }`}
          >

{!message.isOutgoing && showSender && (
          <div className="text-custom-primary text-xs font-medium mt-2 ml-2">
            {message.senderName}
          </div>
        )}
            {message.replyTo && (
              <div 
                className={`px-3 py-2 border-l-2 mx-3 mt-3 rounded text-xs ${
                  message.isOutgoing 
                    ? 'border-white/30 bg-white/5' 
                    : 'border-custom-primary/50 bg-custom-primary/5'
                }`}
              >
                <div className={`font-medium ${message.isOutgoing ? 'text-white/80' : 'text-custom-primary'}`}>
                  {message.replyTo.senderName}
                </div>
                <div className="truncate">{message.replyTo.content}</div>
              </div>
            )}
            
            {hasAttachments && (
              <div className="p-3 pt-3 pb-0">
                {message.attachments?.map((attachment, index) => (
                  <div key={index} className="mb-2">
                    {attachment.type === 'image' && (
                      <img 
                        src={attachment.url} 
                        alt="Attachment" 
                        className="rounded-md max-h-60 w-auto"
                      />
                    )}
                    {attachment.type === 'file' && (
                      <div className="flex items-center bg-custom-border/20 rounded-md p-2">
                        <Icon icon="lucide:file" className="mr-2" />
                        <div className="flex-1 min-w-0">
                          <div className="text-sm truncate">{attachment.name}</div>
                          <div className="text-xs opacity-70">{formatFileSize(attachment.size || 0)}</div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
            
            {message.content && (
              <div className="px-3 py-2 break-words">
                {message.content}
              </div>
            )}
          </div>
          
          {showActions && !message.isOutgoing && (
            <Dropdown>
              <DropdownTrigger>
                <button className="text-custom-muted hover:text-custom-text ml-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Icon icon="lucide:more-vertical" className="text-md" />
                </button>
              </DropdownTrigger>
              <DropdownMenu aria-label="Message actions">
                <DropdownItem onPress={onReply} startContent={<Icon icon="lucide:reply" />}>
                  Reply
                </DropdownItem>
                <DropdownItem startContent={<Icon icon="lucide:copy" />}>
                  Copy
                </DropdownItem>
                <DropdownItem startContent={<Icon icon="lucide:forward" />}>
                  Forward
                </DropdownItem>
                <DropdownItem startContent={<Icon icon="lucide:pin" />}>
                  Pin
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          )}
          
          
        </div>
        
        <div className={`flex items-center mt-1 text-xs ${message.isOutgoing ? 'justify-end' : 'justify-start'}`}>
          <span className="text-custom-muted">{formatTime(message.timestamp)}</span>
          
          {message.isOutgoing && (
            <span className="ml-1">
              {message.status === 'read' ? (
                <Icon icon="lucide:check-check" className="text-custom-primary text-xs" />
              ) : message.status === 'delivered' ? (
                <Icon icon="lucide:check-check" className="text-custom-muted text-xs" />
              ) : (
                <Icon icon="lucide:check" className="text-custom-muted text-xs" />
              )}
            </span>
          )}
        </div>
      </div>
    </motion.div>
  );
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};