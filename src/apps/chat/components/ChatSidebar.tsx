import React, { useState, useMemo } from "react";
import { Card, CardBody, CardHeader, Input, Avatar, Badge, Button } from "@heroui/react";
import { Icon } from "@iconify/react";
import { mockConversations } from "../data/mockData";
import type { ChatConversation } from "../types";

interface ChatSidebarProps {
  activeConversationId?: string;
  onSelectConversation: (conversation: ChatConversation) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

export const ChatSidebar: React.FC<ChatSidebarProps> = ({ 
  activeConversationId, 
  onSelectConversation,
  searchQuery,
  onSearchChange
}) => {
  const [filter, setFilter] = useState<'all' | 'unread' | 'pinned'>('all');

  const filteredConversations = useMemo(() => {
    let result = [...mockConversations];
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        conv => conv.title.toLowerCase().includes(query) || 
                conv.lastMessage?.content.toLowerCase().includes(query)
      );
    }
    
    // Apply type filter
    if (filter === 'unread') {
      result = result.filter(conv => conv.unreadCount > 0);
    } else if (filter === 'pinned') {
      result = result.filter(conv => conv.isPinned);
    }
    
    // Sort by pinned first, then by last message date
    return result.sort((a, b) => {
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      
      const dateA = a.lastMessage?.timestamp || new Date(0);
      const dateB = b.lastMessage?.timestamp || new Date(0);
      return dateB.getTime() - dateA.getTime();
    });
  }, [searchQuery, filter]);

  const formatTime = (date: Date) => {
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    
    if (isToday) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday = date.toDateString() === yesterday.toDateString();
    
    if (isYesterday) {
      return 'Yesterday';
    }
    
    // If it's within the last 7 days, show the day name
    const daysDiff = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    if (daysDiff < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    }
    
    // Otherwise show the date
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  };

  const getConversationIcon = (conversation: ChatConversation) => {
    if (conversation.type === 'channel') {
      return <Icon icon="lucide:megaphone" className="text-custom-text text-sm" />;
    } else if (conversation.type === 'group') {
      return <Icon icon="lucide:users" className="text-custom-text text-sm" />;
    }
    return null;
  };

  return (
    <div className="flex flex-col h-full">
      <div className="border-b border-custom-border p-3">
        <Input
          placeholder="Search chats..."
          value={searchQuery}
          onValueChange={onSearchChange}
          startContent={<Icon icon="lucide:search" className="text-custom-muted" />}
          endContent={
            searchQuery ? (
              <Button 
                isIconOnly 
                variant="light" 
                size="sm" 
                className="text-custom-muted"
                onPress={() => onSearchChange("")}
              >
                <Icon icon="lucide:x" className="text-xs" />
              </Button>
            ) : null
          }
          classNames={{
            inputWrapper: "bg-custom-sidebar border-custom-border h-9",
          }}
          radius="lg"
          variant="bordered"
        />
      </div>
      
      <div className="flex border-b border-custom-border px-2 py-1">
        <Button 
          size="sm" 
          variant={filter === 'all' ? "flat" : "light"} 
          color={filter === 'all' ? "primary" : "default"}
          className="flex-1"
          onPress={() => setFilter('all')}
        >
          All
        </Button>
        <Button 
          size="sm" 
          variant={filter === 'unread' ? "flat" : "light"} 
          color={filter === 'unread' ? "primary" : "default"}
          className="flex-1"
          onPress={() => setFilter('unread')}
        >
          Unread
        </Button>
        <Button 
          size="sm" 
          variant={filter === 'pinned' ? "flat" : "light"} 
          color={filter === 'pinned' ? "primary" : "default"}
          className="flex-1"
          onPress={() => setFilter('pinned')}
        >
          Pinned
        </Button>
      </div>
      
      <div className="flex-1 overflow-y-auto custom-scrollbar">
        {filteredConversations.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-40 text-custom-muted">
            <Icon icon="lucide:search-x" className="text-3xl mb-2" />
            <p>No conversations found</p>
          </div>
        ) : (
          filteredConversations.map((conversation) => (
            <div 
              key={conversation.id} 
              className={`flex items-center p-3 border-b border-custom-border cursor-pointer hover:bg-custom-border/10 relative
                ${activeConversationId === conversation.id ? 'bg-custom-border/20' : ''}
                ${conversation.isPinned ? 'bg-custom-border/5' : ''}
              `}
              onClick={() => onSelectConversation(conversation)}
            >
              {conversation.isPinned && (
                <div className="absolute top-0 right-0 w-0 h-0 border-t-8 border-r-8 border-t-custom-primary border-r-transparent transform -translate-y-0.5 translate-x-0.5"></div>
              )}
              
              <div className="relative">
                {conversation.type === 'private' ? (
                  <Badge
                    content=""
                    color="success" 
                    shape="circle"
                    placement="bottom-right"
                    isInvisible={!conversation.isOnline}
                  >
                    <Avatar
                      src={conversation.avatar || `https://img.heroui.chat/image/avatar?w=200&h=200&u=${conversation.id}`}
                      className="h-10 w-10"
                    />
                  </Badge>
                ) : (
                  <div className="relative">
                    <Avatar
                      src={conversation.avatar || `https://img.heroui.chat/image/avatar?w=200&h=200&u=${conversation.id}`}
                      className="h-10 w-10"
                      icon={getConversationIcon(conversation)}
                    />
                    {conversation.type === 'channel' && (
                      <div className="absolute bottom-0 right-0 bg-custom-primary rounded-full w-4 h-4 flex items-center justify-center">
                        <Icon icon="lucide:megaphone" className="text-white text-[10px]" />
                      </div>
                    )}
                    {conversation.type === 'group' && (
                      <div className="absolute bottom-0 right-0 bg-custom-primary rounded-full w-4 h-4 flex items-center justify-center">
                        <Icon icon="lucide:users" className="text-white text-[10px]" />
                      </div>
                    )}
                  </div>
                )}
              </div>
              
              <div className="ml-3 flex-1 min-w-0">
                <div className="flex justify-between items-center">
                  <p className="text-white text-sm font-medium truncate max-w-[120px]">
                    {conversation.title}
                  </p>
                  <span className="text-custom-muted text-xs whitespace-nowrap">
                    {conversation.lastMessage && formatTime(conversation.lastMessage.timestamp)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-custom-muted text-xs truncate max-w-[150px]">
                    {conversation.isTyping ? (
                      <span className="text-custom-primary">typing...</span>
                    ) : conversation.lastMessage ? (
                      <>
                        {conversation.type !== 'private' && conversation.lastMessage.senderId !== 'me' && (
                          <span className="font-medium mr-1">{conversation.lastMessage.senderName || 'User'}:</span>
                        )}
                        {conversation.lastMessage.content}
                      </>
                    ) : (
                      'No messages yet'
                    )}
                  </p>
                  <div className="flex items-center">
                    {conversation.isMuted && (
                      <Icon icon="lucide:volume-x" className="text-custom-muted text-xs mr-1" />
                    )}
                    {conversation.unreadCount > 0 && (
                      <span className="bg-custom-primary text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1">
                        {conversation.unreadCount}
                      </span>
                    )}
                    {conversation.lastMessage?.senderId === 'me' && (
                      <div className="text-xs text-custom-muted ml-1">
                        {conversation.lastMessage.status === 'read' ? (
                          <Icon icon="lucide:check-check" className="text-custom-primary text-xs" />
                        ) : (
                          <Icon icon="lucide:check" className="text-custom-muted text-xs" />
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};