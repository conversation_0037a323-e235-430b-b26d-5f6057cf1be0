import React, { useState, useRef } from "react";
import { Input, <PERSON><PERSON>, Tooltip } from "@heroui/react";
import { Icon } from "@iconify/react";
import type { ChatMessage, ChatType } from "../types";
import { motion, AnimatePresence } from "framer-motion";

interface MessageInputProps {
  onSendMessage: (content: string, attachments?: any[]) => void;
  replyTo: ChatMessage | null;
  onCancelReply: () => void;
  conversationType: ChatType;
}

export const MessageInput: React.FC<MessageInputProps> = ({ 
  onSendMessage, 
  replyTo, 
  onCancelReply,
  conversationType
}) => {
  const [message, setMessage] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const timerRef = useRef<number | null>(null);
  
  const handleSend = () => {
    if (message.trim()) {
      onSendMessage(message);
      setMessage("");
    }
  };
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Send on Ctrl+Enter or Cmd+Enter
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      handleSend();
    }
  };
  
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    // In a real app, you would upload these files to a server
    // For this demo, we'll just simulate attachments
    const attachments = Array.from(files).map(file => ({
      type: file.type.startsWith('image/') ? 'image' : 'file',
      url: URL.createObjectURL(file),
      name: file.name,
      size: file.size
    }));
    
    onSendMessage(message, attachments);
    setMessage("");
    
    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  const toggleRecording = () => {
    if (isRecording) {
      // Stop recording
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      setIsRecording(false);
      
      // In a real app, you would process the audio recording here
      // For this demo, we'll just simulate sending a voice message
      onSendMessage("🎤 Voice message");
      setRecordingTime(0);
    } else {
      // Start recording
      setIsRecording(true);
      timerRef.current = window.setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    }
  };
  
  const formatRecordingTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  return (
    <div className="p-3 border-t border-custom-border">
      <AnimatePresence>
        {replyTo && (
          <motion.div 
            className="flex items-center bg-custom-sidebar rounded-lg p-2 mb-2"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
          >
            <div className="flex-1 min-w-0">
              <div className="flex items-center">
                <Icon icon="lucide:reply" className="text-custom-primary mr-2" />
                <span className="text-custom-primary text-xs font-medium">
                  Reply to {replyTo.senderName}
                </span>
              </div>
              <p className="text-custom-text text-xs truncate">{replyTo.content}</p>
            </div>
            <Button 
              isIconOnly 
              variant="light" 
              size="sm" 
              className="text-custom-muted"
              onPress={onCancelReply}
            >
              <Icon icon="lucide:x" className="text-xs" />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
      
      {isRecording ? (
        <div className="flex items-center bg-custom-sidebar rounded-lg p-3">
          <div className="flex-1 flex items-center">
            <div className="w-3 h-3 rounded-full bg-danger animate-pulse mr-3"></div>
            <span className="text-custom-text">Recording... {formatRecordingTime(recordingTime)}</span>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="light" 
              color="danger" 
              size="sm" 
              startContent={<Icon icon="lucide:trash-2" />}
              onPress={() => {
                if (timerRef.current) {
                  clearInterval(timerRef.current);
                  timerRef.current = null;
                }
                setIsRecording(false);
                setRecordingTime(0);
              }}
            >
              Cancel
            </Button>
            <Button 
              variant="solid" 
              color="primary" 
              size="sm" 
              startContent={<Icon icon="lucide:send" />}
              onPress={toggleRecording}
            >
              Send
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex items-end gap-2">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileUpload}
            className="hidden"
            multiple
          />
          
          <Tooltip content="Attach files">
            <Button 
              isIconOnly 
              variant="light" 
              className="text-custom-text"
              onPress={() => fileInputRef.current?.click()}
            >
              <Icon icon="lucide:paperclip" />
            </Button>
          </Tooltip>
          
          <div className="flex-1">
            <Input
              placeholder={conversationType === 'channel' ? "Broadcast a message..." : "Type a message..."}
              value={message}
              onValueChange={setMessage}
              onKeyDown={handleKeyDown}
              classNames={{
                inputWrapper: "bg-custom-sidebar border-custom-border min-h-[44px] py-1",
              }}
              radius="lg"
              variant="bordered"
              endContent={
                <div className="flex items-center">
                  <Tooltip content="Emoji">
                    <Button 
                      isIconOnly 
                      variant="light" 
                      size="sm" 
                      className="text-custom-text"
                      onPress={() => setShowEmojiPicker(!showEmojiPicker)}
                    >
                      <Icon icon="lucide:smile" />
                    </Button>
                  </Tooltip>
                </div>
              }
              multiline
              maxRows={5}
            />
          </div>
          
          {message.trim() ? (
            <Button 
              isIconOnly 
              color="primary" 
              className="mb-0.5"
              onPress={handleSend}
            >
              <Icon icon="lucide:send" />
            </Button>
          ) : (
            <Tooltip content="Voice message">
              <Button 
                isIconOnly 
                variant="light" 
                className="text-custom-text"
                onPress={toggleRecording}
              >
                <Icon icon="lucide:mic" />
              </Button>
            </Tooltip>
          )}
        </div>
      )}
      
      <div className="flex justify-between items-center mt-2">
        <div className="text-custom-muted text-xs">
          Press Ctrl+Enter to send
        </div>
        {conversationType !== 'private' && (
          <div className="text-custom-muted text-xs flex items-center">
            <Icon icon="lucide:eye" className="mr-1 text-xs" />
            Seen by 3
          </div>
        )}
      </div>
    </div>
  );
};