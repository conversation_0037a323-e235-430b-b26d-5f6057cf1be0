import React, { useState, useRef, useEffect } from "react";
import { Card, CardBody, CardHeader, Input, Avatar, Button, Tooltip, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem, Badge } from "@heroui/react";
import { Icon } from "@iconify/react";
import type { ChatConversation, ChatMessage } from "../types";
import { mockMessages } from "../data/mockData";
import { MessageBubble } from "./MessageBubble";
import { NewMessageAlert } from "./NewMessageAlert";
import { MessageInput } from "./MessageInput";

interface ChatViewProps {
  conversation: ChatConversation | null;
  onBack: () => void;
}

export const ChatView: React.FC<ChatViewProps> = ({ conversation, onBack }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [showNewMessageAlert, setShowNewMessageAlert] = useState(false);
  const [replyTo, setReplyTo] = useState<ChatMessage | null>(null);
  const [isPinnedMessagesOpen, setIsPinnedMessagesOpen] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [userScrolled, setUserScrolled] = useState(false);
  
  // Load messages when conversation changes
  useEffect(() => {
    if (conversation) {
      setMessages(mockMessages[conversation.id] || []);
      setUserScrolled(false);
      setTimeout(scrollToBottom, 100);
    }
  }, [conversation]);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  const handleScroll = () => {
    if (!messagesContainerRef.current) return;
    
    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
    const isScrolledToBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 10;
    
    setUserScrolled(!isScrolledToBottom);
  };
  
  const handleSendMessage = (content: string, attachments?: any[]) => {
    if (!conversation || (!content.trim() && !attachments?.length)) return;
    
    const newMessage: ChatMessage = {
      id: `msg-${Date.now()}`,
      senderId: 'me',
      senderName: 'Me',
      content,
      timestamp: new Date(),
      status: 'sent',
      isOutgoing: true,
      replyTo: replyTo ? {
        id: replyTo.id,
        content: replyTo.content,
        senderName: replyTo.senderName
      } : undefined,
      attachments
    };
    
    setMessages(prev => [...prev, newMessage]);
    setReplyTo(null);
    
    if (!userScrolled) {
      setTimeout(scrollToBottom, 100);
    }
    
    // Simulate message being read after a delay
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id ? { ...msg, status: 'read' } : msg
        )
      );
    }, 2000);
    
    // Simulate reply after a delay
    if (Math.random() > 0.5) {
      setTimeout(() => {
        // Set typing indicator
        if (conversation) {
          conversation.isTyping = true;
          // Force re-render
          // This would normally be handled by a state management system
        }
        
        // Send response after "typing"
        setTimeout(() => {
          const responseMessage: ChatMessage = {
            id: `msg-${Date.now()}`,
            senderId: conversation.id,
            senderName: conversation.title,
            senderAvatar: conversation.avatar,
            content: getRandomResponse(),
            timestamp: new Date(),
            status: 'read',
            isOutgoing: false
          };
          
          setMessages(prev => [...prev, responseMessage]);
          
          if (conversation) {
            conversation.isTyping = false;
          }
          
          if (userScrolled) {
            setShowNewMessageAlert(true);
          } else {
            setTimeout(scrollToBottom, 100);
          }
        }, 1500);
      }, 1000);
    }
  };
  
  const getRandomResponse = () => {
    const responses = [
      "Got it, thanks!",
      "I'll take a look at this soon.",
      "Sounds good to me.",
      "Can we discuss this in our next meeting?",
      "Thanks for the update!",
      "I'll get back to you on this.",
      "Let me check with the team.",
      "Great progress!",
      "I have some feedback on this.",
      "Let's schedule a call to discuss."
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  };
  
  const handleNewMessageAlertClick = () => {
    scrollToBottom();
    setShowNewMessageAlert(false);
  };

  if (!conversation) {
    return (
      <div className="h-full flex flex-col items-center justify-center bg-custom-card">
        <div className="text-center p-6">
          <div className="w-16 h-16 bg-custom-sidebar rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon icon="lucide:message-circle" className="text-custom-muted text-3xl" />
          </div>
          <h3 className="text-white text-lg font-medium mb-2">Select a conversation</h3>
          <p className="text-custom-muted text-sm">Choose from your contacts to start chatting</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full min-h-0 overflow-hidden bg-custom-card">
      <div className="border-b border-custom-border flex items-center justify-between p-3">
        <div className="flex items-center">
          <Button 
            isIconOnly 
            variant="light" 
            size="sm" 
            className="text-custom-text md:hidden mr-2"
            onPress={onBack}
          >
            <Icon icon="lucide:chevron-left" />
          </Button>
          
          <Avatar
            src={conversation.avatar || `https://img.heroui.chat/image/avatar?w=200&h=200&u=${conversation.id}`}
            className="h-10 w-10"
          />
          
          <div className="ml-3">
            <div className="flex items-center">
              <p className="text-white text-sm font-medium">{conversation.title}</p>
              {conversation.type === 'channel' && (
                <Badge content="" color="primary" className="ml-2">
                  <Icon icon="lucide:megaphone" className="text-xs" />
                </Badge>
              )}
            </div>
            <p className="text-custom-muted text-xs">
              {conversation.isTyping ? (
                <span className="text-custom-primary">typing...</span>
              ) : conversation.isOnline ? (
                'online'
              ) : conversation.lastSeen ? (
                `last seen ${formatLastSeen(conversation.lastSeen)}`
              ) : conversation.type === 'group' ? (
                `${conversation.members?.length || 0} members`
              ) : conversation.type === 'channel' ? (
                conversation.description || 'Channel'
              ) : (
                'offline'
              )}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-1">
          {conversation.type !== 'channel' && (
            <>
              <Tooltip content="Voice call">
                <Button isIconOnly variant="light" className="text-custom-text">
                  <Icon icon="lucide:phone" />
                </Button>
              </Tooltip>
              <Tooltip content="Video call">
                <Button isIconOnly variant="light" className="text-custom-text">
                  <Icon icon="lucide:video" />
                </Button>
              </Tooltip>
            </>
          )}
          
          <Tooltip content="Pinned messages">
            <Button 
              isIconOnly 
              variant="light" 
              className={`text-custom-text ${isPinnedMessagesOpen ? 'bg-primary/10 text-primary' : ''}`}
              onPress={() => setIsPinnedMessagesOpen(!isPinnedMessagesOpen)}
            >
              <Icon icon="lucide:pin" />
            </Button>
          </Tooltip>
          
          <Dropdown>
            <DropdownTrigger>
              <Button isIconOnly variant="light" className="text-custom-text">
                <Icon icon="lucide:more-vertical" />
              </Button>
            </DropdownTrigger>
            <DropdownMenu aria-label="Chat options">
              <DropdownItem key="search-chat" startContent={<Icon icon="lucide:search" />}>
                Search in chat
              </DropdownItem>
              <DropdownItem key="mute-chat" startContent={<Icon icon="lucide:bell" />}>
                Mute notifications
              </DropdownItem>
              <DropdownItem key="view-members" startContent={<Icon icon="lucide:users" />}>
                View members
              </DropdownItem>
              <DropdownItem key="shared-media" startContent={<Icon icon="lucide:image" />}>
                Shared media
              </DropdownItem>
              <DropdownItem 
                key="clear-history"
                startContent={<Icon icon="lucide:trash-2" />}
                className="text-danger"
              >
                Clear history
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </div>
      </div>
      
      <div className="flex-1 flex flex-col relative min-h-0">
        <div 
          className="flex-1 overflow-y-auto p-4 custom-scrollbar"
          ref={messagesContainerRef}
          onScroll={handleScroll}
        >
          {isPinnedMessagesOpen && (
            <div className="bg-custom-sidebar/50 rounded-lg p-3 mb-4 border border-custom-border">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-white text-sm font-medium flex items-center">
                  <Icon icon="lucide:pin" className="mr-2 text-custom-primary" />
                  Pinned Messages
                </h4>
                <Button 
                  isIconOnly 
                  variant="light" 
                  size="sm" 
                  className="text-custom-muted"
                  onPress={() => setIsPinnedMessagesOpen(false)}
                >
                  <Icon icon="lucide:x" className="text-xs" />
                </Button>
              </div>
              <div className="text-custom-muted text-xs text-center py-2">
                No pinned messages yet
              </div>
            </div>
          )}
          
          {messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-custom-muted">
              <div className="w-16 h-16 bg-custom-sidebar rounded-full flex items-center justify-center mb-4">
                <Icon icon="lucide:message-circle" className="text-3xl" />
              </div>
              <p className="mb-2">No messages yet</p>
              <p className="text-xs">Send a message to start the conversation</p>
            </div>
          ) : (
            <>
              <p className="text-custom-text text-center text-xs py-2 mb-4">
                {formatDateHeader(messages[0].timestamp)}
              </p>
              
              {messages.map((message, index) => {
                // Check if we need a date header
                const showDateHeader = index > 0 && 
                  !isSameDay(messages[index-1].timestamp, message.timestamp);
                
                return (
                  <React.Fragment key={message.id}>
                    {showDateHeader && (
                      <p className="text-custom-text text-center text-xs py-2 my-4">
                        {formatDateHeader(message.timestamp)}
                      </p>
                    )}
                    
                    <MessageBubble 
                      message={message} 
                      showSender={shouldShowSender(message, index, messages)}
                      onReply={() => setReplyTo(message)}
                    />
                  </React.Fragment>
                );
              })}
              <div ref={messagesEndRef} />
            </>
          )}
        </div>
        
        {showNewMessageAlert && (
          <NewMessageAlert onClick={handleNewMessageAlertClick} />
        )}
        
        <MessageInput 
          onSendMessage={handleSendMessage} 
          replyTo={replyTo}
          onCancelReply={() => setReplyTo(null)}
          conversationType={conversation.type}
        />
      </div>
    </div>
  );
};

// Helper functions
const formatLastSeen = (date: Date) => {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / 60000);
  
  if (diffMins < 1) return 'just now';
  if (diffMins < 60) return `${diffMins} min ago`;
  
  const diffHours = Math.floor(diffMins / 60);
  if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  
  const diffDays = Math.floor(diffHours / 24);
  if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  
  return date.toLocaleDateString();
};

const formatDateHeader = (date: Date) => {
  const now = new Date();
  const isToday = isSameDay(date, now);
  
  if (isToday) {
    return 'Today';
  }
  
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);
  const isYesterday = isSameDay(date, yesterday);
  
  if (isYesterday) {
    return 'Yesterday';
  }
  
  // If it's within the last 7 days, show the day name
  const daysDiff = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
  if (daysDiff < 7) {
    return date.toLocaleDateString(undefined, { weekday: 'long' });
  }
  
  // Otherwise show the full date
  return date.toLocaleDateString(undefined, { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
};

const isSameDay = (date1: Date, date2: Date) => {
  return date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate();
};

const shouldShowSender = (message: ChatMessage, index: number, messages: ChatMessage[]) => {
  // Always show sender for first message
  if (index === 0) return true;
  
  const prevMessage = messages[index - 1];
  
  // Show sender if previous message is from a different sender
  if (prevMessage.senderId !== message.senderId) return true;
  
  // Show sender if there's a gap of more than 2 minutes between messages
  const timeDiff = message.timestamp.getTime() - prevMessage.timestamp.getTime();
  if (timeDiff > 2 * 60 * 1000) return true;
  
  return false;
};