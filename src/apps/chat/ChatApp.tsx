import React from "react";
import { ChatView } from "./components/ChatView";
// import { ChatConversation } from "./types";

interface ChatConversation {
  id: string;
  title: string;
  avatar?: string;
  type: ChatType;
  lastMessage?: {
    content: string;
    timestamp: Date;
    senderId: string;
    senderName?: string;
    status: MessageStatus;
  };
  unreadCount: number;
  isOnline?: boolean;
  lastSeen?: Date;
  isTyping?: boolean;
  isPinned?: boolean;
  isMuted?: boolean;
  members?: {
    id: string;
    name: string;
    avatar?: string;
    isOnline?: boolean;
    role?: 'admin' | 'member';
  }[];
  description?: string;
  icon?: string;
}

interface ChatAppProps {
  conversation: ChatConversation | null;
  onSelectConversation: (conv: ChatConversation | null) => void;
}

export const ChatApp: React.FC<ChatAppProps> = ({ conversation, onSelectConversation }) => {
  return (
    <div className="h-full flex flex-col">
      <ChatView 
        conversation={conversation}
        onBack={() => onSelectConversation(null)}
      />
    </div>
  );
};