import React, { useState, useEffect, useMemo } from "react";
    import { Card, CardBody, Select, SelectItem, Input, Chip } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import { GanttChart } from "../components/GanttChart";
    import type { ProjectTodo, Project } from "../types";

    // Mock data - in a real app, this would come from API or props
    const mockProjects: Project[] = [
      {
        id: "1",
        name: "Website Redesign",
        color: "#1B84FF",
        description: "Revamping the company website with modern design",
        deadline: "2024-12-15",
        createdAt: "2024-01-15",
      },
      {
        id: "2",
        name: "Mobile App Development",
        color: "#FF6B6B",
        description: "Creating a cross-platform mobile application",
        deadline: "2024-11-30",
        createdAt: "2024-02-01",
      },
      {
        id: "3",
        name: "Marketing Campaign",
        color: "#66BB6A",
        description: "Q3 marketing campaign for new product line",
        deadline: "2024-10-01",
        createdAt: "2024-03-10",
      }
    ];

    const mockTodos: ProjectTodo[] = [
      {
        id: "1",
        title: "Design Homepage Wireframe",
        completed: true,
        dueDate: "2024-06-10",
        startDate: "2024-06-05",
        createdAt: "2024-06-01",
        projectId: "1"
      },
      {
        id: "2",
        title: "Implement Authentication",
        completed: false,
        dueDate: "2024-06-20",
        startDate: "2024-06-12",
        createdAt: "2024-06-05",
        projectId: "1"
      },
      {
        id: "3",
        title: "API Integration",
        completed: false,
        dueDate: "2024-06-25",
        startDate: "2024-06-15",
        createdAt: "2024-06-08",
        projectId: "2"
      },
      {
        id: "4",
        title: "User Testing",
        completed: false,
        dueDate: "2024-07-23",
        startDate: "2024-07-05",
        createdAt: "2024-07-02",
        projectId: "2"
      },
      {
        id: "5",
        title: "Social Media Content",
        completed: false,
        dueDate: "2024-06-18",
        startDate: "2024-06-10",
        createdAt: "2024-06-08",
        projectId: "3"
      },
      {
        id: "6",
        title: "Email Campaign",
        completed: true,
        dueDate: "2024-06-08",
        startDate: "2024-06-01",
        createdAt: "2024-05-25",
        projectId: "3"
      }
    ];

    export const GanttChartPage: React.FC = () => {
      const [todos, setTodos] = useState<ProjectTodo[]>(mockTodos);
      const [filteredTodos, setFilteredTodos] = useState<ProjectTodo[]>(mockTodos);
      const [searchQuery, setSearchQuery] = useState("");
      const [selectedProject, setSelectedProject] = useState<string>("all");
      const [selectedStatus, setSelectedStatus] = useState<string>("all");
      
      // Map project IDs to project names/colors
      const projectMap = useMemo(() => {
        const map: Record<string, Project> = {};
        mockProjects.forEach(project => {
          map[project.id] = project;
        });
        return map;
      }, []);
      
      // Apply filters
      useEffect(() => {
        let result = [...todos];
        
        // Apply project filter
        if (selectedProject !== "all") {
          result = result.filter(todo => todo.projectId === selectedProject);
        }
        
        // Apply status filter
        if (selectedStatus !== "all") {
          const isCompleted = selectedStatus === "completed";
          result = result.filter(todo => todo.completed === isCompleted);
        }
        
        // Apply search query
        if (searchQuery.trim()) {
          const query = searchQuery.toLowerCase();
          result = result.filter(todo => 
            todo.title.toLowerCase().includes(query)
          );
        }
        
        setFilteredTodos(result);
      }, [todos, selectedProject, selectedStatus, searchQuery]);
      
      // In a real app, this would fetch data from an API
      useEffect(() => {
        // Simulating API fetch
        const fetchTodos = () => {
          setTodos(mockTodos);
        };
        
        fetchTodos();
      }, []);
      
      return (
        <div className="p-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-2xl font-semibold text-white mb-1">Projects Gantt Chart</h1>
              <p className="text-custom-muted">
                Visualize all project tasks and timelines in a comprehensive Gantt chart view
              </p>
            </div>
            
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2 text-white text-sm">
                <div className="w-3 h-3 rounded-full bg-success/50"></div>
                <span>Completed</span>
              </div>
              <div className="flex items-center gap-2 text-white text-sm">
                <div className="w-3 h-3 rounded-full bg-primary/50"></div>
                <span>In Progress</span>
              </div>
              <div className="flex items-center gap-2 text-white text-sm">
                <div className="w-3 h-3 rounded-full bg-danger/50"></div>
                <span>Overdue</span>
              </div>
            </div>
          </div>
          
          {/* Filters */}
          <Card className="bg-custom-card border-custom-border shadow-none mb-6">
            <CardBody className="flex flex-col sm:flex-row gap-4">
              <Input
                placeholder="Search tasks..."
                value={searchQuery}
                onValueChange={setSearchQuery}
                startContent={<Icon icon="lucide:search" className="text-custom-muted" />}
                classNames={{
                  base: "flex-1",
                  inputWrapper: "bg-custom-sidebar border-custom-border",
                }}
              />
              
              <Select
                label="Project"
                selectedKeys={new Set([selectedProject])}
                onSelectionChange={(keys) => {
                  const key = Array.from(keys)[0] as string;
                  setSelectedProject(key);
                }}
                labelPlacement="outside-left"
                classNames={{
                  base: "w-full sm:w-64",
                  trigger: "bg-custom-sidebar border-custom-border",
                  popoverContent: "bg-custom-card text-white border-custom-border",
                }}
              >
                {[
                  <SelectItem key="all" textValue="All Projects">All Projects</SelectItem>,
                  ...mockProjects.map(project => (
                    <SelectItem key={project.id} textValue={project.name}>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full" style={{ backgroundColor: project.color }}></div>
                        {project.name}
                      </div>
                    </SelectItem>
                  ))
                ]}
              </Select>
              
              <Select
                label="Project Status"
                selectedKeys={new Set([selectedStatus])}
                onSelectionChange={(keys) => {
                  const key = Array.from(keys)[0] as string;
                  setSelectedStatus(key);
                }}
                labelPlacement="outside-left"
                classNames={{
                  base: "w-full sm:w-64",
                  trigger: "bg-custom-sidebar border-custom-border",
                  popoverContent: "bg-custom-card text-white border-custom-border",
                }}
              >
                {[
                  <SelectItem key="all" textValue="All Statuses">All Statuses</SelectItem>,
                  <SelectItem key="active" textValue="Active">Active</SelectItem>,
                  <SelectItem key="completed" textValue="Completed">Completed</SelectItem>
                ]}
              </Select>
            </CardBody>
          </Card>

          
          {/* Main Gantt Chart */}
          <Card className="bg-custom-card border-custom-border shadow-none">
            <CardBody className="p-0 md:p-4">
              <div className="p-4 pb-0">
                <div className="flex items-center justify-between mb-4">
                  <div className="text-sm text-custom-muted">
                    {filteredTodos.length} tasks
                  </div>
                </div>
              </div>
              
              {/* Unified Gantt chart with grouping order */}
              <GanttChart projectMap={projectMap} todos={[...filteredTodos].sort((a,b)=>{
                if(a.projectId===b.projectId) return a.title.localeCompare(b.title);
                const idxA = mockProjects.findIndex(p=>p.id===a.projectId);
                const idxB = mockProjects.findIndex(p=>p.id===b.projectId);
                return idxA - idxB;
              })} />
              
              {filteredTodos.length === 0 && (
                <div className="text-center py-20 text-custom-muted">
                  <Icon icon="lucide:calendar" className="text-4xl mx-auto mb-4" />
                  <p>No tasks match your filters</p>
                  <button 
                    className="mt-4 text-primary underline"
                    onClick={() => {
                      setSelectedProject("all");
                      setSelectedStatus("all");
                      setSearchQuery("");
                    }}
                  >
                    Clear all filters
                  </button>
                </div>
              )}
            </CardBody>
          </Card>
        </div>
      );
    };