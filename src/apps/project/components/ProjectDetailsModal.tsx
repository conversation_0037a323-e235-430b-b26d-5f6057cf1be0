import React, { useState } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>dal<PERSON>eader, 
  ModalBody,
  Button,
  Tabs,
  Tab,
  Card,
  CardBody,
  Progress,
  Divider,
  Checkbox,
  Input,
  Avatar,
  AvatarGroup,
  DatePicker
} from "@heroui/react";
import { Icon } from "@iconify/react";
import type { Project, ProjectTodo } from "../types";
import { getProjectStats, getProjectTodos, getProjectDocuments, getProjectLinks, getProjectAIReports } from "../utils/project-utils";
import { GanttChart } from "./GanttChart";
import { AIReportCard } from "./AIReportCard";
import { DocumentCard } from "./DocumentCard";
import { parseDate } from "@internationalized/date";

interface ProjectDetailsModalProps {
  project: Project;
  isOpen: boolean;
  onClose: () => void;
}

export const ProjectDetailsModal: React.FC<ProjectDetailsModalProps> = ({ 
  project, 
  isO<PERSON>, 
  onClose 
}) => {
  const [activeTab, setActiveTab] = useState<string>("overview");
  const [newTodoTitle, setNewTodoTitle] = useState("");
  const [newTodoStartDate, setNewTodoStartDate] = useState<Date | null>(null);
  const [newTodoDueDate, setNewTodoDueDate] = useState<Date | null>(null);
  const [newLinkTitle, setNewLinkTitle] = useState("");
  const [newLinkUrl, setNewLinkUrl] = useState("");
  const [showLinkForm, setShowLinkForm] = useState(false);
  
  const stats = getProjectStats(project.id);
  const todos = getProjectTodos(project.id);
  const documents = getProjectDocuments(project.id);
  const links = getProjectLinks(project.id);
  const aiReports = getProjectAIReports(project.id);
  
  const handleAddTodo = () => {
    if (!newTodoTitle.trim()) return;
    
    // In a real app, you'd add the todo to the project with the start and due dates
    console.log("Adding todo:", newTodoTitle, {
      startDate: newTodoStartDate,
      dueDate: newTodoDueDate
    });
    
    setNewTodoTitle("");
    setNewTodoStartDate(null);
    setNewTodoDueDate(null);
  };
  
  const handleToggleTodo = (todoId: string, completed: boolean) => {
    // In a real app, you'd update the todo status
    console.log("Toggling todo:", todoId, completed);
  };
  
  const handleAddLink = () => {
    if (!newLinkTitle.trim() || !newLinkUrl.trim()) return;
    
    // In a real app, you'd add the link to the project
    console.log("Adding link:", newLinkTitle, newLinkUrl);
    setNewLinkTitle("");
    setNewLinkUrl("");
    setShowLinkForm(false);
  };
  
  const toggleLinkForm = () => {
    setShowLinkForm(!showLinkForm);
  };
  
  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="full"
      scrollBehavior="inside"
      classNames={{
        base: "bg-content1",
        body: "p-0",
        backdrop: "bg-background/70 backdrop-blur-sm",
        wrapper: "pb-6 pt-10"
      }}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex justify-between items-center">
              <div className="flex items-center gap-3">
                <div 
                  className="w-10 h-10 rounded-md flex items-center justify-center"
                  style={{ backgroundColor: project.color }}
                >
                  <Icon icon={project.icon || "lucide:folder"} className="text-white text-xl" />
                </div>
                <h3 className="text-xl font-semibold">{project.name}</h3>
              </div>
              <Button 
                isIconOnly
                variant="light"
                onPress={onClose}
                className="text-default-500"
              >
                <Icon icon="lucide:x" />
              </Button>
            </ModalHeader>
            
            <ModalBody className="p-0">
              <Tabs 
                aria-label="Project Details" 
                selectedKey={activeTab}
                onSelectionChange={(key) => setActiveTab(key as string)}
                className="w-full"
                variant="underlined"
                classNames={{
                  tabList: "px-6 pt-2",
                  cursor: "bg-primary",
                  tab: "px-6",
                  panel: "p-6 pt-4"
                }}
              >
                <Tab 
                  key="overview" 
                  title={
                    <div className="flex items-center gap-1">
                      <Icon icon="lucide:layout-dashboard" />
                      <span>Overview</span>
                    </div>
                  }
                >
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="md:col-span-2 space-y-6">
                      {/* Project Description */}
                      <Card className="bg-content1 shadow-none">
                        <CardBody>
                          <h4 className="text-lg font-medium mb-2">About This Project</h4>
                          <p className="text-default-500">{project.description || "No description available."}</p>
                        </CardBody>
                      </Card>
                      
                      {/* Project Progress */}
                      <Card className="bg-content1 shadow-none">
                        <CardBody>
                          <div className="flex justify-between items-center mb-2">
                            <h4 className="text-lg font-medium">Progress</h4>
                            <span className="text-xl font-semibold">{stats.progressPercentage}%</span>
                          </div>
                          <Progress 
                            value={stats.progressPercentage} 
                            color="primary"
                            className="h-2 mb-4"
                          />
                          
                          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-4">
                            <div className="p-4 bg-content2 rounded-lg">
                              <div className="flex justify-between items-center mb-1">
                                <span className="text-default-500">Tasks</span>
                                <span className="text-medium">{stats.tasksCount}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Icon icon="lucide:check-circle" className="text-success" />
                                <span className="text-sm">{stats.completedTasksCount} completed</span>
                              </div>
                            </div>
                            
                            <div className="p-4 bg-content2 rounded-lg">
                              <div className="flex justify-between items-center mb-1">
                                <span className="text-default-500">Hours Spent</span>
                                <span className="text-medium">{stats.totalHours}h</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Icon icon="lucide:users" className="text-primary" />
                                <span className="text-sm">{stats.teamMembers.length} contributors</span>
                              </div>
                            </div>
                            
                            <div className="p-4 bg-content2 rounded-lg">
                              <div className="flex justify-between items-center mb-1">
                                <span className="text-default-500">Deadline</span>
                                <span className="text-medium">
                                  {project.deadline ? new Date(project.deadline).toLocaleDateString() : 'N/A'}
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Icon icon="lucide:calendar" className={project.deadline ? "text-warning" : "text-default-500"} />
                                <span className="text-sm">
                                  {project.deadline ? 
                                    `${Math.round((new Date(project.deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days left` : 
                                    'No deadline set'}
                                </span>
                              </div>
                            </div>
                          </div>
                        </CardBody>
                      </Card>
                      
                      {/* Team Members */}
                      <Card className="bg-content1 shadow-none">
                        <CardBody>
                          <h4 className="text-lg font-medium mb-4">Team</h4>
                          <div className="space-y-3">
                            {stats.teamMembers.map(member => (
                              <div key={member.id} className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <Avatar src={member.avatar} name={member.name} />
                                  <div>
                                    <p className="font-medium">{member.name}</p>
                                    <p className="text-sm text-default-500">{member.role}</p>
                                  </div>
                                </div>
                                <div className="text-right">
                                  <p className="font-medium">{member.tasksCount} tasks</p>
                                  <p className="text-sm text-default-500">{member.hoursSpent}h tracked</p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardBody>
                      </Card>
                    </div>
                    
                    <div className="space-y-6">
                      {/* Recent AI Reports */}
                      <Card className="bg-content1 shadow-none">
                        <CardBody>
                          <div className="flex justify-between items-center mb-4">
                            <h4 className="text-lg font-medium">AI Reports</h4>
                            <Button 
                              size="sm"
                              variant="flat"
                              color="secondary"
                              onPress={() => setActiveTab("ai-reports")}
                            >
                              View All
                            </Button>
                          </div>
                          
                          {aiReports.length > 0 ? (
                            <div className="space-y-3">
                              {aiReports.slice(0, 2).map(report => (
                                <div 
                                  key={report.id}
                                  className="p-3 rounded-lg bg-content2 flex items-center justify-between"
                                >
                                  <div className="flex items-center gap-3">
                                    <div className="p-2 rounded-full bg-secondary/20">
                                      <Icon icon="lucide:sparkles" className="text-secondary" />
                                    </div>
                                    <div>
                                      <p className="font-medium">{report.title}</p>
                                      <p className="text-xs text-default-500">{new Date(report.date).toLocaleDateString()}</p>
                                    </div>
                                  </div>
                                  <Button 
                                    isIconOnly 
                                    size="sm" 
                                    variant="light"
                                  >
                                    <Icon icon="lucide:chevron-right" />
                                  </Button>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="text-default-500 text-center py-4">No AI reports yet</p>
                          )}
                        </CardBody>
                      </Card>
                      
                      {/* Links */}
                      <Card className="bg-content1 shadow-none">
                        <CardBody>
                          <div className="flex justify-between items-center mb-4">
                            <h4 className="text-lg font-medium">Links</h4>
                            <Button
                              isIconOnly
                              size="sm"
                              variant="flat"
                              color="primary"
                              onPress={toggleLinkForm}
                            >
                              <Icon icon={showLinkForm ? "lucide:minus" : "lucide:plus"} />
                            </Button>
                          </div>
                          
                          {links.length > 0 ? (
                            <div className="space-y-3 mb-4">
                              {links.map((link, index) => (
                                <a 
                                  key={index} 
                                  href={link.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="p-3 rounded-lg bg-content2 flex items-center justify-between hover:bg-content3 transition-colors"
                                >
                                  <div className="flex items-center gap-3">
                                    <div className="p-2 rounded-full bg-primary/20">
                                      <Icon icon="lucide:link" className="text-primary" />
                                    </div>
                                    <p className="font-medium">{link.title}</p>
                                  </div>
                                  <Icon icon="lucide:external-link" className="text-default-500" />
                                </a>
                              ))}
                            </div>
                          ) : (
                            <p className="text-default-500 text-center py-2 mb-4">No links yet</p>
                          )}
                          
                          {showLinkForm && (
                            <div className="space-y-2">
                              <Input
                                placeholder="Link title"
                                value={newLinkTitle}
                                onValueChange={setNewLinkTitle}
                                size="sm"
                              />
                              <Input
                                placeholder="URL"
                                value={newLinkUrl}
                                onValueChange={setNewLinkUrl}
                                size="sm"
                              />
                              <Button
                                color="primary"
                                size="sm"
                                fullWidth
                                startContent={<Icon icon="lucide:plus" />}
                                onPress={handleAddLink}
                                isDisabled={!newLinkTitle.trim() || !newLinkUrl.trim()}
                              >
                                Add Link
                              </Button>
                            </div>
                          )}
                        </CardBody>
                      </Card>
                      
                      {/* Documents Preview */}
                      <Card className="bg-content1 shadow-none">
                        <CardBody>
                          <div className="flex justify-between items-center mb-4">
                            <h4 className="text-lg font-medium">Documents</h4>
                            <Button 
                              size="sm"
                              variant="flat"
                              color="primary"
                              onPress={() => setActiveTab("documents")}
                            >
                              View All
                            </Button>
                          </div>
                          
                          {documents.length > 0 ? (
                            <div className="space-y-3">
                              {documents.slice(0, 3).map(doc => (
                                <div 
                                  key={doc.id}
                                  className="p-3 rounded-lg bg-content2 flex items-center justify-between"
                                >
                                  <div className="flex items-center gap-3">
                                    <div className="p-2 rounded-full bg-warning/20">
                                      <Icon 
                                        icon={
                                          doc.type === 'pdf' ? "lucide:file-text" : 
                                          doc.type === 'doc' ? "lucide:file" :
                                          "lucide:file-image"
                                        } 
                                        className="text-warning" 
                                      />
                                    </div>
                                    <div>
                                      <p className="font-medium">{doc.name}</p>
                                      <p className="text-xs text-default-500">{doc.size}</p>
                                    </div>
                                  </div>
                                  <Button 
                                    isIconOnly 
                                    size="sm" 
                                    variant="light"
                                  >
                                    <Icon icon="lucide:download" />
                                  </Button>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="text-default-500 text-center py-4">No documents yet</p>
                          )}
                        </CardBody>
                      </Card>
                    </div>
                  </div>
                </Tab>
                
                <Tab 
                  key="todos" 
                  title={
                    <div className="flex items-center gap-1">
                      <Icon icon="lucide:check-square" />
                      <span>Todo List</span>
                    </div>
                  }
                >
                  <Card className="bg-content1 shadow-none mb-6">
                    <CardBody>
                      <h4 className="text-lg font-medium mb-4">Project Todo List</h4>
                      
                      <div className="space-y-3 mb-6">
                        <Input
                          placeholder="Add new todo item..."
                          value={newTodoTitle}
                          onValueChange={setNewTodoTitle}
                          className="w-full"
                        />
                        
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                          <DatePicker 
                            label="Start Date"
                            value={newTodoStartDate ? parseDate(newTodoStartDate.toISOString().split('T')[0]) : undefined}
                            onChange={(date) => setNewTodoStartDate(date ? new Date(date.toString()) : null)}
                            className="w-full"
                          />
                          
                          <DatePicker 
                            label="Due Date"
                            value={newTodoDueDate ? parseDate(newTodoDueDate.toISOString().split('T')[0]) : undefined}
                            onChange={(date) => setNewTodoDueDate(date ? new Date(date.toString()) : null)}
                            className="w-full"
                          />
                        </div>
                        
                        <Button
                          color="primary"
                          fullWidth
                          onPress={handleAddTodo}
                          isDisabled={!newTodoTitle.trim()}
                        >
                          Add Todo
                        </Button>
                      </div>
                      
                      <div className="space-y-3">
                        {todos.length > 0 ? (
                          todos.map(todo => (
                            <div 
                              key={todo.id}
                              className={`p-4 rounded-lg ${todo.completed ? 'bg-success/10' : 'bg-content2'}`}
                            >
                              <div className="flex items-start gap-3">
                                <Checkbox 
                                  isSelected={todo.completed}
                                  onValueChange={(checked) => handleToggleTodo(todo.id, checked)}
                                  size="lg"
                                  color={todo.completed ? "success" : "primary"}
                                />
                                <div className="flex-1">
                                  <p className={`font-medium ${todo.completed ? 'line-through text-default-400' : ''}`}>
                                    {todo.title}
                                  </p>
                                  <div className="flex flex-wrap items-center gap-x-3 gap-y-1 mt-1">
                                    {todo.startDate && (
                                      <div className="flex items-center gap-1">
                                        <Icon 
                                          icon="lucide:play" 
                                          className="text-xs text-default-500" 
                                        />
                                        <span className="text-xs text-default-500">
                                          Start: {new Date(todo.startDate).toLocaleDateString()}
                                        </span>
                                      </div>
                                    )}
                                    
                                    {todo.dueDate && (
                                      <div className="flex items-center gap-1">
                                        <Icon 
                                          icon="lucide:flag" 
                                          className={`text-xs ${
                                            new Date(todo.dueDate) < new Date() && !todo.completed
                                              ? 'text-danger'
                                              : 'text-default-500'
                                          }`} 
                                        />
                                        <span 
                                          className={`text-xs ${
                                            new Date(todo.dueDate) < new Date() && !todo.completed
                                              ? 'text-danger'
                                              : 'text-default-500'
                                          }`}
                                        >
                                          Due: {new Date(todo.dueDate).toLocaleDateString()}
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                <div className="flex gap-1">
                                  {todo.assignees && todo.assignees.length > 0 && (
                                    <AvatarGroup max={3} size="sm">
                                      {todo.assignees.map(assignee => (
                                        <Avatar 
                                          key={assignee.id}
                                          src={assignee.avatar} 
                                          name={assignee.name} 
                                        />
                                      ))}
                                    </AvatarGroup>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))
                        ) : (
                          <p className="text-default-500 text-center py-6">No todo items yet. Add one to get started!</p>
                        )}
                      </div>
                    </CardBody>
                  </Card>
                  
                  <Card className="bg-content1 shadow-none">
                    <CardBody>
                      <h4 className="text-lg font-medium mb-4">Project Progress</h4>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-default-500">Overall Progress</span>
                        <span className="font-medium">{stats.progressPercentage}%</span>
                      </div>
                      <Progress 
                        value={stats.progressPercentage} 
                        color="primary"
                        className="h-2 mb-6"
                      />
                      
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-default-500">Todo</span>
                          <span>{stats.todoCount} items</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-default-500">Completed</span>
                          <span>{stats.completedTodoCount} items</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-default-500">Overdue</span>
                          <span className="text-danger">{stats.overdueTodoCount} items</span>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                </Tab>
                
                <Tab 
                  key="gantt" 
                  title={
                    <div className="flex items-center gap-1">
                      <Icon icon="lucide:bar-chart-horizontal" />
                      <span>Gantt Chart</span>
                    </div>
                  }
                >
                  <Card className="bg-content1 shadow-none">
                    <CardBody>
                      <h4 className="text-lg font-medium mb-4">Project Timeline</h4>
                      <GanttChart todos={todos.filter(todo => todo.dueDate)} />
                    </CardBody>
                  </Card>
                </Tab>
                
                <Tab 
                  key="documents" 
                  title={
                    <div className="flex items-center gap-1">
                      <Icon icon="lucide:file" />
                      <span>Documents</span>
                    </div>
                  }
                >
                  <div className="space-y-6">
                    <div className="flex justify-between items-center">
                      <h4 className="text-lg font-medium">Project Documents</h4>
                      <Button 
                        color="primary"
                        startContent={<Icon icon="lucide:upload" />}
                      >
                        Upload Document
                      </Button>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                      {documents.length > 0 ? (
                        documents.map(doc => (
                          <DocumentCard key={doc.id} document={doc} />
                        ))
                      ) : (
                        <div className="col-span-full flex flex-col items-center justify-center py-16">
                          <Icon icon="lucide:file" className="text-4xl text-default-400 mb-3" />
                          <p className="text-default-500">No documents uploaded yet</p>
                          <Button 
                            color="primary"
                            variant="flat"
                            startContent={<Icon icon="lucide:upload" />}
                            className="mt-3"
                          >
                            Upload First Document
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </Tab>
                
                <Tab 
                  key="ai-reports" 
                  title={
                    <div className="flex items-center gap-1">
                      <Icon icon="lucide:sparkles" />
                      <span>AI Reports</span>
                    </div>
                  }
                >
                  <div className="space-y-6">
                    <h4 className="text-lg font-medium">AI Generated Reports</h4>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {aiReports.length > 0 ? (
                        aiReports.map(report => (
                          <AIReportCard key={report.id} report={report} />
                        ))
                      ) : (
                        <div className="col-span-full flex flex-col items-center justify-center py-16">
                          <Icon icon="lucide:sparkles" className="text-4xl text-default-400 mb-3" />
                          <p className="text-default-500">No AI reports generated yet</p>
                          <Button 
                            color="secondary"
                            startContent={<Icon icon="lucide:sparkles" />}
                            className="mt-3"
                          >
                            Generate First Report
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </Tab>
              </Tabs>
            </ModalBody>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};