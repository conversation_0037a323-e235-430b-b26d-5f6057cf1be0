import React from "react";
    import { Card, CardBody, Table, TableHeader, TableColumn, TableBody, TableRow, TableCell, Chip, Avatar } from "@heroui/react";
    import { Icon } from "@iconify/react";

    export const ProjectsList: React.FC = () => {
      const projects = [
        {
          id: "1",
          name: "Website Redesign",
          status: "In Progress",
          progress: 65,
          members: 4,
          deadline: "2024-08-15"
        },
        {
          id: "2",
          name: "Mobile App Development",
          status: "Active",
          progress: 32,
          members: 6,
          deadline: "2024-09-20"
        },
        {
          id: "3",
          name: "Marketing Campaign",
          status: "Completed",
          progress: 100,
          members: 3,
          deadline: "2024-06-30"
        },
        {
          id: "4",
          name: "E-commerce Platform",
          status: "On Hold",
          progress: 18,
          members: 5,
          deadline: "2024-10-10"
        }
      ];
      
      const getStatusColor = (status: string) => {
        switch (status) {
          case "Completed": return "success";
          case "In Progress": return "primary";
          case "Active": return "secondary";
          case "On Hold": return "warning";
          default: return "default";
        }
      };
      
      return (
        <Card className="bg-custom-card border-custom-border shadow-none">
          <CardBody>
            <Table 
              aria-label="Projects list"
              removeWrapper 
              classNames={{
                base: "border-collapse border-spacing-0",
                table: "min-w-full",
                thead: "bg-custom-sidebar",
                th: "text-left text-sm font-medium text-custom-muted py-3 px-4",
                td: "text-sm text-white border-t border-custom-border py-4 px-4",
              }}
            >
              <TableHeader>
                <TableColumn>PROJECT</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>PROGRESS</TableColumn>
                <TableColumn>TEAM</TableColumn>
                <TableColumn>DEADLINE</TableColumn>
                <TableColumn>ACTIONS</TableColumn>
              </TableHeader>
              <TableBody>
                {projects.map((project) => (
                  <TableRow key={project.id}>
                    <TableCell>
                      <div className="font-medium">{project.name}</div>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        color={getStatusColor(project.status)} 
                        size="sm" 
                        variant="flat"
                      >
                        {project.status}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="w-24 h-2 bg-custom-border rounded-full overflow-hidden">
                          <div 
                            className={`h-full rounded-full ${
                              project.progress === 100 ? 'bg-success' : 'bg-primary'
                            }`}
                            style={{ width: `${project.progress}%` }}
                          />
                        </div>
                        <span className="text-xs text-custom-muted">{project.progress}%</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex -space-x-2">
                        {Array(project.members).fill(0).map((_, index) => (
                          <Avatar 
                            key={index}
                            className="border-2 border-custom-card"
                            size="sm"
                            src={`https://img.heroui.chat/image/avatar?w=32&h=32&u=${project.id}_${index}`}
                          />
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Icon icon="lucide:calendar" className="text-custom-muted" />
                        <span>
                          {new Date(project.deadline).toLocaleDateString('en-US', { 
                            month: 'short', 
                            day: 'numeric', 
                            year: 'numeric' 
                          })}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <button className="p-1 rounded hover:bg-custom-border text-custom-muted hover:text-white">
                          <Icon icon="lucide:edit" />
                        </button>
                        <button className="p-1 rounded hover:bg-custom-border text-custom-muted hover:text-white">
                          <Icon icon="lucide:trash" />
                        </button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardBody>
        </Card>
      );
    };