import React from "react";
    import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import type { AIReport } from "../types";

    interface AIReportCardProps {
      report: AIReport;
    }

    export const AIReportCard: React.FC<AIReportCardProps> = ({ report }) => {
      return (
        <Card className="bg-content1 shadow-none hover:bg-content2 transition-colors cursor-pointer">
          <CardBody className="p-4">
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-full bg-secondary/20">
                <Icon icon="lucide:sparkles" className="text-secondary" />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="text-md font-medium">{report.title}</h4>
                  {report.isNew && (
                    <Chip size="sm" color="secondary" variant="flat">New</Chip>
                  )}
                </div>
                <p className="text-sm text-default-500 mb-3">{report.summary}</p>
                <div className="flex justify-between items-center">
                  <div className="text-xs text-default-500">
                    {new Date(report.date).toLocaleDateString()}
                  </div>
                  <Button 
                    size="sm" 
                    color="secondary" 
                    variant="flat"
                  >
                    View Report
                  </Button>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      );
    };