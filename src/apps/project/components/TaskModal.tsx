import React from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>nt, 
  <PERSON>dal<PERSON>eader, 
  ModalBody, 
  Button,
  Avatar,
  AvatarGroup,
  Tooltip,
  Tabs,
  Tab,
  Card,
  CardBody,
  Input,
  Textarea,
  Select,
  SelectItem,
  Chip,
  Badge,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  DropdownSection,
  Progress,
  Slider,
  DatePicker,
  Divider
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { parseDate } from "@internationalized/date";
import { useDateFormatter } from "@react-aria/i18n";

// Remove the circular import that causes the cycle
// import { TaskModal as ModularTaskModal } from "./TaskModal";

// Instead, directly export the implementation
export const TaskModal: React.FC<any> = (props) => {
  // Forward all props to the actual implementation
  return (
    <React.Suspense fallback={<div className="p-4">Loading task details...</div>}>
      <TaskModalImplementation {...props} />
    </React.Suspense>
  );
};

// Use dynamic import to break the circular dependency
const TaskModalImplementation = React.lazy(() => 
  import('./TaskModal/index').then(module => ({
    default: module.TaskModal
  }))
);

// Remove this line that's causing the error
// Export the modular version to maintain the API
// export { ModularTaskModal as TaskModal };