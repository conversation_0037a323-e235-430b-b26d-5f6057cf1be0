import React from "react";
import { Avatar, AvatarGroup, Tooltip } from "@heroui/react";
import { Icon } from "@iconify/react";
import type { Task } from "../types";
import { differenceInDays, parseISO } from 'date-fns';

interface TaskCardProps {
  task: Task;
  onPress?: () => void;
  className?: string;
}

export const TaskCard: React.FC<TaskCardProps> = ({ 
  task,
  onPress = () => {}, 
  className = ""
}) => {
  const [isExpanded, setIsExpanded] = React.useState(false);
  
  const handleContentClick = () => {
    onPress();
  };
  
  const getTagColor = (tag: string) => {
    switch (tag) {
      case "Design": return "text-purple-500 bg-purple-500/10";
      case "Development": return "text-primary bg-primary/10";
      case "Bug": return "text-red-500 bg-red-500/10";
      case "Feature": return "text-green-500 bg-green-500/10";
      case "Documentation": return "text-yellow-500 bg-yellow-500/10";
      case "Setup": return "text-cyan-500 bg-cyan-500/10";
      case "Architecture": return "text-orange-500 bg-orange-500/10";
      default: return "text-custom-text bg-custom-border/30";
    }
  };
  
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "High": return <Icon icon="lucide:flag" className="text-red-500" />;
      case "Medium": return <Icon icon="lucide:flag" className="text-yellow-500" />;
      case "Low": return <Icon icon="lucide:flag" className="text-green-500" />;
      default: return <Icon icon="lucide:flag" className="text-custom-muted" />;
    }
  };

  const getDueDateStatus = () => {
    if (!task.dueDate) return null;
    
    const today = new Date();
    const dueDate = parseISO(task.dueDate);
    const daysLeft = differenceInDays(dueDate, today);
    
    if (daysLeft < 0) {
      return {
        label: 'Overdue',
        color: 'text-red-500 bg-red-500/10'
      };
    } else if (daysLeft <= 2) {
      return {
        label: `${daysLeft} day${daysLeft !== 1 ? 's' : ''} left`,
        color: 'text-yellow-500 bg-yellow-500/10'
      };
    } else {
      return {
        label: `${daysLeft} days left`,
        color: 'text-green-500 bg-green-500/10'
      };
    }
  };

  const dueDateStatus = getDueDateStatus();
  
  return (
    <div 
      className={`p-4 bg-custom-sidebar rounded-lg cursor-pointer hover:bg-custom-border/30 transition-colors min-h-[120px] flex flex-col justify-between border border-transparent hover:border-custom-border/50 shadow-sm ${className}`}
    >
      <div>
        <div className="flex justify-between items-center mb-3">
          <div className="flex gap-2 flex-wrap">
            <span className={`px-2 py-0.5 rounded text-xs ${getTagColor(task.tag)}`}>
              {task.tag}
            </span>
            <span 
              className="px-2 py-0.5 rounded text-xs" 
              style={{ 
                backgroundColor: `${task.project.color}20`,
                color: task.project.color 
              }}
            >
              {task.project.name}
            </span>
          </div>
          {getPriorityIcon(task.priority)}
        </div>
        
        <h4 
          className={`text-white text-sm font-medium mb-2 ${isExpanded ? '' : 'line-clamp-2'}`}
          onClick={handleContentClick}
        >
          {task.title}
        </h4>
        
        {task.description && (
          <p 
            className={`text-custom-muted text-xs ${isExpanded ? '' : 'line-clamp-2'} mb-3`}
            onClick={handleContentClick}
          >
            {task.description}
          </p>
        )}
      </div>
      
      <div className="mt-auto">
        <div className="flex justify-between items-center">
          <div>
            <AvatarGroup max={3} size="sm">
              {task.assignees.map((assignee) => (
                <Tooltip
                  key={assignee.id}
                  content={`${assignee.name} • ${assignee.role}`}
                  placement="top"
                >
                  <Avatar
                    src={assignee.avatar}
                    className="h-6 w-6 text-tiny"
                  />
                </Tooltip>
              ))}
            </AvatarGroup>
          </div>
          
          {dueDateStatus && (
            <span className={`text-xs px-2 py-0.5 rounded-full flex items-center gap-1 ${dueDateStatus.color}`}>
              <Icon icon="lucide:calendar" className="text-xs" />
              {dueDateStatus.label}
            </span>
          )}
        </div>
        
        {(task.attachments?.length > 0 || task.comments?.length > 0) && (
          <div className="flex gap-3 mt-2 pt-2 border-t border-custom-border">
            {task.attachments?.length > 0 && (
              <span className="text-xs text-custom-muted flex items-center">
                <Icon icon="lucide:paperclip" className="text-xs mr-1" />
                {task.attachments.length}
              </span>
            )}
            
            {task.comments?.length > 0 && (
              <span className="text-xs text-custom-muted flex items-center">
                <Icon icon="lucide:message-circle" className="text-xs mr-1" />
                {task.comments.length}
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
};