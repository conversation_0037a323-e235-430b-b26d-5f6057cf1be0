import React, { useMemo } from "react";
import type { ProjectTodo, Project } from "../types";
import { Tooltip } from "@heroui/react";

interface GanttChartProps {
  todos: ProjectTodo[];
  projectMap?: Record<string, Project>;
}

// Helper to convert HEX to RGBA with given alpha
const hexToRgba = (hex: string, alpha: number) => {
  let c = hex.replace('#', '');
  if (c.length === 3) c = c.split('').map(ch=>ch+ch).join('');
  const r = parseInt(c.substring(0,2),16);
  const g = parseInt(c.substring(2,4),16);
  const b = parseInt(c.substring(4,6),16);
  return `rgba(${r},${g},${b},${alpha})`;
};

export const GanttChart: React.FC<GanttChartProps> = ({ todos, projectMap = {} }) => {
  const dayMs = 1000 * 60 * 60 * 24;
  const toDay = (d: Date) => new Date(d.getFullYear(), d.getMonth(), d.getDate());

  // width in pixels for each day column
  const dayWidthPx = 40;

  // Get min and max dates to determine chart range
  const { minDate, maxDate, dateRange } = useMemo(() => {
    if (todos.length === 0) {
      const today = new Date();
      const oneMonthLater = new Date();
      oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);
      return { 
        minDate: today, 
        maxDate: oneMonthLater,
        dateRange: 30
      };
    }
    
    const datesDue = todos.map(todo => toDay(new Date(todo.dueDate as string)));
    const datesStart = todos.map(todo => toDay(new Date(todo.startDate || todo.createdAt)));
    const minStart = new Date(Math.min(...datesStart.map(d => d.getTime())) - 1000*60*60*24*1);
    const maxDue = new Date(Math.max(...datesDue.map(d => d.getTime())) + 1000*60*60*24*1);
    const min = minStart;
    const max = maxDue;
    
    const diffDays = Math.ceil((max.getTime() - min.getTime()) / (1000 * 60 * 60 * 24));
    const totalDays = diffDays + 1; // include both start and end day
    const rangeMinus1 = totalDays - 1;
    
    return { 
      minDate: min, 
      maxDate: max,
      dateRange: totalDays
    };
  }, [todos]);

  const dateHeaders = useMemo(() => {
    const dates = [];
    const currentDate = new Date(minDate);
    
    while (currentDate <= maxDate) {
      dates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return dates;
  }, [minDate, maxDate]);

  const chartWidth = dateHeaders.length * dayWidthPx;

  // Format date for display in tooltip
  const formatDate = (dateString: string) => {
    if (!dateString) return "Not set";
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Calculate position and width for each todo item
  const todoPositions = useMemo(() => {
    const positions = todos.map(todo => {
      const dueDate = toDay(new Date(todo.dueDate as string));
      const startDate = toDay(new Date(todo.startDate || todo.createdAt));
      
      const startDiff = Math.floor((startDate.getTime() - minDate.getTime()) / dayMs);
      const durationDays = Math.max(1, ( (dueDate.getTime() - startDate.getTime()) / dayMs) + 1);
      
      return {
        ...todo,
        leftPx: startDiff * dayWidthPx,
        widthPx: durationDays * dayWidthPx
      };
    });
    
    return positions;
  }, [todos, minDate, dateRange]);

  return (
    <div className="relative pb-4">
      <div className="overflow-x-auto">
        <div style={{width: `${chartWidth + dayWidthPx}px`}}>
          <div>
            {/* Header with dates */}
             {/* Today indicator */}
             <div className="relative h-6 mt-4">
              <div className="absolute top-0 h-full w-px bg-danger" style={{ 
                // left: `${((toDay(new Date()).getTime() - minDate.getTime()) / dayMs) * dayWidthPx}px` 
                left: `${5 * dayWidthPx}px`, 
                top: `-10px`,
              }}>
                <div className="absolute -top-5 -translate-x-1/2 bg-danger text-white text-xs px-1 py-0.5 rounded">Today</div>
              </div>
            </div>
            <div className="flex border-b border-default-200 mb-4">
              
              <div className="w-[40px] shrink-0 p-3 font-medium sticky left-0 z-20 bg-custom-card"></div>
               
              <div className="flex" style={{width: `${chartWidth}px`}}>
                {dateHeaders.map((date, i) => (
                  <div 
                    key={i}
                    className={`flex-none text-center text-xs p-2 ${
                      date.getDate() === 1 || i === 0 ? 'border-l border-default-200' : ''
                    } ${date.getDay() === 0 || date.getDay() === 6 ? 'bg-default-50' : ''}`}
                    style={{width: `${dayWidthPx}px`}}
                  >
                    <div className="font-medium">{date.getDate()}</div>
                    <div className="text-default-500">{date.toLocaleString('default', { month: 'short' })}</div>
                  </div>
                ))}
              </div>
            
            </div>
            
            {/* Gantt body */}
            <div>
              {(() => {
                const groups: Record<string, typeof todoPositions> = {};
                todoPositions.forEach(tp=>{
                  if(!groups[tp.projectId]) groups[tp.projectId]=[];
                  groups[tp.projectId].push(tp);
                });
                return Object.keys(groups).map(projectId=>{
                  const rows = groups[projectId];
                  const project = projectMap[projectId];
                  return (
                    <div key={projectId} className="flex  my-2">
                      {/* Sticky project label cell */}
                      <div
                        className="w-[40px] h-full mt-2 rounded-md shrink-0 sticky left-0 z-10 bg-custom-card flex justify-center overflow-hidden items-start"
                        style={{
                          backgroundColor: project?.color ? hexToRgba(project.color,1):undefined,
                          height: `${(rows.length * 2.5)+0.5}rem`
                        }}
                      >
                        <div className="flex flex-col items-center mt-2">
                          <Tooltip
                            content={<span className="px-2 py-1 text-xs">{project?.name || projectId}</span>}
                            placement="right"
                          >
                            <div className="text-xs font-medium rotate-90 whitespace-nowrap cursor-default">
                              {project?.icon || projectId}
                            </div>
                          </Tooltip>
                        </div>
                      </div>

                      {/* Rows for tasks */}
                      <div className="flex-1 ">
                        {rows.map(todo=> (
                          <div key={todo.id} className="flex items-center h-10  mt-2" style={{backgroundColor: project?.color ? hexToRgba(project.color,0.07):undefined}}>
                            <div className="flex-1 relative h-6 px-2" style={{width: `${chartWidth}px`}}>
                              <Tooltip
                                content={
                                  <div className="px-2 py-1 max-w-xs whitespace-normal">
                                    <div className="text-sm font-medium whitespace-normal break-words">{todo.title}</div>
                                    <div className="text-xs flex flex-col gap-1 mt-1">
                                      <div className="flex items-center">
                                        <span className="text-default-400 mr-2">Start:</span>
                                        {formatDate(todo.startDate || todo.createdAt)}
                                      </div>
                                      <div className="flex items-center">
                                        <span className="text-default-400 mr-2">Due:</span>
                                        {formatDate(todo.dueDate as string)}
                                      </div>
                                      {todo.completed && 
                                        <div className="text-success mt-1">✓ Completed</div>
                                      }
                                    </div>
                                  </div>
                                }
                                placement="top"
                                classNames={{
                                  base: "px-0 py-0",
                                  content: "bg-content1 border border-default-200"
                                }}
                              >
                                <div 
                                  className={`absolute top-0 h-full rounded-md cursor-pointer ${
                                    todo.completed ? 'bg-success/20 border border-success/30' : 
                                    new Date(todo.dueDate as string) < new Date() ? 'bg-danger/20 border border-danger/30' : 
                                    'bg-primary/20 border border-primary/30'
                                  }`}
                                  style={{ 
                                    left: `${todo.leftPx}px`, 
                                    width: `${todo.widthPx}px` 
                                  }}
                                >
                                  {todo.widthPx > 60 && (
                                    <div className="px-2 py-1 text-xs truncate">
                                      {todo.title}
                                    </div>
                                  )}
                                </div>
                              </Tooltip>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                });
              })()}
            </div>
            
            
          </div>
        </div>
      </div>
    </div>
  );
};