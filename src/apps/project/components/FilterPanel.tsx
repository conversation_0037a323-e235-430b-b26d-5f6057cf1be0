import React from "react";
import { 
  Card, 
  CardBody, 
  Button,
  Checkbox,
  RadioGroup,
  Radio
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { mockProjects, mockUsers } from "../data/mock-data";

interface FilterPanelProps {
  onClose: () => void;
  onApply?: (filters: {
    priorities: string[],
    tags: string[],
    assignees: string[],
    showCompleted: boolean,
    sort: string,
    order: "asc" | "desc"
  }) => void;
  initialFilters?: {
    priorities: string[],
    tags: string[],
    assignees: string[],
    showCompleted: boolean,
    sort: string,
    order: "asc" | "desc"
  };
}

export const FilterPanel: React.FC<FilterPanelProps> = ({ 
  onClose, 
  onApply,
  initialFilters = {
    priorities: [],
    tags: [],
    assignees: [],
    showCompleted: true,
    sort: "dueDate",
    order: "asc"
  }
}) => {
  const [selectedProjects, setSelectedProjects] = React.useState<string[]>([]);
  const [selectedTags, setSelectedTags] = React.useState<string[]>(initialFilters.tags);
  const [selectedAssignees, setSelectedAssignees] = React.useState<string[]>(initialFilters.assignees);
  const [selectedPriorities, setSelectedPriorities] = React.useState<string[]>(initialFilters.priorities);
  const [showCompleted, setShowCompleted] = React.useState<boolean>(initialFilters.showCompleted);
  const [sortBy, setSortBy] = React.useState<string>(initialFilters.sort);
  const [sortOrder, setSortOrder] = React.useState<"asc" | "desc">(initialFilters.order);
  
  // Get all unique tags from tasks
  const allTags = ["Bug", "Feature", "Design", "Development", "Documentation", "Setup", "Architecture"];
  
  const handleProjectChange = (projectId: string) => {
    setSelectedProjects((prev) => 
      prev.includes(projectId)
        ? prev.filter((id) => id !== projectId)
        : [...prev, projectId]
    );
  };
  
  const handleAssigneeChange = (userId: string) => {
    setSelectedAssignees((prev) => 
      prev.includes(userId)
        ? prev.filter((id) => id !== userId)
        : [...prev, userId]
    );
  };
  
  const handlePriorityChange = (priority: string) => {
    setSelectedPriorities((prev) => 
      prev.includes(priority)
        ? prev.filter((p) => p !== priority)
        : [...prev, priority]
    );
  };
  
  const handleTagChange = (tag: string) => {
    setSelectedTags((prev) => 
      prev.includes(tag)
        ? prev.filter((t) => t !== tag)
        : [...prev, tag]
    );
  };
  
  const handleReset = () => {
    setSelectedProjects([]);
    setSelectedAssignees([]);
    setSelectedPriorities([]);
    setSelectedTags([]);
    setShowCompleted(true);
    setSortBy("dueDate");
    setSortOrder("asc");
  };
  
  const handleApply = () => {
    if (onApply) {
      onApply({
        priorities: selectedPriorities,
        tags: selectedTags,
        assignees: selectedAssignees,
        showCompleted,
        sort: sortBy,
        order: sortOrder
      });
    }
    onClose();
  };
  
  return (
    <Card className="bg-custom-card border-custom-border shadow-none mb-6">
      <CardBody>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-white text-lg font-medium">Filters</h3>
          <Button
            isIconOnly
            variant="light"
            onPress={onClose}
          >
            <Icon icon="lucide:x" className="text-custom-muted" />
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div>
            <h4 className="text-sm font-medium text-custom-muted mb-3">Projects</h4>
            <div className="space-y-2">
              {mockProjects.map((project) => (
                <Checkbox 
                  key={project.id}
                  isSelected={selectedProjects.includes(project.id)}
                  onValueChange={() => handleProjectChange(project.id)}
                >
                  <span className="flex items-center gap-2">
                    <span 
                      className="h-3 w-3 rounded-full"
                      style={{ backgroundColor: project.color }}
                    />
                    {project.name}
                  </span>
                </Checkbox>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-custom-muted mb-3">Assignees</h4>
            <div className="space-y-2">
              {mockUsers.map((user) => (
                <Checkbox 
                  key={user.id}
                  isSelected={selectedAssignees.includes(user.id)}
                  onValueChange={() => handleAssigneeChange(user.id)}
                >
                  <span className="flex items-center gap-2">
                    <img 
                      src={user.avatar} 
                      className="h-5 w-5 rounded-full object-cover" 
                      alt={user.name} 
                    />
                    {user.name}
                  </span>
                </Checkbox>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-custom-muted mb-3">Priority</h4>
            <div className="space-y-2">
              <Checkbox 
                isSelected={selectedPriorities.includes("High")}
                onValueChange={() => handlePriorityChange("High")}
              >
                <span className="flex items-center gap-2">
                  <Icon icon="lucide:flag" className="text-red-500" />
                  High
                </span>
              </Checkbox>
              <Checkbox 
                isSelected={selectedPriorities.includes("Medium")}
                onValueChange={() => handlePriorityChange("Medium")}
              >
                <span className="flex items-center gap-2">
                  <Icon icon="lucide:flag" className="text-yellow-500" />
                  Medium
                </span>
              </Checkbox>
              <Checkbox 
                isSelected={selectedPriorities.includes("Low")}
                onValueChange={() => handlePriorityChange("Low")}
              >
                <span className="flex items-center gap-2">
                  <Icon icon="lucide:flag" className="text-green-500" />
                  Low
                </span>
              </Checkbox>
            </div>
            
            <h4 className="text-sm font-medium text-custom-muted mb-3 mt-6">Other Filters</h4>
            <div className="space-y-4">
              <Checkbox 
                isSelected={showCompleted}
                onValueChange={setShowCompleted}
              >
                Show completed tasks
              </Checkbox>
            </div>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-custom-muted mb-3">Tags</h4>
            <div className="space-y-2">
              {allTags.map((tag) => (
                <Checkbox 
                  key={tag}
                  isSelected={selectedTags.includes(tag)}
                  onValueChange={() => handleTagChange(tag)}
                >
                  <span className="flex items-center gap-2">
                    {tag}
                  </span>
                </Checkbox>
              ))}
            </div>
            
            {/* <h4 className="text-sm font-medium text-custom-muted mb-3 mt-6">Sort By</h4>
            <div className="space-y-2">
              <RadioGroup value={sortBy} onValueChange={setSortBy}>
                <Radio value="dueDate">Due Date</Radio>
                <Radio value="priority">Priority</Radio>
                <Radio value="title">Title</Radio>
                <Radio value="createdAt">Created Date</Radio>
              </RadioGroup>
              
              <div className="mt-3">
                <RadioGroup value={sortOrder} onValueChange={setSortOrder}>
                  <Radio value="asc">Ascending</Radio>
                  <Radio value="desc">Descending</Radio>
                </RadioGroup>
              </div>
            </div> */}

          </div>
        </div>
        
        <div className="flex justify-end mt-6 gap-3">
          <Button
            variant="flat"
            color="default"
            onPress={handleReset}
          >
            Reset Filters
          </Button>
          <Button
            color="primary"
            onPress={handleApply}
          >
            Apply Filters
          </Button>
        </div>
      </CardBody>
    </Card>
  );
};