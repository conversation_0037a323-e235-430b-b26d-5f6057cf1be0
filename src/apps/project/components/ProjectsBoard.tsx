import React from "react";
    import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@heroui/react";
    import { Icon } from "@iconify/react";

    export const ProjectsBoard: React.FC = () => {
      // This is a simplified version - in the real app, this would use the KanbanBoard component
      const columns = [
        {
          title: "To Do",
          tasks: [
            { title: "Create wireframes", tag: "Design", priority: "High" },
            { title: "Set up project repository", tag: "Setup", priority: "Medium" }
          ]
        },
        {
          title: "In Progress",
          tasks: [
            { title: "Implement authentication", tag: "Development", priority: "High" },
            { title: "Design landing page", tag: "Design", priority: "Medium" }
          ]
        },
        {
          title: "Done",
          tasks: [
            { title: "Project planning", tag: "Documentation", priority: "Medium" },
            { title: "Initial setup", tag: "Setup", priority: "Low" }
          ]
        }
      ];
      
      const getTagColor = (tag: string) => {
        switch (tag) {
          case "Design": return "text-purple-500 bg-purple-500/10";
          case "Development": return "text-primary bg-primary/10";
          case "Bug": return "text-red-500 bg-red-500/10";
          case "Feature": return "text-green-500 bg-green-500/10";
          case "Documentation": return "text-yellow-500 bg-yellow-500/10";
          case "Setup": return "text-cyan-500 bg-cyan-500/10";
          case "Architecture": return "text-orange-500 bg-orange-500/10";
          default: return "text-custom-text bg-custom-border/30";
        }
      };
      
      const getPriorityIcon = (priority: string) => {
        switch (priority) {
          case "High": return <Icon icon="lucide:flag" className="text-red-500" />;
          case "Medium": return <Icon icon="lucide:flag" className="text-yellow-500" />;
          case "Low": return <Icon icon="lucide:flag" className="text-green-500" />;
          default: return <Icon icon="lucide:flag" className="text-custom-muted" />;
        }
      };
      
      return (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {columns.map((column, columnIndex) => (
            <Card key={columnIndex} className="bg-custom-card border-custom-border shadow-none">
              <CardHeader className="flex justify-between items-center border-b border-custom-border">
                <div className="flex items-center">
                  <h3 className="text-white text-base font-medium">{column.title}</h3>
                  <span className="ml-2 px-2 py-0.5 bg-custom-border rounded-full text-xs text-custom-muted">
                    {column.tasks.length}
                  </span>
                </div>
                <Button isIconOnly variant="light" className="text-custom-muted">
                  <Icon icon="lucide:plus" />
                </Button>
              </CardHeader>
              <CardBody className="p-3 space-y-3">
                {column.tasks.map((task, taskIndex) => (
                  <div 
                    key={taskIndex} 
                    className="p-3 bg-custom-sidebar rounded-lg cursor-pointer hover:bg-custom-border/30 transition-colors"
                  >
                    <div className="flex justify-between items-center mb-2">
                      <span className={`px-2 py-0.5 rounded text-xs ${getTagColor(task.tag)}`}>
                        {task.tag}
                      </span>
                      {getPriorityIcon(task.priority)}
                    </div>
                    <h4 className="text-white text-sm font-medium">{task.title}</h4>
                  </div>
                ))}
              </CardBody>
            </Card>
          ))}
        </div>
      );
    };