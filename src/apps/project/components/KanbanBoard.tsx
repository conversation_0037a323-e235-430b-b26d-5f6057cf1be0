import React from "react";
import type { Board, Task } from "../types";
import { KanbanColumn } from "./KanbanColumn";

interface KanbanBoardProps {
  board: Board;
  tasks: Record<string, Task>;
  onTaskClick: (task: Task) => void;
  onBoardUpdate?: (updatedBoard: Board) => void;
}

export const KanbanBoard: React.FC<KanbanBoardProps> = ({ 
  board, 
  tasks, 
  onTaskClick,
  onBoardUpdate 
}) => {
  return (
      <div className="flex gap-6 md:gap-8 overflow-x-auto pb-4">
        {board.columns.map((column) => {
          const columnTasks = column.taskIds
            .map((taskId) => tasks[taskId])
            .filter(Boolean);

          // Calculate task statistics for the column
          const overdueTasks = columnTasks.filter(task => 
            task.dueDate && new Date(task.dueDate) < new Date()
          ).length;
          
          const highPriorityTasks = columnTasks.filter(task => 
            task.priority === "High"
          ).length;
          
          return (
            <div key={column.id} className="flex-1 min-w-[260px]">
                  <KanbanColumn
                columnId={column.id}
                droppableId={`${column.id}--${board.id}`}
                    title={column.title}
                    tasks={columnTasks}
                    count={columnTasks.length}
                    overdueTasks={overdueTasks}
                    highPriorityTasks={highPriorityTasks}
                    onTaskClick={onTaskClick}
                  />
                </div>
          );
        })}
      </div>
  );
};