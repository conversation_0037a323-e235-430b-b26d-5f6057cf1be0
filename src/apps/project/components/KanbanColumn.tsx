import React from "react";
import { Draggable, Droppable } from "react-beautiful-dnd";
import { <PERSON>, CardHeader, CardBody, Button, Avatar, AvatarGroup } from "@heroui/react";
import { Icon } from "@iconify/react";
import type { Task } from "../types";
import { TaskCard } from "./TaskCard";

interface KanbanColumnProps {
  columnId: string;
  droppableId: string;
  title: string;
  count: number;
  tasks: Task[];
  overdueTasks?: number;
  highPriorityTasks?: number;
  onTaskClick: (task: Task) => void;
}

export const KanbanColumn: React.FC<KanbanColumnProps> = ({ 
  columnId,
  droppableId,
  title, 
  count, 
  tasks, 
  overdueTasks = 0,
  highPriorityTasks = 0,
  onTaskClick 
}) => {
  return (
    <Droppable droppableId={droppableId} type="DEFAULT">
      {(droppableProvided, droppableSnapshot) => (
        <div className={`h-full ${droppableSnapshot.isDraggingOver ? 'bg-custom-card/50 rounded-lg' : ''}`}> 
    <Card className="bg-custom-card border-custom-border shadow-none h-full flex flex-col">
      <CardHeader className="flex flex-col border-b border-custom-border pb-3">
        <div className="flex justify-between items-center w-full mb-2">
          <div className="flex items-center">
            <h3 className="text-white text-base font-medium">{title}</h3>
            <span className="ml-2 px-2 py-0.5 bg-custom-border rounded-full text-xs text-custom-muted">
              {count}
            </span>
          </div>
          <Button isIconOnly variant="light" className="text-custom-muted">
            <Icon icon="lucide:plus" />
          </Button>
        </div>
        
        {/* Column statistics */}
        <div className="flex gap-2 flex-wrap">
          {highPriorityTasks > 0 && (
            <div className="px-2 py-1 rounded-md bg-red-500/10 text-red-500 text-xs flex items-center">
              <Icon icon="lucide:flag" className="text-xs mr-1" />
              {highPriorityTasks} high priority
            </div>
          )}
          
          {overdueTasks > 0 && (
            <div className="px-2 py-1 rounded-md bg-red-500/10 text-red-500 text-xs flex items-center">
              <Icon icon="lucide:alert-circle" className="text-xs mr-1" />
              {overdueTasks} overdue
            </div>
          )}
          
          {tasks.some(task => task.attachments?.length > 0) && (
            <div className="px-2 py-1 rounded-md bg-custom-border/30 text-custom-muted text-xs flex items-center">
              <Icon icon="lucide:paperclip" className="text-xs mr-1" />
              {tasks.reduce((total, task) => total + (task.attachments?.length || 0), 0)}
            </div>
          )}
          
          {tasks.some(task => task.comments?.length > 0) && (
            <div className="px-2 py-1 rounded-md bg-custom-border/30 text-custom-muted text-xs flex items-center">
              <Icon icon="lucide:message-circle" className="text-xs mr-1" />
              {tasks.reduce((total, task) => total + (task.comments?.length || 0), 0)}
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardBody className="p-3 space-y-4 flex-grow overflow-y-auto custom-scrollbar">
              <div
                ref={droppableProvided.innerRef as any}
                {...droppableProvided.droppableProps}
                className="space-y-4"
              >
        {tasks.map((task, index) => (
          <Draggable key={task.id} draggableId={task.id} index={index}>
            {(provided, snapshot) => (
              <div
                ref={provided.innerRef}
                {...provided.draggableProps}
                {...provided.dragHandleProps}
                style={{
                  ...provided.draggableProps.style,
                }}
                className={`${snapshot.isDragging ? 'z-50 shadow-lg' : ''}`}
                onClick={() => {
                  // Only trigger task click if not currently dragging
                  if (!snapshot.isDragging) {
                    onTaskClick(task);
                  }
                }}
              >
                <TaskCard task={task} />
              </div>
            )}
          </Draggable>
        ))}
        
        {/* Empty state */}
        {(!tasks || tasks.length === 0) && (
          <div className="flex flex-col items-center justify-center p-6 my-6 text-custom-muted border-2 border-dashed border-custom-border rounded-lg">
            <Icon icon="lucide:inbox" className="text-2xl mb-2" />
            <p className="text-sm">No tasks</p>
          </div>
        )}
                {droppableProvided.placeholder}
              </div>
      </CardBody>
    </Card>
        </div>
      )}
    </Droppable>
  );
};