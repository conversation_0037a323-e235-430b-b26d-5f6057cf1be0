import React from "react";
    import { <PERSON>, Card<PERSON>ody, CardHeader, Progress } from "@heroui/react";
    import { Icon } from "@iconify/react";

    export const ProjectDashboard: React.FC = () => {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="bg-custom-card border-custom-border shadow-none">
            <CardHeader className="flex justify-between items-center border-b border-custom-border">
              <h3 className="text-white text-base font-medium">Projects Overview</h3>
              <button className="text-custom-muted hover:text-white">
                <Icon icon="lucide:more-horizontal" />
              </button>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm text-custom-text">Active Projects</span>
                    <span className="text-sm text-custom-muted">8/12</span>
                  </div>
                  <Progress 
                    value={66} 
                    color="primary"
                    classNames={{
                      base: "h-2",
                      track: "bg-custom-border",
                      indicator: "bg-custom-primary",
                    }}
                  />
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm text-custom-text">Completed Projects</span>
                    <span className="text-sm text-custom-muted">4/12</span>
                  </div>
                  <Progress 
                    value={33} 
                    color="success"
                    classNames={{
                      base: "h-2",
                      track: "bg-custom-border",
                      indicator: "bg-success",
                    }}
                  />
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm text-custom-text">Overdue Tasks</span>
                    <span className="text-sm text-custom-muted">3/24</span>
                  </div>
                  <Progress 
                    value={12} 
                    color="danger"
                    classNames={{
                      base: "h-2",
                      track: "bg-custom-border",
                      indicator: "bg-danger",
                    }}
                  />
                </div>
              </div>
            </CardBody>
          </Card>
          
          {/* Additional dashboard cards would go here */}
          <Card className="bg-custom-card border-custom-border shadow-none">
            <CardHeader className="flex justify-between items-center border-b border-custom-border">
              <h3 className="text-white text-base font-medium">Recent Activity</h3>
              <button className="text-custom-muted hover:text-white">
                <Icon icon="lucide:more-horizontal" />
              </button>
            </CardHeader>
            <CardBody className="p-0">
              <div className="divide-y divide-custom-border">
                {[1, 2, 3, 4].map((_, index) => (
                  <div key={index} className="p-4 flex items-start gap-3">
                    <div className="bg-primary/20 p-2 rounded-full">
                      <Icon icon="lucide:check-circle" className="text-primary" />
                    </div>
                    <div>
                      <p className="text-sm text-white">Task completed: Homepage Design</p>
                      <p className="text-xs text-custom-muted">2 hours ago</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
          
          <Card className="bg-custom-card border-custom-border shadow-none">
            <CardHeader className="flex justify-between items-center border-b border-custom-border">
              <h3 className="text-white text-base font-medium">Team Workload</h3>
              <button className="text-custom-muted hover:text-white">
                <Icon icon="lucide:more-horizontal" />
              </button>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                {['John Doe', 'Sarah Smith', 'Alex Johnson'].map((name, index) => (
                  <div key={index}>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm text-custom-text">{name}</span>
                      <span className="text-sm text-custom-muted">{5 + index}/10 tasks</span>
                    </div>
                    <Progress 
                      value={(5 + index) * 10} 
                      color={index === 0 ? "primary" : index === 1 ? "warning" : "success"}
                      classNames={{
                        base: "h-2",
                        track: "bg-custom-border",
                      }}
                    />
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </div>
      );
    };