import React from "react";
    import { <PERSON>, CardBody, Button, ButtonGroup } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import type { ProjectDocument } from "../types";

    interface DocumentCardProps {
      document: ProjectDocument;
    }

    export const DocumentCard: React.FC<DocumentCardProps> = ({ document }) => {
      const getDocIcon = () => {
        switch (document.type) {
          case 'pdf': return "lucide:file-text";
          case 'doc': 
          case 'docx': return "lucide:file";
          case 'xls':
          case 'xlsx': return "lucide:file-spreadsheet";
          case 'ppt':
          case 'pptx': return "lucide:file-presentation";
          default: return "lucide:file";
        }
      };
      
      const getDocColor = () => {
        switch (document.type) {
          case 'pdf': return "text-danger";
          case 'doc': 
          case 'docx': return "text-primary";
          case 'xls':
          case 'xlsx': return "text-success";
          case 'ppt':
          case 'pptx': return "text-warning";
          default: return "text-default-500";
        }
      };
      
      return (
        <Card className="bg-content1 shadow-none">
          <CardBody className="p-4">
            <div className="flex flex-col items-center mb-3">
              <div className={`p-3 rounded-xl mb-2 ${getDocColor().replace('text-', 'bg-')}/10`}>
                <Icon icon={getDocIcon()} className={`text-3xl ${getDocColor()}`} />
              </div>
              <h4 className="text-md font-medium text-center">{document.name}</h4>
              <div className="flex items-center gap-1 text-xs text-default-500 mt-1">
                <span>{document.type.toUpperCase()}</span>
                <span>•</span>
                <span>{document.size}</span>
              </div>
            </div>
            <div className="flex justify-center">
              <ButtonGroup size="sm">
                <Button startContent={<Icon icon="lucide:eye" />} variant="flat">View</Button>
                <Button startContent={<Icon icon="lucide:download" />} variant="flat">Download</Button>
              </ButtonGroup>
            </div>
          </CardBody>
        </Card>
      );
    };