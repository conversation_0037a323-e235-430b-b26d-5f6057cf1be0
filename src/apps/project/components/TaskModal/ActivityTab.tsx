import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, CardBody } from "@heroui/react";
import { Icon } from "@iconify/react";
import type { Task, TimeLog } from "../../types";
import { formatDistanceToNow, parseISO } from 'date-fns';
import { useDateFormatter } from "@react-aria/i18n";
import { motion } from "framer-motion";

interface ActivityTabProps {
  task: Task;
  scrollRef?: React.RefObject<HTMLDivElement>;
}

export const ActivityTab: React.FC<ActivityTabProps> = ({ task, scrollRef }) => {
  const activityScrollRef = React.useRef<HTMLDivElement>(null);
  const dateFormatter = useDateFormatter({ dateStyle: "medium", timeStyle: "short" });

  // Function to format date consistently
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(parseISO(dateString), { addSuffix: true });
    } catch (e) {
      return dateFormatter.format(new Date(dateString));
    }
  };

  // Group activities by date for better organization
  const groupedActivities = React.useMemo(() => {
    // This would group activities by date in a real app
    return {
      today: task?.timeLogs?.slice(0, 2) || [],
      yesterday: task?.timeLogs?.slice(2, 4) || [],
      older: task?.timeLogs?.slice(4) || []
    };
  }, [task]);

  const renderActivityItem = (icon: string, title: string, timestamp: string, description: string, user: any, bgColor: string = "bg-custom-primary/20", iconColor: string = "text-custom-primary") => {
    return (
      <div className="mb-6">
        <div className="absolute -left-3 mt-1.5">
          <div className={`rounded-full p-1.5 ${bgColor}`}>
            <Icon icon={icon} className={`${iconColor} text-xs`} />
          </div>
        </div>
        <div className="flex flex-col pl-6">
          <span className="text-sm font-medium text-custom-text">{title}</span>
          <span className="text-xs text-custom-muted">{formatDate(timestamp)}</span>
          <div className="flex items-center gap-2 mt-1">
            <Avatar src={user?.avatar} className="h-5 w-5" />
            <span className="text-sm text-custom-muted">{description}</span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="space-y-8 max-w-3xl mx-auto"
    >
      <div className="bg-custom-card border border-custom-border rounded-lg p-5 mb-6">

        
   
        
        {/* Today's Activities */}
        <div className="mb-6">
          <div className="flex items-center mb-4">
            <div className="flex-1 h-px bg-custom-border"></div>
            <span className="px-4 text-sm text-custom-muted">Today</span>
            <div className="flex-1 h-px bg-custom-border"></div>
          </div>
          
          <div className="relative pl-6 border-l border-custom-border">
            {/* Task Created */}
            {renderActivityItem(
              "lucide:plus-circle",
              "Task Created",
              task?.createdAt || '',
              `${task?.createdBy?.name || "Someone"} created this task`,
              task?.createdBy
            )}
            
            {/* Status Change */}
            {renderActivityItem(
              "lucide:refresh-cw",
              "Status Changed",
              new Date().toISOString(),
              `${task?.assignees?.[0]?.name || "Someone"} changed status from "To Do" to "In Progress"`,
              task?.assignees?.[0],
              "bg-warning/20",
              "text-warning"
            )}
            
            {/* Assignee Added */}
            {renderActivityItem(
              "lucide:user-plus",
              "Assignee Added",
              new Date(Date.now() - 3600000).toISOString(),
              `${task?.createdBy?.name || "Someone"} assigned ${task?.assignees?.[1]?.name || "team member"}`,
              task?.createdBy,
              "bg-success/20",
              "text-success"
            )}
          </div>
        </div>
        
        {/* Yesterday's Activities */}
        <div className="mb-6">
          <div className="flex items-center mb-4">
            <div className="flex-1 h-px bg-custom-border"></div>
            <span className="px-4 text-sm text-custom-muted">Yesterday</span>
            <div className="flex-1 h-px bg-custom-border"></div>
          </div>
          
          <div className="relative pl-6 border-l border-custom-border">
            {/* Description Updated */}
            {renderActivityItem(
              "lucide:edit-3",
              "Description Updated",
              new Date(Date.now() - 86400000).toISOString(),
              `${task?.assignees?.[0]?.name || "Someone"} updated the task description`,
              task?.assignees?.[0],
              "bg-primary/20",
              "text-primary"
            )}
            
            {/* Time Logged */}
            {task?.timeLogs && task.timeLogs[0] && renderActivityItem(
              "lucide:clock",
              "Time Logged",
              task.timeLogs[0].timestamp,
              `${task.timeLogs[0].user.name} logged ${task.timeLogs[0].duration}`,
              task.timeLogs[0].user,
              "bg-primary/20",
              "text-primary"
            )}
          </div>
        </div>
        
        {/* Older Activities */}
        <div>
          <div className="flex items-center mb-4">
            <div className="flex-1 h-px bg-custom-border"></div>
            <span className="px-4 text-sm text-custom-muted">Older</span>
            <div className="flex-1 h-px bg-custom-border"></div>
          </div>
          
          <div className="relative pl-6 border-l border-custom-border">
            {/* Priority Changed */}
            {renderActivityItem(
              "lucide:flag",
              "Priority Changed",
              new Date(Date.now() - 172800000).toISOString(),
              `${task?.createdBy?.name || "Someone"} changed priority from "Medium" to "High"`,
              task?.createdBy,
              "bg-danger/20",
              "text-danger"
            )}
            
            {/* Deadline Set */}
            {renderActivityItem(
              "lucide:calendar",
              "Deadline Set",
              new Date(Date.now() - 259200000).toISOString(),
              `${task?.createdBy?.name || "Someone"} set the deadline to ${formatDate(task?.dueDate || '')}`,
              task?.createdBy,
              "bg-warning/20",
              "text-warning"
            )}
          </div>
        </div>
      </div>
      

    </motion.div>
  );
};

// Helper function to calculate total time (reused from TimeLogsTab)
const calculateTotalTime = (logs: TimeLog[] = []) => {
  return logs.length > 0 ? `${logs.length * 2}h ${logs.length * 15}m` : "0h 0m";
};