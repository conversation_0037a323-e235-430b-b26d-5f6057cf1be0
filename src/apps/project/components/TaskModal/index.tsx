import React from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>b,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Badge,
  Input,
  Avatar,
  useDisclosure
} from "@heroui/react";
import { Icon } from "@iconify/react";
import type { Task, User, Project } from "../../types";
import { DetailsTab } from "./DetailsTab";
import { TimeLogsTab } from "./TimeLogsTab";
import { ChatTab } from "./ChatTab";
import { ActivityTab } from "./ActivityTab";
import { parseDate, parseTime, CalendarDate, Time, getLocalTimeZone } from "@internationalized/date";
import { motion, AnimatePresence } from "framer-motion";

// Update the TaskModalProps interface to be more specific and comprehensive
export interface TaskModalProps {
  task: Task | null;
  isOpen: boolean;
  onClose: () => void;
  onSave?: (updatedTask: Task) => Promise<void> | void;
}

// Create a more specific interface for the form state to manage it better
interface TaskFormState {
  title: string;
  description: string;
  priority: string;
  assignees: User[];
  viewers: User[];
  tags: string[];
  dueDate: CalendarDate | null;
  dueTime: Time | null;
  selectedProject: Project | null;
  progressValues: Record<string, number>;
  estimatedHours: string;
  estimatedMinutes: string;
}

export const TaskModal: React.FC<TaskModalProps> = ({ task, isOpen, onClose, onSave }) => {
  const [activeTab, setActiveTab] = React.useState("details");
  const [isEditMode, setIsEditMode] = React.useState(false);
  const [isSaving, setIsSaving] = React.useState(false);
  const [showValidationErrors, setShowValidationErrors] = React.useState(false);
  
  // Combine all form state into a single object for easier management
  const [formState, setFormState] = React.useState<TaskFormState>({
    title: "",
    description: "",
    priority: "Medium",
    assignees: [],
    viewers: [],
    tags: [],
    dueDate: null,
    dueTime: null,
    selectedProject: null,
    progressValues: {},
    estimatedHours: "0",
    estimatedMinutes: "0"
  });

  // Create individual setters for form fields
  const updateForm = (field: keyof TaskFormState, value: any) => {
    setFormState(prev => ({ ...prev, [field]: value }));
  };
  
  // Track scroll positions with refs instead of state to avoid re-renders
  const scrollRefs = {
    details: React.useRef<HTMLDivElement>(null),
    "time-logs": React.useRef<HTMLDivElement>(null),
    chat: React.useRef<HTMLDivElement>(null),
    activity: React.useRef<HTMLDivElement>(null)
  };

  // Initialize form values when task changes
  React.useEffect(() => {
    if (task) {
      setFormState({
        title: task.title || "",
        description: task.description || "",
        priority: task.priority || "Medium",
        assignees: task.assignees || [],
        viewers: [],
        tags: task.tag ? [task.tag] : [],
        dueDate: task.dueDate ? parseDate(task.dueDate.slice(0,10)) : null,
        dueTime: task.dueDate ? parseTime(new Date(task.dueDate).toTimeString().slice(0,5)) : null,
        selectedProject: task.project || null,
        progressValues: task.assignees?.reduce((acc, assignee) => {
          acc[assignee.id] = 0; // Initialize progress to 0 or get from task if available
          return acc;
        }, {} as Record<string, number>) || {},
        estimatedHours: "0",
        estimatedMinutes: "0"
      });
    }
  }, [task]);

  // Reset edit mode when modal is closed
  React.useEffect(() => {
    if (!isOpen) {
      setIsEditMode(false);
      setShowValidationErrors(false);
    }
  }, [isOpen]);
  
  // Improved tab change handler with proper typing
  const handleTabChange = (key: React.Key) => {
    setActiveTab(key as string);
  };
  
  if (!task) return null;
  
  // Enhanced form validation
  const validateForm = (): boolean => {
    if (!formState.title.trim()) {
      setShowValidationErrors(true);
      return false;
    }
    
    if (formState.assignees.length === 0) {
      setShowValidationErrors(true);
      return false;
    }
    
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;
    
    setIsSaving(true);
    
    try {
      // Prepare updated task data
      const updatedTask = {
        ...task,
        title: formState.title,
        description: formState.description,
        priority: formState.priority,
        assignees: formState.assignees,
        tag: formState.tags[0] || "",
        dueDate: formState.dueDate && formState.dueTime
          ? new Date(formState.dueDate.toDate(getLocalTimeZone()).setHours(formState.dueTime.hour, formState.dueTime.minute)).toISOString()
          : null,
        project: formState.selectedProject || task.project,
        updatedAt: new Date().toISOString()
      };
      
      // Call the onSave handler
      if (onSave) {
        await onSave(updatedTask);
      }
      
      setIsEditMode(false);
    } catch (error) {
      console.error("Failed to save task:", error);
      // Show error notification here
    } finally {
      setIsSaving(false);
    }
  };
  
  const handleCancel = () => {
    // Reset form state to original task data
    if (task) {
      setFormState({
        title: task.title || "",
        description: task.description || "",
        priority: task.priority || "Medium",
        assignees: task.assignees || [],
        viewers: [],
        tags: task.tag ? [task.tag] : [],
        dueDate: task.dueDate ? parseDate(task.dueDate.slice(0,10)) : null,
        dueTime: task.dueDate ? parseTime(new Date(task.dueDate).toTimeString().slice(0,5)) : null,
        selectedProject: task.project || null,
        progressValues: {},
        estimatedHours: "0",
        estimatedMinutes: "0"
      });
    }
    
    setIsEditMode(false);
    setShowValidationErrors(false);
  };

  // Progress related functions
  const handleProgressChange = (userId: string, value: number) => {
    updateForm('progressValues', {
      ...formState.progressValues,
      [userId]: value
    });
  };
  
  const calculateAverageProgress = (): number => {
    const values = Object.values(formState.progressValues);
    if (values.length === 0) return 0;
    return Math.round(values.reduce((sum, value) => sum + value, 0) / values.length);
  };

  // Tag management
  const handleAddTag = (tag: string) => {
    if (tag && !formState.tags.includes(tag)) {
      updateForm('tags', [...formState.tags, tag]);
    }
  };
  
  const handleRemoveTag = (tagToRemove: string) => {
    updateForm('tags', formState.tags.filter(tag => tag !== tagToRemove));
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      hideCloseButton
      size="full"
      scrollBehavior="inside"
      classNames={{
        base: "bg-custom-card dark:bg-custom-card", 
        body: "p-0",
        backdrop: "bg-background/70 backdrop-blur-sm",
        content: "overflow-auto border border-custom-border"
      }}
      motionProps={{
        variants: {
          enter: {
            opacity: 1,
            scale: 1,
            y: 0,
            transition: { duration: 0.3, ease: [0.16, 1, 0.3, 1] }
          },
          exit: {
            opacity: 0,
            scale: 0.98,
            y: 20,
            transition: { duration: 0.2, ease: [0.36, 0, 0.66, -0.56] }
          }
        }
      }}
    >
      <ModalContent>
        {(onModalClose) => (
          <>
            <ModalHeader className="flex justify-between items-center pt-4 px-6 border-b border-custom-border bg-custom-sidebar">
              <AnimatePresence mode="wait">
                <motion.div 
                  key={isEditMode ? "edit" : "view"}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  transition={{ duration: 0.2 }}
                  className="flex items-center"
                >
                  <div 
                    className="w-10 h-10 rounded-md flex items-center justify-center"
                    style={{ backgroundColor: task?.project?.color || "#1B84FF" }}
                  >
                    <Icon icon="lucide:layout-list" className="text-white text-xl" />
                  </div>
                  
                  <div className="ml-3">
                    <h3 className="text-xl font-semibold text-white">
                      {isEditMode ? "Edit Task" : task?.title}
                    </h3>
                    {!isEditMode && (
                      <div className="flex items-center gap-2 text-custom-muted text-sm">
                        <span>{task?.project?.name}</span>
                        <span>•</span>
                        <span>Task #{task?.id?.slice(-4)}</span>
                        <span>•</span>
                        <span className="flex items-center gap-1">
                          <Icon icon="lucide:clock" size={14} />
                          {task?.createdAt ? formatDate(task.createdAt) : "Just now"}
                        </span>
                      </div>
                    )}
                  </div>
                </motion.div>
              </AnimatePresence>
              
              <div className="flex items-center gap-2">
                {isEditMode ? (
                  <>
                    <Button 
                      variant="flat" 
                      onPress={handleCancel}
                      size="sm"
                      className="bg-custom-sidebar border border-custom-border text-white"
                      startContent={<Icon icon="lucide:x" />}
                    >
                      Cancel
                    </Button>
                    <Button 
                      color="primary" 
                      onPress={handleSave}
                      size="sm"
                      isLoading={isSaving}
                      startContent={<Icon icon="lucide:save" />}
                    >
                      Save Changes
                    </Button>
                  </>
                ) : (
                  <>
                    <Button 
                      variant="flat" 
                      onPress={() => setIsEditMode(true)}
                      startContent={<Icon icon="lucide:edit-3" />}
                      size="sm"
                      className="bg-custom-sidebar border border-custom-border text-white"
                    >
                      Edit
                    </Button>
                    <Button 
                      variant="flat" 
                      color="danger"
                      startContent={<Icon icon="lucide:trash-2" />}
                      size="sm"
                      className="bg-custom-sidebar border border-custom-border"
                    >
                      Delete
                    </Button>
                    <Button 
                      variant="flat"
                      onPress={onModalClose}
                      size="sm"
                      className="bg-custom-sidebar border border-custom-border text-white"
                      startContent={<Icon icon="lucide:x" />}
                    >
                      Close
                    </Button>
                  </>
                )}
              </div>
            </ModalHeader>

            
            <ModalBody className="p-0">
              <Tabs 
                aria-label="Task Details" 
                selectedKey={activeTab}
                onSelectionChange={handleTabChange}
                className="w-full"
                variant="underlined"
                classNames={{
                  tabList: "px-6 pt-2 bg-custom-card border-b border-custom-border",
                  cursor: "bg-custom-primary",
                  tab: "text-custom-text data-[selected=true]:text-custom-primary",
                  panel: "p-6 pt-4"
                }}
              >
                <Tab 
                  key="details"
                  className="px-10 pt-2"
                  title={
                    <div className="flex items-center gap-1">
                      <Icon icon="lucide:file-text" />
                      <span>Details</span>
                    </div>
                  }
                >
                  <DetailsTab 
                    task={task}
                    isEditMode={isEditMode}
                    showValidationErrors={showValidationErrors}
                    title={formState.title}
                    setTitle={(value) => updateForm('title', value)}
                    description={formState.description}
                    setDescription={(value) => updateForm('description', value)}
                    priority={formState.priority}
                    setPriority={(value) => updateForm('priority', value)}
                    assignees={formState.assignees}
                    setAssignees={(value) => updateForm('assignees', value)}
                    viewers={formState.viewers}
                    setViewers={(value) => updateForm('viewers', value)}
                    tags={formState.tags}
                    handleAddTag={handleAddTag}
                    handleRemoveTag={handleRemoveTag}
                    dueDate={formState.dueDate}
                    setDueDate={(value) => updateForm('dueDate', value)}
                    dueTime={formState.dueTime}
                    setDueTime={(value) => updateForm('dueTime', value)}
                    selectedProject={formState.selectedProject}
                    setSelectedProject={(value) => updateForm('selectedProject', value)}
                    progressValues={formState.progressValues}
                    handleProgressChange={handleProgressChange}
                    calculateAverageProgress={calculateAverageProgress}
                    estimatedHours={formState.estimatedHours}
                    setEstimatedHours={(value) => updateForm('estimatedHours', value)}
                    estimatedMinutes={formState.estimatedMinutes}
                    setEstimatedMinutes={(value) => updateForm('estimatedMinutes', value)}
                    scrollRef={scrollRefs.details}
                  />
                </Tab>
                <Tab 
                  key="time-logs" 
                  title={
                    <div className="flex items-center gap-1">
                      <Icon icon="lucide:clock" />
                      <span>Time Logs</span>
                    </div>
                  }
                >
                  <TimeLogsTab 
                    task={task} 
                    estimatedHours={formState.estimatedHours} 
                    estimatedMinutes={formState.estimatedMinutes} 
                    scrollRef={scrollRefs["time-logs"]} 
                  />
                </Tab>
                <Tab 
                  key="chat" 
                  title={
                    <div className="flex items-center gap-1">
                      <Icon icon="lucide:message-circle" />
                      <span>Chat</span>
                      {task?.chatMessages && task.chatMessages.length > 0 && (
                        <Badge color="primary" content={task.chatMessages.length} />
                      )}
                    </div>
                  }
                >
                  <ChatTab task={task} scrollRef={scrollRefs.chat} />
                </Tab>
                
                <Tab 
                  key="activity" 
                  title={
                    <div className="flex items-center gap-1">
                      <Icon icon="lucide:activity" />
                      <span>Activity</span>
                    </div>
                  }
                >
                  <ActivityTab task={task} scrollRef={scrollRefs.activity} />
                </Tab>
              </Tabs>
            </ModalBody>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};