import React from "react";
import { 
  Input, 
  But<PERSON>, 
  Avatar
} from "@heroui/react";
import { Icon } from "@iconify/react";
import type { Task, ChatMessage } from "../../types";
import { formatDistanceToNow, parseISO } from 'date-fns';
import { useDateFormatter } from "@react-aria/i18n";
import { motion } from "framer-motion";

interface ChatTabProps {
  task: Task;
  scrollRef?: React.RefObject<HTMLDivElement>;
}

export const ChatTab: React.FC<ChatTabProps> = ({ task, scrollRef }) => {
  const [chatMessage, setChatMessage] = React.useState("");
  const [replyToMessage, setReplyToMessage] = React.useState<ChatMessage | null>(null);
  const [isTyping, setIsTyping] = React.useState(false);
  const chatScrollRef = React.useRef<HTMLDivElement>(null);
  const chatEndRef = React.useRef<HTMLDivElement>(null);
  const dateFormatter = useDateFormatter({ dateStyle: "medium", timeStyle: "short" });

  // Function to format date consistently
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(parseISO(dateString), { addSuffix: true });
    } catch (e) {
      return dateFormatter.format(new Date(dateString));
    }
  };

  // Fix the chat scroll behavior
  React.useEffect(() => {
    if (chatEndRef.current) {
      chatEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [task.chatMessages]);

  const handleSendMessage = () => {
    if (!chatMessage.trim()) return;
    
    // In a real app, you'd send this to an API
    setChatMessage("");
    setReplyToMessage(null);
  };

  return (
    <div className="flex flex-col h-[calc(70vh-180px)]">
      <div className="flex-1 overflow-y-auto p-2 space-y-4" ref={chatScrollRef}>
        {(task.chatMessages?.length || 0) === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-custom-muted">
            <div className="p-4 rounded-full bg-custom-primary/10 mb-4">
              <Icon icon="lucide:message-circle" className="text-4xl text-custom-primary" />
            </div>
            <p className="text-custom-text font-medium">Start the conversation for this task</p>
            <p className="text-sm text-custom-muted mt-1">Messages will sync with the Chat App</p>
          </div>
        ) : (
          <>
            {/* Date divider */}
            <div className="flex items-center my-4">
              <div className="flex-1 h-px bg-custom-border"></div>
              <span className="px-3 text-xs text-custom-muted">Today</span>
              <div className="flex-1 h-px bg-custom-border"></div>
            </div>
            
            {task.chatMessages?.map((message, index) => {
              const isFirstInGroup = index === 0 || message.user.id !== task.chatMessages[index - 1].user.id;
              const isLastInGroup = index === task.chatMessages.length - 1 || message.user.id !== task.chatMessages[index + 1].user.id;
              
              return (
                <motion.div 
                  key={message.id} 
                  initial={{ opacity: 0, y: 10 }} 
                  animate={{ opacity: 1, y: 0 }} 
                  transition={{ delay: index * 0.05, duration: 0.2 }}
                  className={`flex gap-2 ${!isFirstInGroup ? 'pl-10 mt-1' : 'mt-4'}`}
                >
                  {isFirstInGroup && (
                    <Avatar src={message.user.avatar} size="sm" className="mt-1" />
                  )}
                  <div className="flex-1">
                    {isFirstInGroup && (
                      <div className="flex items-baseline gap-2">
                        <span className="font-medium text-sm text-custom-text">{message.user.name}</span>
                        <span className="text-xs text-custom-muted">
                          {formatDate(message.timestamp)}
                        </span>
                      </div>
                    )}
                    
                    <div className={`rounded-xl ${isFirstInGroup ? 'rounded-tl-sm' : ''} ${isLastInGroup ? 'rounded-bl-sm' : ''} bg-custom-sidebar border border-custom-border py-2 px-3 mt-1 inline-block max-w-[85%]`}>
                      <div className="text-sm text-custom-text">{message.content}</div>
                    </div>
                    
                    <div className="opacity-0 hover:opacity-100 transition-opacity flex gap-1 mt-1">
                      <Button 
                        size="sm" 
                        variant="light" 
                        className="h-6 px-1 min-w-0 text-custom-muted"
                        onPress={() => setReplyToMessage(message)}
                      >
                        Reply
                      </Button>
                      <Button 
                        size="sm" 
                        variant="light" 
                        className="h-6 px-1 min-w-0 text-custom-muted"
                      >
                        <Icon icon="lucide:smile" size={14} />
                      </Button>
                      <Button 
                        size="sm" 
                        variant="light" 
                        className="h-6 px-1 min-w-0 text-custom-muted"
                      >
                        <Icon icon="lucide:more-horizontal" size={14} />
                      </Button>
                    </div>
                  </div>
                </motion.div>
              );
            })}
            <div ref={chatEndRef} />
          </>
        )}
      </div>
      
      <div className="border-t border-custom-border pt-3 mt-2">
        {/* User is typing indicator */}
        {isTyping && (
          <div className="px-3 py-2 mb-2 flex items-center gap-2 text-custom-muted">
            <div className="flex space-x-1">
              <div className="w-2 h-2 rounded-full bg-custom-primary animate-bounce" style={{ animationDelay: "0ms" }}></div>
              <div className="w-2 h-2 rounded-full bg-custom-primary animate-bounce" style={{ animationDelay: "150ms" }}></div>
              <div className="w-2 h-2 rounded-full bg-custom-primary animate-bounce" style={{ animationDelay: "300ms" }}></div>
            </div>
            <span className="text-xs"><em>Someone is typing...</em></span>
          </div>
        )}
        
        {/* Reply to message UI */}
        {replyToMessage && (
          <div className="mb-2 px-3 py-2 bg-custom-sidebar rounded-md flex justify-between items-start border-l-2 border-custom-primary">
            <div className="flex-1">
              <div className="text-xs text-custom-primary font-medium flex items-center gap-1">
                <Icon icon="lucide:corner-up-left" size={12} />
                Replying to {replyToMessage.user.name}
              </div>
              <div className="text-sm text-custom-muted truncate">
                {replyToMessage.content}
              </div>
            </div>
            <Button 
              isIconOnly 
              size="sm" 
              variant="light" 
              onPress={() => setReplyToMessage(null)}
            >
              <Icon icon="lucide:x" className="text-custom-muted" size={14} />
            </Button>
          </div>
        )}
        
        <div className="flex gap-2">
          <Input
            placeholder="Type a message..."
            value={chatMessage}
            onValueChange={setChatMessage}
            variant="bordered"
            fullWidth
            classNames={{
              inputWrapper: "bg-custom-sidebar border-custom-border text-custom-text"
            }}
            startContent={
              <Button 
                isIconOnly 
                size="sm" 
                variant="light"
                className="text-custom-muted"
              >
                <Icon icon="lucide:at-sign" />
              </Button>
            }
            endContent={
              <div className="flex items-center gap-1">
                <Button 
                  isIconOnly 
                  size="sm" 
                  variant="light"
                  className="text-custom-muted"
                >
                  <Icon icon="lucide:smile" />
                </Button>
                <Button 
                  isIconOnly 
                  size="sm" 
                  variant="light"
                  className="text-custom-muted"
                >
                  <Icon icon="lucide:paperclip" />
                </Button>
              </div>
            }
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
          />
          <Button 
            color="primary" 
            isIconOnly
            onPress={handleSendMessage}
            isDisabled={!chatMessage.trim()}
          >
            <Icon icon="lucide:send" />
          </Button>
        </div>
      </div>
    </div>
  );
};