import React from "react";
import { 
  <PERSON><PERSON>, 
  Select, 
  SelectItem, 
  Switch, 
  Card, 
  CardBody, 
  Avatar, 
  Badge, 
  <PERSON>, 
  Divider, 
  Toolt<PERSON>
} from "@heroui/react";
import { Icon } from "@iconify/react";
import type { Task, TimeLog } from "../../types";
import { formatDistanceToNow, parseISO } from 'date-fns';
import { useDateFormatter } from "@react-aria/i18n";
import { motion } from "framer-motion";

interface TimeLogsTabProps {
  task: Task;
  estimatedHours?: string;
  estimatedMinutes?: string;
  scrollRef?: React.RefObject<HTMLDivElement>;
}

export const TimeLogsTab: React.FC<TimeLogsTabProps> = ({ task, estimatedHours = "0", estimatedMinutes = "0", scrollRef }) => {
  const [sortTimeLogsBy, setSortTimeLogsBy] = React.useState<"date" | "user">("date");
  const [groupTimeLogsByUser, setGroupTimeLogsByUser] = React.useState(false);
  const timeLogsScrollRef = React.useRef<HTMLDivElement>(null);
  const dateFormatter = useDateFormatter({ dateStyle: "medium", timeStyle: "short" });

  // Function to format date consistently
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(parseISO(dateString), { addSuffix: true });
    } catch (e) {
      return dateFormatter.format(new Date(dateString));
    }
  };
  
  // Filter and sort time logs
  const getFilteredTimeLogs = () => {
    let filtered = [...(task.timeLogs || [])];
    
    if (sortTimeLogsBy === "date") {
      filtered.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );
    } else {
      filtered.sort((a, b) => a.user.name.localeCompare(b.user.name));
    }
    
    return filtered;
  };
  
  // Group time logs by user
  const getGroupedTimeLogs = () => {
    const logs = getFilteredTimeLogs();
    const grouped: Record<string, TimeLog[]> = {};
    
    logs.forEach(log => {
      if (!grouped[log.user.id]) {
        grouped[log.user.id] = [];
      }
      grouped[log.user.id].push(log);
    });
    
    return grouped;
  };

  // Calculate total time
  const calculateTotalTime = (logs: TimeLog[] = []) => {
    // In a real app, this would calculate the actual total time
    return logs.length > 0 ? `${logs.length * 2}h ${logs.length * 15}m` : "0h 0m";
  };

  return (
    <div className="space-y-6">
      <div className="bg-custom-card border border-custom-border rounded-lg p-5 mb-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-3">
            <div className="p-3 rounded-full bg-custom-primary/20">
              <Icon icon="lucide:clock" className="text-custom-primary text-xl" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-white">Time Tracking</h3>
              <p className="text-sm text-custom-muted">Manage time spent on this task</p>
            </div>
          </div>
          

        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-6">
          <div className="bg-custom-sidebar p-4 rounded-lg border border-custom-border">
            <div className="text-xs text-custom-muted mb-1">Total Time</div>
            <div className="text-xl font-semibold text-custom-text">
              {calculateTotalTime(task.timeLogs)}
            </div>
          </div>
          
          <div className="bg-custom-sidebar p-4 rounded-lg border border-custom-border">
            <div className="text-xs text-custom-muted mb-1">Estimated</div>
            <div className="text-xl font-semibold text-custom-text">
              {estimatedHours > "0" ? `${estimatedHours}h ` : ''}
              {estimatedMinutes > "0" ? `${estimatedMinutes}m` : ''}
              {estimatedHours === "0" && estimatedMinutes === "0" && "Not set"}
            </div>
          </div>
          
          <div className="bg-custom-sidebar p-4 rounded-lg border border-custom-border">
            <div className="text-xs text-custom-muted mb-1">Remaining</div>
            <div className="text-xl font-semibold text-custom-text">
              {/* Calculate remaining time based on total and estimated */}
              {(estimatedHours > "0" || estimatedMinutes > "0") ? "3h 45m" : "N/A"}
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-custom-text">Sort by:</span>
            <Select 
              selectedKeys={[sortTimeLogsBy]}
              onChange={(e) => setSortTimeLogsBy(e.target.value as "date" | "user")}
              size="sm"
              className="w-32"
              variant="bordered"
              classNames={{
                trigger: "bg-custom-sidebar border-custom-border text-custom-text",
                listboxWrapper: "bg-custom-card",
                popoverContent: "bg-custom-card border-custom-border"
              }}
            >
              <SelectItem key="date" className="text-custom-text">Date</SelectItem>
              <SelectItem key="user" className="text-custom-text">User</SelectItem>
            </Select>
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm text-custom-text">Group by user:</span>
            <Switch 
              isSelected={groupTimeLogsByUser}
              onValueChange={setGroupTimeLogsByUser}
              size="sm"
              color="primary"
            />
          </div>
        </div>
        
        <div>
          <Button 
            variant="flat" 
            className="bg-custom-sidebar border border-custom-border text-custom-text"
            startContent={<Icon icon="lucide:download" />}
            size="sm"
          >
            Export
          </Button>
        </div>
      </div>
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        {groupTimeLogsByUser ? (
          // Grouped by user view
          <div className="space-y-6">
            {Object.entries(getGroupedTimeLogs()).map(([userId, logs], index) => {
              const user = logs[0].user;
              const totalTime = calculateTotalTime(logs); 
                
              return (
                <motion.div
                  key={userId}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05, duration: 0.3 }}
                >
                  <Card className="border border-custom-border bg-custom-card shadow-none">
                    <CardBody className="p-0">
                      <div className="p-4 flex items-center justify-between border-b border-custom-border">
                        <div className="flex items-center gap-3">
                          <Avatar src={user.avatar} className="h-10 w-10" />
                          <div>
                            <div className="font-medium text-custom-text">{user.name}</div>
                            <div className="text-xs text-custom-muted">{user.role}</div>
                          </div>
                        </div>
                        <Badge content={logs.length} color="primary">
                          <Chip color="primary" variant="flat">{totalTime}</Chip>
                        </Badge>
                      </div>
                      
                      <div className="divide-y divide-custom-border">
                        {logs.map(log => (
                          <div key={log.id} className="flex items-center justify-between p-3 hover:bg-custom-sidebar transition-colors">
                            <div className="flex items-center gap-3">
                              <div className="p-2 rounded-full bg-custom-primary/20">
                                <Icon icon="lucide:clock" className="text-custom-primary text-base" />
                              </div>
                              <div>
                                <div className="text-sm text-custom-text font-medium">{log.duration}</div>
                                <div className="text-xs text-custom-muted">{formatDate(log.timestamp)}</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {log.notes && (
                                <Tooltip 
                                  content={log.notes}
                                  className="bg-custom-card border border-custom-border text-custom-text"
                                >
                                  <Button isIconOnly size="sm" variant="light" className="text-custom-muted">
                                    <Icon icon="lucide:message-circle" className="text-xs" />
                                  </Button>
                                </Tooltip>
                              )}
                              <Button isIconOnly size="sm" variant="light" className="text-custom-muted">
                                <Icon icon="lucide:more-vertical" className="text-xs" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardBody>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        ) : (
          // List view
          <Card className="border border-custom-border bg-custom-card shadow-none">
            <CardBody className="p-0">
              <table className="w-full">
                <thead className="bg-custom-sidebar">
                  <tr className="border-b border-custom-border">
                    <th className="text-left py-3 px-4 font-medium text-sm text-custom-muted">User</th>
                    <th className="text-left py-3 px-4 font-medium text-sm text-custom-muted">Date & Time</th>
                    <th className="text-left py-3 px-4 font-medium text-sm text-custom-muted">Duration</th>
                    <th className="text-left py-3 px-4 font-medium text-sm text-custom-muted">Notes</th>
                    <th className="py-3 px-4 font-medium text-sm text-right text-custom-muted">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-custom-border">
                  {getFilteredTimeLogs().map((log, index) => (
                    <motion.tr 
                      key={log.id} 
                      className="hover:bg-custom-sidebar transition-colors"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.03, duration: 0.2 }}
                    >
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">
                          <Avatar src={log.user.avatar} size="sm" />
                          <div>
                            <div className="text-sm font-medium text-custom-text">{log.user.name}</div>
                            <div className="text-xs text-custom-muted">{log.user.role}</div>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-sm text-custom-text">{formatDate(log.timestamp)}</td>
                      <td className="py-3 px-4">
                        <span className="text-sm font-medium text-custom-text px-2 py-1 rounded-md bg-custom-primary/10">
                          {log.duration}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-sm text-custom-muted max-w-[200px] truncate">
                        {log.notes || "-"}
                      </td>
                      <td className="py-3 px-4 text-right">
                        <div className="flex justify-end gap-1">
                          <Button isIconOnly size="sm" variant="light" className="text-custom-muted">
                            <Icon icon="lucide:edit-2" className="text-base" />
                          </Button>
                          <Button isIconOnly size="sm" variant="light" className="text-custom-muted">
                            <Icon icon="lucide:trash-2" className="text-base" />
                          </Button>
                          <Button isIconOnly size="sm" variant="light" className="text-custom-muted">
                            <Icon icon="lucide:more-vertical" className="text-xs" />
                          </Button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                  
                  {/* Empty state */}
                  {getFilteredTimeLogs().length === 0 && (
                    <tr>
                      <td colSpan={5} className="py-10 text-center text-custom-muted">
                        <Icon icon="lucide:clock" className="text-3xl mx-auto mb-2" />
                        <p>No time logs recorded yet</p>
                        <Button 
                          size="sm" 
                          color="primary" 
                          variant="flat" 
                          className="mt-2"
                          startContent={<Icon icon="lucide:plus" />}
                        >
                          Add Time Log
                        </Button>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </CardBody>
          </Card>
        )}
      </motion.div>
      

    </div>
  );
};