import React from "react";
import {
  Input,
  Textarea,
  Select,
  SelectItem,
  Chip,
  Card,
  CardBody,
  Avatar,
  Progress,
  Divider,
  Slider,
  DatePicker,
  TimeInput,
  Button,
  Tooltip,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  DropdownSection
} from "@heroui/react";
import { Icon } from "@iconify/react";
import type { Task, User, Project } from "../../types";
import { useDateFormatter } from "@react-aria/i18n";
import { formatDistanceToNow, parseISO } from 'date-fns';
import { CalendarDate, Time } from "@internationalized/date";
import { mockProjects, mockUsers } from "../../data/mock-data";
import { motion, AnimatePresence } from "framer-motion";

interface DetailsTabProps {
  task: Task;
  isEditMode: boolean;
  showValidationErrors: boolean;
  title: string;
  setTitle: (title: string) => void;
  description: string;
  setDescription: (desc: string) => void;
  priority: string;
  setPriority: (priority: string) => void;
  assignees: User[];
  setAssignees: (users: User[]) => void;
  viewers: User[];
  setViewers: (users: User[]) => void;
  tags: string[];
  handleAddTag: (tag: string) => void;
  handleRemoveTag: (tag: string) => void;
  dueDate: CalendarDate | null;
  setDueDate: (date: CalendarDate | null) => void;
  dueTime: Time | null;
  setDueTime: (time: Time | null) => void;
  selectedProject: Project | null;
  setSelectedProject: (project: Project | null) => void;
  progressValues: Record<string, number>;
  handleProgressChange: (userId: string, value: number) => void;
  calculateAverageProgress: () => number;
  estimatedHours: string;
  setEstimatedHours: (hours: string) => void;
  estimatedMinutes: string;
  setEstimatedMinutes: (minutes: string) => void;
  scrollRef?: React.RefObject<HTMLDivElement>;
}

export const DetailsTab: React.FC<DetailsTabProps> = ({
  task,
  isEditMode,
  showValidationErrors,
  title,
  setTitle,
  description,
  setDescription,
  priority,
  setPriority,
  assignees,
  setAssignees,
  viewers,
  setViewers,
  tags,
  handleAddTag,
  handleRemoveTag,
  dueDate,
  setDueDate,
  dueTime,
  setDueTime,
  selectedProject,
  setSelectedProject,
  progressValues,
  handleProgressChange,
  calculateAverageProgress,
  estimatedHours,
  setEstimatedHours,
  estimatedMinutes,
  setEstimatedMinutes,
  scrollRef
}) => {
  const titleInputRef = React.useRef<HTMLInputElement>(null);
  const dateFormatter = useDateFormatter({ dateStyle: "medium", timeStyle: "short" });

  // Focus on title input when in edit mode and title is empty
  React.useEffect(() => {
    if (isEditMode && !title && titleInputRef.current) {
      titleInputRef.current.focus();
    }
  }, [isEditMode, title]);

  // Function to format date consistently
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(parseISO(dateString), { addSuffix: true });
    } catch (e) {
      return dateFormatter.format(new Date(dateString));
    }
  };

  // Editable field component
  const renderEditableField = (label: string, content: React.ReactNode, className?: string) => {
    return (
      <div className={`space-y-1 ${className || ""}`}>
        <label className="text-sm font-medium">{label}</label>
        {content}
      </div>
    );
  };

  // Get tag color helper
  const getTagColor = (tag: string) => {
    switch (tag) {
      case "Bug": return "text-danger bg-danger-100";
      case "Feature": return "text-success bg-success-100";
      case "Documentation": return "text-warning bg-warning-100";
      default: return "text-primary bg-primary-100";
    }
  };

  return (
    <div className="space-y-6 mx-auto w-full">
      <AnimatePresence mode="wait">
        {isEditMode ? (
          <motion.div 
            key="edit-mode"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-5"
          >
            {/* Title & Description */}
            <div className="md:col-span-2">
              {renderEditableField("Title", 
                <Input
                  ref={titleInputRef}
                  value={title}
                  onValueChange={setTitle}
                  placeholder="Task title"
                  variant="bordered"
                  isInvalid={showValidationErrors && !title.trim()}
                  errorMessage={showValidationErrors && !title.trim() ? "Title is required" : undefined}
                  isRequired
                  className="w-full"
                  classNames={{
                    inputWrapper: "bg-custom-sidebar border-custom-border"
                  }}
                />
              )}
            </div>
            
            <div className="md:col-span-2">
              {renderEditableField("Description", 
                <Textarea
                  value={description}
                  onValueChange={setDescription}
                  placeholder="Add a detailed description of the task..."
                  variant="bordered"
                  minRows={5}
                  className="w-full"
                  classNames={{
                    inputWrapper: "bg-custom-sidebar border-custom-border"
                  }}
                />
              )}
            </div>

            {/* Left Column */}
            <div>
              {/* Project Selector */}
              {renderEditableField("Project", 
                <Select 
                  selectedKeys={selectedProject ? [selectedProject.id] : []}
                  onChange={(e) => {
                    const project = mockProjects.find(p => p.id === e.target.value);
                    if (project) setSelectedProject(project);
                  }}
                  className="w-full"
                  variant="bordered"
                  classNames={{
                    trigger: "bg-custom-sidebar border-custom-border",
                    listboxWrapper: "bg-custom-card",
                    popoverContent: "bg-custom-card border-custom-border"
                  }}
                >
                  {mockProjects.map(project => (
                    <SelectItem 
                      key={project.id} 
                      startContent={
                        <span 
                          className="h-3 w-3 rounded-full" 
                          style={{ backgroundColor: project.color }}
                        />
                      }
                      className="text-custom-text"
                    >
                      {project.name}
                    </SelectItem>
                  ))}
                </Select>
              )}
              
              {/* Status */}
              {renderEditableField("Status", 
                <Select 
                  selectedKeys={[task?.status || ""]}
                  onChange={(e) => {
                    // Handle status change
                  }}
                  className="w-full"
                  variant="bordered"
                  classNames={{
                    trigger: "bg-custom-sidebar border-custom-border",
                    listboxWrapper: "bg-custom-card",
                    popoverContent: "bg-custom-card border-custom-border"
                  }}
                >
                    <SelectItem key="todo" 
                    startContent={<span className="h-3 w-3 rounded-full bg-slate-400" />}
                    className="text-custom-text"
                    >
                    To Do
                  </SelectItem>
                    <SelectItem key="inprogress" 
                    startContent={<span className="h-3 w-3 rounded-full bg-custom-primary" />}
                    className="text-custom-text"
                    >
                    In Progress
                  </SelectItem>
                    <SelectItem key="review" 
                    startContent={<span className="h-3 w-3 rounded-full bg-yellow-500" />}
                    className="text-custom-text"
                    >
                    Review
                  </SelectItem>
                    <SelectItem key="done" 
                    startContent={<span className="h-3 w-3 rounded-full bg-green-500" />}
                    className="text-custom-text"
                    >
                    Done
                  </SelectItem>
                </Select>
              )}
              
              {/* Due Date with Time Picker */}
              {renderEditableField("Due Date & Time",
                <div className="flex gap-3">
                  <div className="flex-1">
                    <DatePicker
                      value={dueDate}
                      onChange={setDueDate}
                      variant="bordered"
                      className="w-full"
                      classNames={{
                        trigger: "bg-custom-sidebar border-custom-border",
                        popoverContent: "bg-custom-card border-custom-border"
                      }}
                    />
                  </div>
                  <div className="w-[120px]">
                    <TimeInput
                        value={dueTime ?? undefined}
                      onChange={setDueTime}
                      variant="bordered"
                      classNames={{
                        inputWrapper: "bg-custom-sidebar border-custom-border"
                      }}
                    />
                  </div>
                </div>
              )}
              
              {/* Tag Management */}
              {renderEditableField("Tags", 
                <div className="flex flex-wrap gap-2">
                  {tags.map(tag => (
                    <Chip 
                      key={tag}
                      variant="flat"
                      className={getTagColor(tag)}
                      onClose={() => handleRemoveTag(tag)}
                    >
                      {tag}
                    </Chip>
                  ))}
                  <Select
                    placeholder="Add tag"
                    onChange={(e) => handleAddTag(e.target.value)}
                    className="w-32"
                    variant="bordered"
                    classNames={{
                      trigger: "bg-custom-sidebar border-custom-border",
                      listboxWrapper: "bg-custom-card",
                      popoverContent: "bg-custom-card border-custom-border"
                    }}
                  >
                      <SelectItem key="Bug" className="text-custom-text">Bug</SelectItem>
                      <SelectItem key="Feature" className="text-custom-text">Feature</SelectItem>
                      <SelectItem key="Design" className="text-custom-text">Design</SelectItem>
                      <SelectItem key="Documentation" className="text-custom-text">Documentation</SelectItem>
                      <SelectItem key="Development" className="text-custom-text">Development</SelectItem>
                  </Select>
                </div>
              )}
            </div>
            
            {/* Right Column */}
            <div>
              {/* Priority Selector */}
              {renderEditableField("Priority", 
                <Select 
                  selectedKeys={[priority]} 
                  onChange={(e) => setPriority(e.target.value)} 
                  className="w-full"
                  variant="bordered"
                  classNames={{
                    trigger: "bg-custom-sidebar border-custom-border",
                    listboxWrapper: "bg-custom-card",
                    popoverContent: "bg-custom-card border-custom-border"
                  }}
                >
                  <SelectItem 
                    key="Low" 
                    startContent={<Icon icon="lucide:flag" className="text-green-500" />}
                    className="text-custom-text"
                  >
                    Low
                  </SelectItem>
                  <SelectItem 
                    key="Medium" 
                    startContent={<Icon icon="lucide:flag" className="text-yellow-500" />}
                    className="text-custom-text"
                  >
                    Medium
                  </SelectItem>
                  <SelectItem 
                    key="High" 
                    startContent={<Icon icon="lucide:flag" className="text-red-500" />}
                    className="text-custom-text"
                  >
                    High
                  </SelectItem>
                </Select>
              )}

              {/* Estimated Time field */}
              {renderEditableField("Estimated Time", 
                <div className="flex gap-3">
                  <Input
                    type="number"
                    label="Hours"
                    min={0}
                    max={999}
                    className="w-24"
                    variant="bordered"
                    value={estimatedHours}
                    onValueChange={setEstimatedHours}
                    classNames={{
                      inputWrapper: "bg-custom-sidebar border-custom-border"
                    }}
                  />
                  <Input
                    type="number"
                    label="Minutes"
                    min={0}
                    max={59}
                    className="w-24"
                    variant="bordered"
                    value={estimatedMinutes}
                    onValueChange={setEstimatedMinutes}
                    classNames={{
                      inputWrapper: "bg-custom-sidebar border-custom-border"
                    }}
                  />
                </div>
              )}
              
              {/* Assignees Field */}
              {renderEditableField("Assignees", 
                <div className="w-full">
                  <Dropdown>
                    <DropdownTrigger>
                      <Button 
                        variant="flat" 
                        className="w-full justify-between bg-custom-sidebar border border-custom-border text-custom-text"
                        endContent={<Icon icon="lucide:chevron-down" className="text-small" />}
                        color={showValidationErrors && assignees.length === 0 ? "danger" : "default"}
                      >
                        {assignees.length > 0 ? `${assignees.length} selected` : "Select assignees"}
                      </Button>
                    </DropdownTrigger>
                    <DropdownMenu 
                      selectionMode="multiple"
                      selectedKeys={new Set(assignees.map(a => a.id))}
                      onSelectionChange={(keys) => {
                        const keyArray = Array.from(keys);
                        setAssignees(mockUsers.filter(user => keyArray.includes(user.id)));
                      }}
                      classNames={{
                        base: "max-h-64 bg-custom-card border border-custom-border",
                      }}
                    >
                      {(task.assignees || []).map(user => (
                        <DropdownItem key={user.id} className="text-custom-text">
                          <div className="flex items-center gap-2">
                            <Avatar src={user.avatar} size="sm" />
                            <div>
                              <div className="text-sm">{user.name}</div>
                              <div className="text-tiny text-custom-muted">{user.role}</div>
                            </div>
                          </div>
                        </DropdownItem>
                      ))}
                    </DropdownMenu>
                  </Dropdown>
                  {showValidationErrors && assignees.length === 0 && (
                    <p className="text-danger text-xs mt-1">At least one assignee is required</p>
                  )}
                  <div className="flex flex-wrap gap-1 mt-2">
                    {assignees.map(assignee => (
                      <Chip 
                        key={assignee.id}
                        variant="flat"
                        onClose={isEditMode ? () => {
                          setAssignees(assignees.filter(a => a.id !== assignee.id));
                        } : undefined}
                        avatar={<Avatar src={assignee.avatar} size="sm" />}
                        className="bg-custom-sidebar border border-custom-border text-custom-text"
                      >
                        {assignee.name}
                      </Chip>
                    ))}
                  </div>
                </div>
              )}

              {/* Viewers Field */}
              {renderEditableField("Visible To", 
                <div className="w-full">
                  <Dropdown>
                    <DropdownTrigger>
                      <Button 
                        variant="flat" 
                        className="w-full justify-between bg-custom-sidebar border border-custom-border text-custom-text"
                        endContent={<Icon icon="lucide:chevron-down" className="text-small" />}
                      >
                        {viewers.length > 0 ? `${viewers.length} selected` : "Select viewers"}
                      </Button>
                    </DropdownTrigger>
                    <DropdownMenu 
                      selectionMode="multiple"
                      selectedKeys={new Set(viewers.map(v => v.id))}
                      onSelectionChange={(keys) => {
                        const keyArray = Array.from(keys);
                        setViewers((task.assignees || []).filter(user => keyArray.includes(user.id)));
                      }}
                      classNames={{
                        base: "max-h-64 bg-custom-card border border-custom-border",
                      }}
                    >
                      {(task.assignees || []).map(user => (
                        <DropdownItem key={user.id} className="text-custom-text">
                          <div className="flex items-center gap-2">
                            <Avatar src={user.avatar} size="sm" />
                            <div>
                              <div className="text-sm">{user.name}</div>
                              <div className="text-tiny text-custom-muted">{user.role}</div>
                            </div>
                          </div>
                        </DropdownItem>
                      ))}
                    </DropdownMenu>
                  </Dropdown>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {viewers.map(viewer => (
                      <Chip 
                        key={viewer.id}
                        variant="flat"
                        onClose={isEditMode ? () => {
                          setViewers(viewers.filter(v => v.id !== viewer.id));
                        } : undefined}
                        avatar={<Avatar src={viewer.avatar} size="sm" />}
                        startContent={<Icon icon="lucide:eye" className="text-tiny" />}
                        className="bg-custom-sidebar border border-custom-border text-custom-text"
                      >
                        {viewer.name}
                      </Chip>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            {/* Progress Management - Full Width */}
            <div className="md:col-span-2 mt-4">
              {renderEditableField("Progress Tracking", 
                <Card className="bg-custom-sidebar border border-custom-border">
                  <CardBody>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-custom-text">Overall Progress</span>
                      <span className="text-lg font-semibold text-custom-text">{calculateAverageProgress()}%</span>
                    </div>
                    <Progress value={calculateAverageProgress()} className="mb-4" 
                      classNames={{
                        track: "bg-custom-border",
                        indicator: "bg-custom-primary",
                      }}
                    />
                    
                    <div className="space-y-4">
                      {task.assignees?.map(assignee => (
                        <div key={assignee.id} className="flex items-center justify-between bg-custom-card p-3 rounded-lg">
                          <div className="flex items-center gap-2">
                            <Avatar src={assignee.avatar} size="sm" />
                            <span className="text-sm text-custom-text">{assignee.name}</span>
                          </div>
                          <div className="w-1/2 flex items-center gap-2">
                            <Slider
                              size="sm"
                              step={5}
                              value={progressValues[assignee.id] || 0}
                              onChange={(value) => handleProgressChange(assignee.id, value as number)}
                              classNames={{
                                track: "bg-custom-border",
                                filler: "bg-custom-primary",
                              }}
                            />
                            <span className="text-sm w-12 text-right text-custom-text">{progressValues[assignee.id] || 0}%</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardBody>
                </Card>
              )}
            </div>
            
            {/* Attachments Section - Full Width */}
            <div className="md:col-span-2 mt-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-base font-medium text-custom-text">Attachments</h3>
                <Button 
                  size="sm" 
                  color="primary" 
                  variant="flat" 
                  startContent={<Icon icon="lucide:upload" />}
                  className="bg-custom-sidebar border border-custom-border"
                >
                  Upload Files
                </Button>
              </div>
              <Card className="border border-custom-border bg-custom-sidebar">
                <CardBody className="p-4">
                  {(!task?.attachments || task.attachments.length === 0) ? (
                    <div className="text-center py-10 bg-custom-card rounded-lg border border-dashed border-custom-border">
                      <Icon icon="lucide:file" className="text-4xl text-custom-muted mx-auto mb-3" />
                      <p className="text-custom-text text-sm">No attachments</p>
                      {isEditMode && (
                        <Button 
                          color="primary" 
                          className="mt-3"
                          startContent={<Icon icon="lucide:upload" />}
                        >
                          Upload Files
                        </Button>
                      )}
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {task.attachments.map((attachment) => (
                        <Card key={attachment.id} className="bg-custom-card border border-custom-border">
                          <CardBody className="p-3 flex items-center gap-3">
                            {attachment.type === 'image' && attachment.url ? (
                              <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                                <img 
                                  src={attachment.url} 
                                  alt={attachment.name}
                                  className="w-full h-full object-cover" 
                                />
                              </div>
                            ) : (
                              <div className="w-16 h-16 rounded-lg bg-custom-sidebar flex items-center justify-center flex-shrink-0">
                                <Icon 
                                  icon={
                                    attachment.type === 'document' ? "lucide:file-text" : 
                                    "lucide:file"
                                  } 
                                  className="text-2xl text-custom-muted"
                                />
                              </div>
                            )}
                            
                            <div className="flex-1 min-w-0">
                              <div className="text-sm font-medium truncate text-custom-text">{attachment.name}</div>
                              <div className="text-xs text-custom-muted">{attachment.size}</div>
                              <div className="text-xs text-custom-muted">Added {formatDate(attachment.uploadedAt || task.createdAt)}</div>
                              
                              <div className="flex gap-1 mt-2">
                                <Button size="sm" variant="flat" className="h-7 px-2 bg-custom-sidebar border border-custom-border text-custom-text">
                                  <Icon icon="lucide:download" className="text-xs" />
                                  <span className="text-xs">Download</span>
                                </Button>
                                {isEditMode && (
                                  <Button size="sm" variant="flat" color="danger" className="h-7 px-2">
                                    <Icon icon="lucide:trash-2" className="text-xs" />
                                    <span className="text-xs">Delete</span>
                                  </Button>
                                )}
                              </div>
                            </div>
                          </CardBody>
                        </Card>
                      ))}
                      
                      {isEditMode && (
                        <Card className="border border-dashed border-custom-border bg-transparent h-full">
                          <CardBody className="p-3 flex flex-col items-center justify-center h-full">
                            <div 
                              className="w-full h-full flex flex-col items-center justify-center cursor-pointer py-8"
                              onDragOver={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                              }}
                              onDrop={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                // Handle file drop logic here
                                console.log("File dropped:", e.dataTransfer.files);
                              }}
                            >
                              <Icon icon="lucide:upload-cloud" className="text-2xl text-custom-muted mb-1" />
                              <span className="text-sm text-custom-muted">Drop files or click to upload</span>
                              <Button 
                                size="sm" 
                                variant="flat" 
                                color="primary" 
                                className="mt-2"
                                startContent={<Icon icon="lucide:plus" />}
                              >
                                Add Files
                              </Button>
                            </div>
                          </CardBody>
                        </Card>
                      )}
                    </div>
                  )}
                </CardBody>
              </Card>
            </div>
          </motion.div>
        ) : (
          // View mode
          <motion.div 
            key="view-mode"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6"
          >
            {/* Left Column */}
            <div className="md:col-span-2 space-y-8">
              {/* Description in view mode */}
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <h4 className="text-lg font-medium text-white">Description</h4>
                  {!description && (
                    <Button 
                      size="sm" 
                      variant="flat" 
                      onPress={() => setIsEditMode(true)} 
                      className="bg-custom-sidebar border border-custom-border"
                    >
                      Add Description
                    </Button>
                  )}
                </div>
                <div className="bg-custom-sidebar p-4 rounded-lg border border-custom-border">
                  <p className="text-custom-text whitespace-pre-line">
                    {description || "No description provided for this task."}
                  </p>
                </div>
              </div>
              
              {/* Progress */}
              <div>
                <h3 className="text-lg font-medium text-white mb-4">Progress</h3>
                <div className="bg-custom-sidebar p-4 rounded-lg border border-custom-border">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <div className="w-10 h-10 rounded-full flex items-center justify-center bg-custom-primary/20 text-custom-primary">
                        <Icon icon="lucide:bar-chart-2" />
                      </div>
                      <span className="text-md font-medium text-custom-text">Overall Progress</span>
                    </div>
                    <span className="text-lg font-semibold text-custom-text">{calculateAverageProgress()}%</span>
                  </div>
                  <Progress 
                    value={calculateAverageProgress()} 
                    className="mb-4" 
                    classNames={{
                      track: "bg-custom-border",
                      indicator: "bg-custom-primary",
                    }}
                  />
                  
                  <div className="space-y-3 mt-4">
                    {task.assignees?.map(assignee => (
                      <div key={assignee.id} className="flex items-center justify-between p-2 rounded-md hover:bg-custom-card transition-colors">
                        <div className="flex items-center gap-2">
                          <Avatar src={assignee.avatar} size="sm" />
                          <span className="text-sm text-custom-text">{assignee.name}</span>
                        </div>
                        <div className="w-1/3 flex items-center gap-2">
                          <Progress 
                            value={progressValues[assignee.id] || 0} 
                            size="sm" 
                            className="flex-1" 
                            classNames={{
                              track: "bg-custom-border",
                              indicator: progressValues[assignee.id] >= 80 ? "bg-success" : 
                                            progressValues[assignee.id] >= 50 ? "bg-custom-primary" : 
                                            progressValues[assignee.id] >= 25 ? "bg-warning" : "bg-danger",
                            }}
                          />
                          <span className="text-sm w-10 text-right text-custom-text">{progressValues[assignee.id] || 0}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              
              {/* Field overview cards */}
              <div>
                <h3 className="text-lg font-medium text-white mb-4">Overview</h3>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div className="p-4 bg-custom-sidebar rounded-lg border border-custom-border">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center gap-2">
                        <div className="p-2 rounded-full bg-success/20">
                          <Icon icon="lucide:panels-top-left" className="text-success" />
                        </div>
                        <span className="text-custom-muted">Project</span>
                      </div>
                    </div>
                    <div className="mt-2 text-custom-text font-medium flex items-center gap-1">
                      {selectedProject ? (
                        <>
                          <span className="w-2 h-2 rounded-full" style={{ backgroundColor: selectedProject.color }}></span>
                          {selectedProject.name}
                        </>
                      ) : "Not assigned"}
                    </div>
                  </div>
                  
                  <div className="p-4 bg-custom-sidebar rounded-lg border border-custom-border">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center gap-2">
                        <div className="p-2 rounded-full bg-warning/20">
                          <Icon icon="lucide:calendar-days" className="text-warning" />
                        </div>
                        <span className="text-custom-muted">Due Date</span>
                      </div>
                    </div>
                    <div className="mt-2 text-custom-text font-medium">
                      {task.dueDate 
                        ? dateFormatter.format(new Date(task.dueDate))
                        : "No due date set"}
                    </div>
                  </div>

                  <div className="p-4 bg-custom-sidebar rounded-lg border border-custom-border">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center gap-2">
                        <div className="p-2 rounded-full bg-custom-primary/20">
                          <Icon icon="lucide:timer" className="text-custom-primary" />
                        </div>
                        <span className="text-custom-muted">Estimated</span>
                      </div>
                    </div>
                    <div className="mt-2 text-custom-text font-medium">
                      {estimatedHours > "0" ? `${estimatedHours}h ` : ''}
                      {estimatedMinutes > "0" ? `${estimatedMinutes}m` : ''}
                      {estimatedHours === "0" && estimatedMinutes === "0" && "No estimate set"}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Assignees in view mode */}
              <div>
                <h4 className="text-lg font-medium text-white mb-4">Assignees</h4>
                <div className="bg-custom-sidebar p-4 rounded-lg border border-custom-border space-y-3">
                  {assignees.map(assignee => (
                    <div key={assignee.id} className="flex items-center justify-between p-2 hover:bg-custom-card rounded-md transition-colors">
                      <div className="flex items-center gap-3">
                        <Avatar src={assignee.avatar} size="md" />
                        <div>
                          <p className="font-medium text-custom-text">{assignee.name}</p>
                          <p className="text-sm text-custom-muted">{assignee.role}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-custom-text">{assignee.tasksCount || 0} tasks</p>
                        <p className="text-sm text-custom-muted">{assignee.hoursSpent || 0}h tracked</p>
                      </div>
                    </div>
                  ))}
                  {assignees.length === 0 && (
                    <div className="text-center py-6">
                      <Icon icon="lucide:users" className="text-3xl text-custom-muted mx-auto mb-2" />
                      <span className="text-sm text-custom-muted">No assignees</span>
                      <Button 
                        size="sm" 
                        color="primary" 
                        variant="flat" 
                        className="mt-2"
                        startContent={<Icon icon="lucide:plus" />}
                        onPress={() => setIsEditMode(true)}
                      >
                        Add Assignee
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              {/* Attachments in view mode */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-medium text-white">Attachments</h4>
                  <Button 
                    size="sm" 
                    variant="flat" 
                    startContent={<Icon icon="lucide:upload" />}
                    className="bg-custom-sidebar border border-custom-border text-custom-text"
                    onPress={() => setIsEditMode(true)}
                  >
                    Upload File
                  </Button>
                </div>
                <Card className="bg-custom-sidebar border border-custom-border shadow-none">
                  <CardBody className="p-3">
                    {(!task?.attachments || task.attachments.length === 0) ? (
                      <div className="text-center py-10">
                        <Icon icon="lucide:file" className="text-4xl text-custom-muted mx-auto mb-3" />
                        <p className="text-custom-muted">No attachments added to this task yet</p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {task.attachments.map((attachment) => (
                          <Card key={attachment.id} className="bg-custom-card border border-custom-border shadow-none">
                            <CardBody className="p-3 flex items-center gap-3">
                              {attachment.type === 'image' && attachment.url ? (
                                <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                                  <img 
                                    src={attachment.url} 
                                    alt={attachment.name}
                                    className="w-full h-full object-cover" 
                                  />
                                </div>
                              ) : (
                                <div className="w-16 h-16 rounded-lg bg-custom-sidebar flex items-center justify-center flex-shrink-0">
                                  <Icon 
                                    icon={
                                      attachment.type === 'document' ? "lucide:file-text" : 
                                      "lucide:file"
                                    } 
                                    className="text-2xl text-custom-muted"
                                  />
                                </div>
                              )}
                              
                              <div className="flex-1 min-w-0">
                                <div className="text-sm font-medium truncate text-custom-text">{attachment.name}</div>
                                <div className="text-xs text-custom-muted">{attachment.size}</div>
                                <div className="text-xs text-custom-muted">Added {formatDate(attachment.uploadedAt || task.createdAt)}</div>
                                
                                <div className="flex gap-1 mt-2">
                                  <Button size="sm" variant="flat" className="h-7 px-2 bg-custom-sidebar border border-custom-border text-custom-text">
                                    <Icon icon="lucide:download" className="text-xs" />
                                    <span className="text-xs">Download</span>
                                  </Button>
                                </div>
                              </div>
                            </CardBody>
                          </Card>
                        ))}
                      </div>
                    )}
                  </CardBody>
                </Card>
              </div>
            </div>
            
            {/* Right Column */}
            <div className="space-y-6">
              {/* Status in view mode */}
              <div className="bg-custom-sidebar p-4 rounded-lg border border-custom-border">
                <span className="text-sm font-medium text-custom-muted block mb-2">Status</span>
                <div className="flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full ${
                    task?.status === "done" ? "bg-success" : 
                    task?.status === "inprogress" ? "bg-custom-primary" :
                    task?.status === "review" ? "bg-warning" : "bg-slate-400"
                  }`}></div>
                  <Chip
                    variant="flat"
                    color={
                      task?.status === "done" ? "success" : 
                      task?.status === "inprogress" ? "primary" :
                      task?.status === "review" ? "warning" : "default"
                    }
                    className="bg-custom-card/30"
                  >
                    {task?.status === "todo" ? "To Do" :
                     task?.status === "inprogress" ? "In Progress" :
                     task?.status === "review" ? "Review" :
                     task?.status === "done" ? "Done" : task?.status}
                  </Chip>
                </div>
              </div>

              {/* Priority in view mode */}
              <div className="bg-custom-sidebar p-4 rounded-lg border border-custom-border">
                <span className="text-sm font-medium text-custom-muted block mb-2">Priority</span>
                <div className="flex items-center gap-2">
                  {(() => {
                    const color = task.priority === "High" ? "text-danger bg-danger/20" : 
                                task.priority === "Medium" ? "text-warning bg-warning/20" : 
                                "text-success bg-success/20";
                    const icon = task.priority === "High" ? "lucide:alert-triangle" : 
                                task.priority === "Medium" ? "lucide:flag" : 
                                "lucide:check-circle";
                    
                    return (
                      <div className={`p-1 rounded-full ${color.split(' ')[1]}`}>
                        <Icon icon={icon} className={color.split(' ')[0]} />
                      </div>
                    );
                  })()}
                  <span className="text-custom-text">{task.priority}</span>
                </div>
              </div>

              {/* Tags in view mode */}
              <div className="bg-custom-sidebar p-4 rounded-lg border border-custom-border">
                <span className="text-sm font-medium text-custom-muted block mb-2">Tags</span>
                <div className="flex flex-wrap gap-2">
                  {tags.length > 0 ? tags.map(tag => (
                    <Chip 
                      key={tag} 
                      variant="flat" 
                      className={`${getTagColor(tag)} border border-custom-border`}
                    >
                      {tag}
                    </Chip>
                  )) : (
                    <span className="text-sm text-custom-muted">No tags</span>
                  )}
                </div>
              </div>
              
              {/* Viewers in view mode */}
              <div className="bg-custom-sidebar p-4 rounded-lg border border-custom-border">
                <span className="text-sm font-medium text-custom-muted block mb-2">Visible To</span>
                <div className="space-y-3">
                  {viewers.length > 0 ? (
                    viewers.map(viewer => (
                      <div key={viewer.id} className="flex items-center gap-2 bg-custom-card/30 p-2 rounded-lg">
                        <Avatar src={viewer.avatar} size="sm" />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-custom-text truncate">{viewer.name}</p>
                          <p className="text-xs text-custom-muted">{viewer.role}</p>
                        </div>
                        <Icon icon="lucide:eye" className="text-custom-muted" size={16} />
                      </div>
                    ))
                  ) : (
                    <div className="flex items-center gap-2 bg-custom-card/30 p-2 rounded-lg">
                      <div className="p-1 rounded-full bg-custom-primary/20">
                        <Icon icon="lucide:users" className="text-custom-primary" size={16} />
                      </div>
                      <span className="text-sm text-custom-text">All team members can view this task</span>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Dates & Timeline */}
              <div className="bg-custom-sidebar p-4 rounded-lg border border-custom-border">
                <span className="text-sm font-medium text-custom-muted block mb-3">Timeline</span>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="p-1 rounded-full bg-custom-primary/20">
                      <Icon icon="lucide:plus-circle" className="text-custom-primary" size={16} />
                    </div>
                    <div>
                      <p className="text-xs text-custom-muted">Created</p>
                      <p className="text-sm text-custom-text">{formatDate(task?.createdAt || '')}</p>
                    </div>
                  </div>
                  
                  {task?.updatedAt !== task?.createdAt && (
                    <div className="flex items-center gap-3">
                      <div className="p-1 rounded-full bg-custom-primary/20">
                        <Icon icon="lucide:edit" className="text-custom-primary" size={16} />
                      </div>
                      <div>
                        <p className="text-xs text-custom-muted">Updated</p>
                        <p className="text-sm text-custom-text">{formatDate(task?.updatedAt || '')}</p>
                      </div>
                    </div>
                  )}
                  
                  {task?.dueDate && (
                    <div className="flex items-center gap-3">
                      <div className="p-1 rounded-full bg-warning/20">
                        <Icon icon="lucide:calendar-clock" className="text-warning" size={16} />
                      </div>
                      <div>
                        <p className="text-xs text-custom-muted">Due Date</p>
                        <p className="text-sm text-custom-text">{formatDate(task?.dueDate)}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};