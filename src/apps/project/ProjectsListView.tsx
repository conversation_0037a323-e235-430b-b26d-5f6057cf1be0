import React, { useState } from "react";
    import { 
      Card, 
      CardBody, 
      Button, 
      Avatar, 
      AvatarGroup, 
      Input,
      Progress,
      Badge
    } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import { mockProjects } from "./data/mock-data";
    import { ProjectDetailsModal } from "./components/ProjectDetailsModal";
    import type { Project } from "./types";
    import { getProjectStats } from "./utils/project-utils";

    export const ProjectsListView = () => {
      const [searchQuery, setSearchQuery] = useState("");
      const [selectedProject, setSelectedProject] = useState<Project | null>(null);
      const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
      
      const filteredProjects = mockProjects.filter(project => 
        project.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      
      const handleProjectClick = (project: Project) => {
        setSelectedProject(project);
        setIsDetailsModalOpen(true);
      };
      
      return (
        <div className="p-6 max-w-[1600px] mx-auto">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-white text-2xl font-semibold">Projects</h1>
            
            <div className="flex items-center gap-3">
              <div className="w-64">
                <Input
                  placeholder="Search projects..."
                  value={searchQuery}
                  onValueChange={setSearchQuery}
                  startContent={<Icon icon="lucide:search" className="text-custom-muted" />}
                  classNames={{
                    inputWrapper: "bg-custom-card border-custom-border h-10",
                  }}
                  size="sm"
                  radius="lg"
                  variant="bordered"
                />
              </div>
              
              <Button 
                color="primary"
                startContent={<Icon icon="lucide:plus" />}
              >
                New Project
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredProjects.map((project) => {
              const stats = getProjectStats(project.id);
              
              return (
                <Card 
                  key={project.id}
                  className="bg-custom-card border-custom-border hover:bg-custom-sidebar transition-colors cursor-pointer"
                  isPressable
                  onPress={() => handleProjectClick(project)}
                >
                  <CardBody className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div 
                        className="w-10 h-10 rounded-md flex items-center justify-center"
                        style={{ backgroundColor: project.color }}
                      >
                        <Icon 
                          icon={project.icon || "lucide:folder"} 
                          className="text-white text-xl"
                        />
                      </div>
                      <div>
                        <h3 className="text-white text-lg font-medium">{project.name}</h3>
                        <p className="text-custom-muted text-sm">{stats.tasksCount} tasks</p>
                      </div>
                    </div>
                    
                    <div className="mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-custom-muted text-xs">Progress</span>
                        <span className="text-white text-xs font-medium">{stats.progressPercentage}%</span>
                      </div>
                      <Progress 
                        value={stats.progressPercentage} 
                        color="primary"
                        size="sm"
                        className="h-1"
                      />
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <AvatarGroup max={4} size="sm">
                        {stats.teamMembers.map(member => (
                          <Avatar 
                            key={member.id}
                            src={member.avatar} 
                            name={member.name} 
                          />
                        ))}
                      </AvatarGroup>
                      
                      <div className="flex items-center gap-2">
                        <Icon icon="lucide:clock" className="text-custom-muted text-sm" />
                        <span className="text-white text-sm">{stats.totalHours}h</span>
                      </div>
                    </div>
                    
                    {stats.hasAIReports && (
                      <Badge color="secondary" className="top-2 right-2">AI</Badge>
                    )}
                  </CardBody>
                </Card>
              );
            })}
          </div>
          
          {selectedProject && (
            <ProjectDetailsModal
              project={selectedProject}
              isOpen={isDetailsModalOpen}
              onClose={() => setIsDetailsModalOpen(false)}
            />
          )}
        </div>
      );
    };