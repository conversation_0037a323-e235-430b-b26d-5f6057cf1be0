import React from "react";
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Button, 
  Dropdown, 
  DropdownTrigger, 
  DropdownMenu, 
  DropdownItem, 
  Input 
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { DragDropContext } from 'react-beautiful-dnd';
import type { DropResult } from 'react-beautiful-dnd';
import { mockBoards, mockTasks } from './data/mock-data';
import type { Board, Task } from './types';
import { KanbanBoard } from './components/KanbanBoard';
import { TaskModal } from './components/TaskModal';
import { useState } from 'react';
import { FilterPanel } from './components/FilterPanel';
import { Chip } from "@heroui/react";
import { ProjectsListView } from "./ProjectsListView";
import { useLocation, useNavigate } from "react-router-dom";

export const ProjectApp = () => {
  const [boards, setBoards] = useState<Board[]>(mockBoards);
  const [selectedBoardId, setSelectedBoardId] = useState<string>(mockBoards[0].id);
  const [tasks, setTasks] = useState<Record<string, Task>>(mockTasks);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = React.useState<string>("dueDate");
  const [sortOrder, setSortOrder] = React.useState<"asc" | "desc">("asc");
  const [filterPriorities, setFilterPriorities] = React.useState<string[]>([]);
  const [filterTags, setFilterTags] = React.useState<string[]>([]);
  const [filterAssignees, setFilterAssignees] = React.useState<string[]>([]);
  const [showCompletedTasks, setShowCompletedTasks] = React.useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const segment = location.pathname.split("/")[2] || "projects";
  const view: "kanban" | "projects" = segment === "boards" ? "kanban" : "projects";

  const selectedBoard = boards.find((board) => board.id === selectedBoardId) || boards[0];

  const handleDragEnd = (result: DropResult) => {
    const { destination, source, draggableId } = result;

    // Log the drag result for debugging
    console.log('Drag end result:', result);

    // Dropped outside any droppable area
    if (!destination) {
      console.log('Dropped outside droppable area');
      return;
    }

    // Dropped in the same position
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      console.log('Dropped in same position');
      return;
    }

    // Find source and destination columns
    const newBoards = [...boards];
    const boardIndex = newBoards.findIndex(board => board.id === selectedBoardId);
    if (boardIndex === -1) {
      console.log('Board not found');
      return; // Early return if board not found
    }
    
    const board = newBoards[boardIndex];
    
    const getColumnId = (droppableId: string) => droppableId.split('--')[0];

    const sourceColId = getColumnId(source.droppableId);
    const destColId = getColumnId(destination.droppableId);

    const sourceColIndex = board.columns.findIndex(col => col.id === sourceColId);
    const destColIndex = board.columns.findIndex(col => col.id === destColId);
    
    // Check if columns exist before proceeding
    if (sourceColIndex === -1 || destColIndex === -1) {
      console.log('Column not found', { sourceColIndex, destColIndex });
      return;
    }
    
    const sourceCol = board.columns[sourceColIndex];
    const destCol = board.columns[destColIndex];

    // Create new taskIds arrays for the columns
    const sourceTaskIds = [...sourceCol.taskIds];
    sourceTaskIds.splice(source.index, 1);
    
    const destTaskIds = 
      source.droppableId === destination.droppableId
        ? sourceTaskIds // If same column, use the updated sourceTaskIds
        : [...destCol.taskIds]; // Otherwise, create a copy of destination taskIds
    
    destTaskIds.splice(destination.index, 0, draggableId);

    // Create new columns
    const newSourceCol = {
      ...sourceCol,
      taskIds: sourceTaskIds
    };

    const newDestCol = {
      ...destCol,
      taskIds: destTaskIds
    };

    // Update the board with new columns
    const newColumns = [...board.columns];
    newColumns[sourceColIndex] = newSourceCol;
    newColumns[destColIndex] = newDestCol;

    // Create new board with updated columns
    const newBoard = {
      ...board,
      columns: newColumns
    };

    console.log('Board updated successfully', { 
      oldColumns: board.columns,
      newColumns: newBoard.columns 
    });

    newBoards[boardIndex] = newBoard;
    setBoards(newBoards);
  };

  // Filter tasks based on search query and other filters
  const getFilteredTasks = () => {
    let filtered = { ...tasks };
    
    // Text search
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = Object.entries(filtered).reduce((result, [id, task]) => {
        if (
          task.title.toLowerCase().includes(query) ||
          task.description?.toLowerCase().includes(query) ||
          task.tag.toLowerCase().includes(query) ||
          task.project.name.toLowerCase().includes(query)
        ) {
          result[id] = task;
        }
        return result;
      }, {} as Record<string, Task>);
    }
    
    // Priority filter
    if (filterPriorities.length > 0) {
      filtered = Object.entries(filtered).reduce((result, [id, task]) => {
        if (filterPriorities.includes(task.priority)) {
          result[id] = task;
        }
        return result;
      }, {} as Record<string, Task>);
    }
    
    // Tag filter
    if (filterTags.length > 0) {
      filtered = Object.entries(filtered).reduce((result, [id, task]) => {
        if (filterTags.includes(task.tag)) {
          result[id] = task;
        }
        return result;
      }, {} as Record<string, Task>);
    }
    
    // Assignee filter
    if (filterAssignees.length > 0) {
      filtered = Object.entries(filtered).reduce((result, [id, task]) => {
        if (task.assignees.some(assignee => filterAssignees.includes(assignee.id))) {
          result[id] = task;
        }
        return result;
      }, {} as Record<string, Task>);
    }
    
    // Show/hide completed tasks
    if (!showCompletedTasks) {
      filtered = Object.entries(filtered).reduce((result, [id, task]) => {
        if (task.status !== "done") {
          result[id] = task;
        }
        return result;
      }, {} as Record<string, Task>);
    }
    
    return filtered;
  };

  const handleApplyFilters = (filters: {
    priorities: string[],
    tags: string[],
    assignees: string[],
    showCompleted: boolean,
    sort: string,
    order: "asc" | "desc"
  }) => {
    setFilterPriorities(filters.priorities);
    setFilterTags(filters.tags);
    setFilterAssignees(filters.assignees);
    setShowCompletedTasks(filters.showCompleted);
    setSortBy(filters.sort);
    setSortOrder(filters.order);
  };

  const filteredTasks = getFilteredTasks();

  const toProjects = ()=> navigate("/project/projects");
  const toBoards = ()=> navigate("/project/boards");

  return (
    <div className="max-w-[1600px] mx-auto">

      
      {segment === "boards" ? (
        <>
        <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <h1 className="text-white text-2xl font-semibold">Boards</h1>
                    
          <Dropdown>
            <DropdownTrigger>
              <Button 
                variant="flat" 
                color="default"
                className="bg-custom-card border border-custom-border"
                endContent={<Icon icon="lucide:chevron-down" className="text-sm" />}
              >
                {selectedBoard.title}
              </Button>
            </DropdownTrigger>
            <DropdownMenu aria-label="Board Selection">
              {boards.map((board) => (
                <DropdownItem 
                  key={board.id} 
                  onPress={() => setSelectedBoardId(board.id)}
                >
                  {board.title}
                </DropdownItem>
              ))}
            </DropdownMenu>
          </Dropdown>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="w-64">
            <Input
              placeholder="Search tasks..."
              value={searchQuery}
              onValueChange={setSearchQuery}
              startContent={<Icon icon="lucide:search" className="text-custom-muted" />}
              classNames={{
                inputWrapper: "bg-custom-card border-custom-border h-10",
              }}
              size="sm"
              radius="lg"
              variant="bordered"
            />
          </div>
          
          <Dropdown>
            <DropdownTrigger>
              <Button 
                variant="flat" 
                className="bg-custom-card border border-custom-border"
                endContent={<Icon icon="lucide:chevron-down" className="text-sm" />}
              >
                Sort: {sortBy} {sortOrder === "asc" ? "↑" : "↓"}
              </Button>
            </DropdownTrigger>
            <DropdownMenu aria-label="Sort Options">
              <DropdownItem key="dueAsc" onPress={() => { setSortBy("dueDate"); setSortOrder("asc"); }}>
                Due Date (Ascending)
              </DropdownItem>
              <DropdownItem key="dueDesc" onPress={() => { setSortBy("dueDate"); setSortOrder("desc"); }}>
                Due Date (Descending)
              </DropdownItem>
              <DropdownItem key="prioDesc" onPress={() => { setSortBy("priority"); setSortOrder("desc"); }}>
                Priority (High to Low)
              </DropdownItem>
              <DropdownItem key="prioAsc" onPress={() => { setSortBy("priority"); setSortOrder("asc"); }}>
                Priority (Low to High)
              </DropdownItem>
              <DropdownItem key="titleAsc" onPress={() => { setSortBy("title"); setSortOrder("asc"); }}>
                Title (A-Z)
              </DropdownItem>
              <DropdownItem key="titleDesc" onPress={() => { setSortBy("title"); setSortOrder("desc"); }}>
                Title (Z-A)
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
          
          <Button 
            variant="flat" 
            color={showFilterPanel ? "primary" : "default"}
            className="bg-custom-card border border-custom-border"
            startContent={<Icon icon="lucide:filter" />}
            onPress={() => setShowFilterPanel(!showFilterPanel)}
          >
            Filter
          </Button>
          
          <Button 
            variant="flat" 
            color="primary"
            startContent={<Icon icon="lucide:plus" />}
          >
            Add Task
          </Button>
        </div>
      </div>
      <div className="flex items-center gap-2 mb-4">
        {filterPriorities.length > 0 && (
          <Chip 
            variant="flat" 
            color="primary"
            onClose={() => setFilterPriorities([])}
          >
            {filterPriorities.length} Priorities
          </Chip>
        )}
        {filterTags.length > 0 && (
          <Chip 
            variant="flat" 
            color="primary" 
            onClose={() => setFilterTags([])}
          >
            {filterTags.length} Tags
          </Chip>
        )}
        {filterAssignees.length > 0 && (
          <Chip 
            variant="flat" 
            color="primary"
            onClose={() => setFilterAssignees([])}
          >
            {filterAssignees.length} Assignees
          </Chip>
        )}
        {!showCompletedTasks && (
          <Chip 
            variant="flat" 
            color="primary"
            onClose={() => setShowCompletedTasks(true)}
          >
            Hiding Completed
          </Chip>
        )}
        
        {(filterPriorities.length > 0 || filterTags.length > 0 || filterAssignees.length > 0 || !showCompletedTasks) && (
          <Button 
            size="sm" 
            variant="light" 
            onPress={() => {
              setFilterPriorities([]);
              setFilterTags([]);
              setFilterAssignees([]);
              setShowCompletedTasks(true);
            }}
          >
            Clear All Filters
          </Button>
        )}
      </div>
      
      {showFilterPanel && (
        <FilterPanel 
          onClose={() => setShowFilterPanel(false)} 
          onApply={handleApplyFilters}
          initialFilters={{
            priorities: filterPriorities,
            tags: filterTags,
            assignees: filterAssignees,
            showCompleted: showCompletedTasks,
            sort: sortBy,
            order: sortOrder
          }}
        />
      )}

      <div className="overflow-x-auto pb-6">
        <div className="min-w-[900px]">
          <DragDropContext 
            onDragEnd={handleDragEnd}
            onDragStart={(start) => {
              document.body.classList.add('is-dragging');
              console.log('Drag started:', start);
            }}
            onDragUpdate={(update) => {
              console.log('Drag update:', update);
            }}
            onBeforeDragStart={() => {
              console.log('Before drag start');
            }}
            onBeforeCapture={() => {
              console.log('Before capture');
            }}
          >
            <KanbanBoard 
              board={selectedBoard} 
              tasks={filteredTasks}
              onTaskClick={setSelectedTask} 
            />
          </DragDropContext>
        </div>
      </div>
      </div>
        </>
      ) : (
        // Projects List View
        <ProjectsListView />
      )}

      <TaskModal 
        task={selectedTask} 
        isOpen={!!selectedTask} 
        onClose={() => setSelectedTask(null)} 
      />
    </div>
  );
};

interface KanbanColumnProps {
  title: string;
  count: number;
  tasks: Array<{
    title: string;
    tag: string;
    priority: string;
  }>;
}

const KanbanColumn: React.FC<KanbanColumnProps> = ({ title, count, tasks }) => {
  const getTagColor = (tag: string) => {
    switch (tag) {
      case "Design": return "text-purple-500 bg-purple-500/10";
      case "Development": return "text-primary bg-primary/10";
      case "Bug": return "text-red-500 bg-red-500/10";
      case "Feature": return "text-green-500 bg-green-500/10";
      case "Documentation": return "text-yellow-500 bg-yellow-500/10";
      case "Setup": return "text-cyan-500 bg-cyan-500/10";
      case "Architecture": return "text-orange-500 bg-orange-500/10";
      default: return "text-custom-text bg-custom-border/30";
    }
  };
  
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "High": return <Icon icon="lucide:flag" className="text-red-500" />;
      case "Medium": return <Icon icon="lucide:flag" className="text-yellow-500" />;
      case "Low": return <Icon icon="lucide:flag" className="text-green-500" />;
      default: return <Icon icon="lucide:flag" className="text-custom-muted" />;
    }
  };
  
  return (
    <Card className="bg-custom-card border-custom-border shadow-none">
      <CardHeader className="flex justify-between items-center border-b border-custom-border">
        <div className="flex items-center">
          <h3 className="text-white text-base font-medium">{title}</h3>
          <span className="ml-2 px-2 py-0.5 bg-custom-border rounded-full text-xs text-custom-muted">
            {count}
          </span>
        </div>
        <Button isIconOnly variant="light" className="text-custom-muted">
          <Icon icon="lucide:plus" />
        </Button>
      </CardHeader>
      <CardBody className="p-3 space-y-3">
        {tasks.map((task, index) => (
          <div 
            key={index} 
            className="p-3 bg-custom-sidebar rounded-lg cursor-pointer hover:bg-custom-border/30 transition-colors"
          >
            <div className="flex justify-between items-center mb-2">
              <span className={`px-2 py-0.5 rounded text-xs ${getTagColor(task.tag)}`}>
                {task.tag}
              </span>
              {getPriorityIcon(task.priority)}
            </div>
            <h4 className="text-white text-sm font-medium">{task.title}</h4>
          </div>
        ))}
      </CardBody>
    </Card>
  );
};