import { mockTasks, mockUsers } from "../data/mock-data";
    import type { ProjectTodo, ProjectDocument, ProjectLink, AIReport, User, Project } from "../types";

    // Sample data for project todos
    const mockProjectTodos: ProjectTodo[] = [
      {
        id: "todo-1",
        title: "Create project wireframes",
        completed: true,
        dueDate: "2023-11-15",
        startDate: "2023-11-10",
        createdAt: "2023-11-10",
        assignees: [mockUsers[2]],
        projectId: "project-1"
      },
      {
        id: "todo-2",
        title: "Develop landing page",
        completed: false,
        dueDate: "2023-11-20",
        startDate: "2023-11-16",
        createdAt: "2023-11-15",
        assignees: [mockUsers[1], mockUsers[3]],
        projectId: "project-1"
      },
      {
        id: "todo-3",
        title: "Set up analytics",
        completed: false,
        dueDate: "2023-11-25",
        startDate: "2023-11-21",
        createdAt: "2023-11-15",
        projectId: "project-1"
      },
      {
        id: "todo-4",
        title: "User testing",
        completed: false,
        dueDate: "2023-11-30",
        startDate: "2023-11-26",
        createdAt: "2023-11-15",
        assignees: [mockUsers[0], mockUsers[3]],
        projectId: "project-1"
      },
      {
        id: "todo-5",
        title: "App architecture design",
        completed: true,
        dueDate: "2023-11-12",
        startDate: "2023-11-08",
        createdAt: "2023-11-08",
        assignees: [mockUsers[1]],
        projectId: "project-2"
      },
      {
        id: "todo-6",
        title: "Backend API development",
        completed: false,
        dueDate: "2023-11-22",
        startDate: "2023-11-13",
        createdAt: "2023-11-13",
        assignees: [mockUsers[1]],
        projectId: "project-2"
      }
    ];

    // Sample data for project documents
    const mockProjectDocuments: ProjectDocument[] = [
      {
        id: "doc-1",
        name: "Project Requirements.pdf",
        type: "pdf",
        size: "2.4 MB",
        url: "#",
        createdAt: "2023-11-08",
        createdBy: mockUsers[0],
        projectId: "project-1"
      },
      {
        id: "doc-2",
        name: "Design Guidelines.pdf",
        type: "pdf",
        size: "5.7 MB",
        url: "#",
        createdAt: "2023-11-09",
        createdBy: mockUsers[2],
        projectId: "project-1"
      },
      {
        id: "doc-3",
        name: "Technical Specifications.docx",
        type: "doc",
        size: "1.2 MB",
        url: "#",
        createdAt: "2023-11-10",
        createdBy: mockUsers[1],
        projectId: "project-1"
      },
      {
        id: "doc-4",
        name: "Meeting Notes.docx",
        type: "doc",
        size: "0.5 MB",
        url: "#",
        createdAt: "2023-11-12",
        createdBy: mockUsers[0],
        projectId: "project-1"
      },
      {
        id: "doc-5",
        name: "App Architecture.pdf",
        type: "pdf",
        size: "3.1 MB",
        url: "#",
        createdAt: "2023-11-08",
        createdBy: mockUsers[1],
        projectId: "project-2"
      }
    ];

    // Sample data for project links
    const mockProjectLinks: ProjectLink[] = [
      {
        title: "Design Figma",
        url: "https://figma.com/project-design",
        projectId: "project-1"
      },
      {
        title: "GitHub Repository",
        url: "https://github.com/organization/project",
        projectId: "project-1"
      },
      {
        title: "Staging Environment",
        url: "https://staging.project.com",
        projectId: "project-1"
      },
      {
        title: "API Documentation",
        url: "https://api-docs.project.com",
        projectId: "project-2"
      }
    ];

    // Sample data for AI reports
    const mockAIReports: AIReport[] = [
      {
        id: "report-1",
        title: "Weekly Progress Analysis",
        summary: "The project is on track with 85% of tasks completed on time. Team velocity has increased by 12% compared to last week.",
        date: "2023-11-15",
        projectId: "project-1",
        isNew: true
      },
      {
        id: "report-2",
        title: "Resource Allocation Insights",
        summary: "Team resources appear to be imbalanced. Consider redistributing tasks to improve efficiency and prevent bottlenecks.",
        date: "2023-11-10",
        projectId: "project-1"
      },
      {
        id: "report-3",
        title: "Risk Assessment",
        summary: "Several tasks are at risk of missing deadlines. The critical path analysis suggests focusing on API development to avoid delays.",
        date: "2023-11-05",
        projectId: "project-1"
      },
      {
        id: "report-4",
        title: "Technical Debt Analysis",
        summary: "Current technical debt levels are within acceptable range. Backend implementation has improved code quality by 15% this month.",
        date: "2023-11-12",
        projectId: "project-2",
        isNew: true
      }
    ];

    /**
     * Get statistics for a specific project
     */
    export const getProjectStats = (projectId: string) => {
      // Get tasks for this project
      const projectTasks = Object.values(mockTasks).filter(task => 
        task.project && task.project.id === projectId
      );
      
      // Get todos for this project
      const projectTodos = mockProjectTodos.filter(todo => 
        todo.projectId === projectId
      );
      
      // Get unique team members who have tasks in this project
      const teamMembersMap = new Map<string, {
        id: string;
        name: string;
        avatar: string;
        role: string;
        tasksCount: number;
        hoursSpent: number;
      }>();
      
      projectTasks.forEach(task => {
        task.assignees.forEach(user => {
          if (!teamMembersMap.has(user.id)) {
            teamMembersMap.set(user.id, {
              id: user.id,
              name: user.name,
              avatar: user.avatar,
              role: user.role,
              tasksCount: 1,
              hoursSpent: 0
            });
          } else {
            const member = teamMembersMap.get(user.id);
            if (member) {
              member.tasksCount += 1;
            }
          }
        });
        
        // Add hours from time logs
        if (task.timeLogs) {
          task.timeLogs.forEach(log => {
            const userId = log.user.id;
            const hours = parseFloat(log.duration.split('h')[0]);
            
            if (teamMembersMap.has(userId)) {
              const member = teamMembersMap.get(userId);
              if (member) {
                member.hoursSpent += hours;
              }
            }
          });
        }
      });
      
      const teamMembers = Array.from(teamMembersMap.values());
      
      // Calculate project progress based on todos
      const totalTodos = projectTodos.length;
      const completedTodos = projectTodos.filter(todo => todo.completed).length;
      const progressPercentage = totalTodos > 0 ? Math.round((completedTodos / totalTodos) * 100) : 0;
      
      // Calculate total hours spent on the project
      const totalHours = teamMembers.reduce((total, member) => total + member.hoursSpent, 0);
      
      // Count overdue todos
      const overdueTodos = projectTodos.filter(todo => 
        !todo.completed && todo.dueDate && new Date(todo.dueDate) < new Date()
      );
      
      // Check if project has AI reports
      const hasAIReports = mockAIReports.some(report => report.projectId === projectId);
      
      return {
        tasksCount: projectTasks.length,
        completedTasksCount: projectTasks.filter(task => task.status === 'done').length,
        teamMembers,
        progressPercentage,
        totalHours,
        todoCount: totalTodos,
        completedTodoCount: completedTodos,
        overdueTodoCount: overdueTodos.length,
        hasAIReports
      };
    };

    /**
     * Get todos for a specific project
     */
    export const getProjectTodos = (projectId: string) => {
      return mockProjectTodos.filter(todo => todo.projectId === projectId);
    };

    /**
     * Get documents for a specific project
     */
    export const getProjectDocuments = (projectId: string) => {
      return mockProjectDocuments.filter(doc => doc.projectId === projectId);
    };

    /**
     * Get links for a specific project
     */
    export const getProjectLinks = (projectId: string) => {
      return mockProjectLinks.filter(link => link.projectId === projectId);
    };

    /**
     * Get AI reports for a specific project
     */
    export const getProjectAIReports = (projectId: string) => {
      return mockAIReports.filter(report => report.projectId === projectId);
    };