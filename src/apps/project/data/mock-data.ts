import type { Board, Project, Task, User } from '../types';
import { addDays, subDays, format } from 'date-fns';

export const mockUsers: User[] = [
  {
    id: "user-1",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=1",
    role: "Product Manager",
    status: "active"
  },
  {
    id: "user-2",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=2",
    role: "Developer",
    status: "busy"
  },
  {
    id: "user-3",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=3",
    role: "Designer",
    status: "active"
  },
  {
    id: "user-4",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=4",
    role: "QA Engineer",
    status: "inactive"
  }
];

export const mockProjects: Project[] = [
  {
    id: "project-1",
    name: "Website Redesign",
    color: "#4f46e5",
    icon: "lucide:layout",
    description: "Complete redesign of the company website with modern UI/UX principles, responsive design, and improved performance metrics. The project aims to enhance user engagement and conversion rates.",
    deadline: new Date(new Date().setDate(new Date().getDate() + 14)).toISOString(),
    createdAt: new Date(new Date().setDate(new Date().getDate() - 10)).toISOString()
  },
  {
    id: "project-2",
    name: "Mobile App",
    color: "#0ea5e9",
    icon: "lucide:smartphone",
    description: "Develop a cross-platform mobile application for iOS and Android that integrates with our existing services. The app will provide core functionality to users on the go with a focus on performance and offline capabilities.",
    deadline: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString(),
    createdAt: new Date(new Date().setDate(new Date().getDate() - 5)).toISOString()
  },
  {
    id: "project-3",
    name: "Marketing Campaign",
    color: "#10b981",
    icon: "lucide:megaphone",
    description: "Q3 marketing campaign across multiple channels including social media, email, and content marketing. The campaign will focus on promoting our new product features and expanding market reach.",
    deadline: new Date(new Date().setDate(new Date().getDate() + 20)).toISOString(),
    createdAt: new Date(new Date().setDate(new Date().getDate() - 8)).toISOString()
  },
  {
    id: "project-4",
    name: "Internal Tools",
    color: "#f59e0b",
    icon: "lucide:tool",
    description: "Develop internal tools for improving team productivity and project management. The tools will integrate with our existing workflow and provide better insights into project progress and resource allocation.",
    deadline: new Date(new Date().setDate(new Date().getDate() + 45)).toISOString(),
    createdAt: new Date(new Date().setDate(new Date().getDate() - 15)).toISOString()
  },
  {
    id: "project-5",
    name: "Customer Portal",
    color: "#8b5cf6",
    icon: "lucide:users",
    description: "Create a self-service customer portal where clients can manage their accounts, access support resources, and view usage analytics.",
    deadline: new Date(new Date().setDate(new Date().getDate() + 60)).toISOString(),
    createdAt: new Date(new Date().setDate(new Date().getDate() - 3)).toISOString()
  },
  {
    id: "project-6",
    name: "Data Migration",
    color: "#ec4899",
    icon: "lucide:database",
    description: "Migrate legacy database systems to a new cloud-based architecture with minimal downtime and complete data integrity.",
    deadline: new Date(new Date().setDate(new Date().getDate() + 25)).toISOString(),
    createdAt: new Date(new Date().setDate(new Date().getDate() - 12)).toISOString()
  }
];

export const mockTasks: Record<string, Task> = {
  'task1': {
    id: 'task1',
    title: 'Update dashboard UI',
    description: 'Revamp the dashboard UI to match the new design system. Focus on making it more intuitive and visually appealing.',
    tag: 'Design',
    project: mockProjects[0],
    priority: 'High',
    dueDate: format(addDays(new Date(), 5), 'yyyy-MM-dd'),
    assignees: [mockUsers[0]],
    comments: [
      {
        id: 'comment1',
        user: mockUsers[1],
        text: 'Please make sure to use the new color palette we discussed.',
        timestamp: '2023-11-10T14:30:00Z'
      }
    ],
    attachments: [
      {
        id: 'attach1',
        name: 'dashboard_mockup.png',
        type: 'image',
        url: '#',
        size: '2.4 MB',
        thumbnailUrl: 'https://img.heroui.chat/image/dashboard?w=200&h=120&u=1'
      }
    ],
    timeLogs: [
      {
        id: 'log1',
        user: mockUsers[0],
        duration: '2h 15m',
        timestamp: '2023-11-10T12:00:00Z',
        description: 'Initial wireframing'
      }
    ],
    createdAt: '2023-11-09T10:00:00Z',
    updatedAt: '2023-11-10T15:00:00Z'
  },
  'task2': {
    id: 'task2',
    title: 'Implement sidebar navigation',
    description: 'Create a responsive sidebar navigation component that works well on all device sizes.',
    tag: 'Development',
    project: mockProjects[0],
    priority: 'Medium',
    dueDate: format(addDays(new Date(), 2), 'yyyy-MM-dd'),
    assignees: [mockUsers[2], mockUsers[3]],
    comments: [],
    attachments: [],
    timeLogs: [],
    createdAt: '2023-11-09T11:00:00Z',
    updatedAt: '2023-11-09T11:00:00Z'
  },
  'task3': {
    id: 'task3',
    title: 'Fix heatmap responsiveness',
    description: 'The heatmap component is not rendering correctly on smaller screens. Fix the responsive behavior.',
    tag: 'Bug',
    project: mockProjects[0],
    priority: 'Low',
    dueDate: format(addDays(new Date(), 1), 'yyyy-MM-dd'),
    assignees: [mockUsers[3]],
    comments: [],
    attachments: [],
    timeLogs: [],
    createdAt: '2023-11-09T12:00:00Z',
    updatedAt: '2023-11-09T12:00:00Z'
  },
  'task4': {
    id: 'task4',
    title: 'Add user settings page',
    description: 'Create a settings page that allows users to customize their profile and preferences.',
    tag: 'Feature',
    project: mockProjects[0],
    priority: 'Medium',
    dueDate: format(addDays(new Date(), 7), 'yyyy-MM-dd'),
    assignees: [mockUsers[2]],
    comments: [],
    attachments: [],
    timeLogs: [],
    createdAt: '2023-11-09T13:00:00Z',
    updatedAt: '2023-11-09T13:00:00Z'
  },
  'task5': {
    id: 'task5',
    title: 'Update documentation',
    description: 'Update the developer documentation to reflect the latest changes in the API.',
    tag: 'Documentation',
    project: mockProjects[0],
    priority: 'Low',
    dueDate: format(addDays(new Date(), 10), 'yyyy-MM-dd'),
    assignees: [mockUsers[1]],
    comments: [],
    attachments: [],
    timeLogs: [],
    createdAt: '2023-11-09T14:00:00Z',
    updatedAt: '2023-11-09T14:00:00Z'
  },
  'task6': {
    id: 'task6',
    title: 'Modular app structure',
    description: 'Refactor the application to use a modular structure for better code organization and maintenance.',
    tag: 'Architecture',
    project: mockProjects[1],
    priority: 'High',
    dueDate: format(subDays(new Date(), 1), 'yyyy-MM-dd'),
    assignees: [mockUsers[2], mockUsers[3]],
    comments: [
      {
        id: 'comment2',
        user: mockUsers[1],
        text: 'Let\'s use the feature-based approach we discussed in the meeting.',
        timestamp: '2023-11-08T16:45:00Z'
      }
    ],
    attachments: [],
    timeLogs: [
      {
        id: 'log2',
        user: mockUsers[2],
        duration: '4h 30m',
        timestamp: '2023-11-09T09:00:00Z',
        description: 'Initial refactoring'
      }
    ],
    createdAt: '2023-11-08T15:00:00Z',
    updatedAt: '2023-11-09T10:00:00Z'
  },
  'task7': {
    id: 'task7',
    title: 'Activity heatmap component',
    description: 'Create a reusable heatmap component to visualize user activity over time.',
    tag: 'Development',
    project: mockProjects[0],
    priority: 'Medium',
    dueDate: format(addDays(new Date(), 3), 'yyyy-MM-dd'),
    assignees: [mockUsers[0], mockUsers[2]],
    comments: [],
    attachments: [
      {
        id: 'attach2',
        name: 'heatmap_design.fig',
        type: 'document',
        url: '#',
        size: '1.8 MB'
      }
    ],
    timeLogs: [],
    createdAt: '2023-11-09T15:00:00Z',
    updatedAt: '2023-11-09T15:00:00Z'
  },
  'task8': {
    id: 'task8',
    title: 'Dark theme optimization',
    description: 'Optimize the dark theme implementation to ensure consistent colors and contrasts.',
    tag: 'Design',
    project: mockProjects[0],
    priority: 'Medium',
    dueDate: format(addDays(new Date(), 4), 'yyyy-MM-dd'),
    assignees: [mockUsers[0]],
    comments: [],
    attachments: [],
    timeLogs: [],
    createdAt: '2023-11-09T16:00:00Z',
    updatedAt: '2023-11-09T16:00:00Z'
  },
  'task9': {
    id: 'task9',
    title: 'Initial project setup',
    description: 'Set up the initial project structure and dependencies.',
    tag: 'Setup',
    project: mockProjects[2],
    priority: 'High',
    dueDate: format(subDays(new Date(), 5), 'yyyy-MM-dd'),
    assignees: [mockUsers[2]],
    comments: [],
    attachments: [],
    timeLogs: [
      {
        id: 'log3',
        user: mockUsers[2],
        duration: '3h 45m',
        timestamp: '2023-11-05T13:00:00Z',
        description: 'Project initialization'
      }
    ],
    createdAt: '2023-11-05T10:00:00Z',
    updatedAt: '2023-11-05T14:00:00Z'
  },
  'task10': {
    id: 'task10',
    title: 'Design system tokens',
    description: 'Create a set of design system tokens for consistent styling across the application.',
    tag: 'Design',
    project: mockProjects[2],
    priority: 'Medium',
    dueDate: format(subDays(new Date(), 3), 'yyyy-MM-dd'),
    assignees: [mockUsers[0]],
    comments: [],
    attachments: [],
    timeLogs: [],
    createdAt: '2023-11-06T10:00:00Z',
    updatedAt: '2023-11-06T10:00:00Z'
  },
  'task11': {
    id: 'task11',
    title: 'Basic layout structure',
    description: 'Create the basic layout structure for the application.',
    tag: 'Development',
    project: mockProjects[2],
    priority: 'High',
    dueDate: format(subDays(new Date(), 2), 'yyyy-MM-dd'),
    assignees: [mockUsers[3]],
    comments: [],
    attachments: [],
    timeLogs: [],
    createdAt: '2023-11-07T10:00:00Z',
    updatedAt: '2023-11-07T10:00:00Z'
  },
  'task12': {
    id: 'task12',
    title: 'Time tracking widget',
    description: 'Implement a widget for tracking time spent on tasks.',
    tag: 'Feature',
    project: mockProjects[3],
    priority: 'Medium',
    dueDate: format(subDays(new Date(), 1), 'yyyy-MM-dd'),
    assignees: [mockUsers[2]],
    comments: [],
    attachments: [],
    timeLogs: [],
    createdAt: '2023-11-08T10:00:00Z',
    updatedAt: '2023-11-08T10:00:00Z'
  }
};

export const mockBoards: Board[] = [
  {
    id: 'board1',
    title: 'Main Dashboard',
    columns: [
      {
        id: 'todo',
        title: 'To Do',
        taskIds: ['task1', 'task2', 'task3', 'task4', 'task5']
      },
      {
        id: 'inprogress',
        title: 'In Progress',
        taskIds: ['task6', 'task7', 'task8']
      },
      {
        id: 'done',
        title: 'Done',
        taskIds: ['task9', 'task10', 'task11', 'task12']
      }
    ]
  },
  {
    id: 'board2',
    title: 'Development Sprints',
    columns: [
      {
        id: 'backlog',
        title: 'Backlog',
        taskIds: ['task1', 'task3']
      },
      {
        id: 'todo',
        title: 'To Do',
        taskIds: ['task2', 'task4']
      },
      {
        id: 'inprogress',
        title: 'In Progress',
        taskIds: ['task6', 'task7']
      },
      {
        id: 'review',
        title: 'Code Review',
        taskIds: ['task8']
      },
      {
        id: 'done',
        title: 'Done',
        taskIds: ['task9', 'task11', 'task12']
      }
    ]
  },
  {
    id: 'board3',
    title: 'Design Tasks',
    columns: [
      {
        id: 'todo',
        title: 'To Do',
        taskIds: []
      },
      {
        id: 'inprogress',
        title: 'In Progress',
        taskIds: ['task1', 'task8']
      },
      {
        id: 'review',
        title: 'Review',
        taskIds: []
      },
      {
        id: 'done',
        title: 'Done',
        taskIds: ['task10']
      }
    ]
  }
];