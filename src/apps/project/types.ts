export interface User {
  id: string;
  name: string;
  email?: string;
  avatar: string;
  role: string;
  status?: 'active' | 'inactive' | 'busy';
}

export interface Project {
  id: string;
  name: string;
  color: string;
  icon?: string;
  description?: string;
  deadline?: string; // ISO date string
  createdAt?: string; // ISO date string
  updatedAt?: string; // ISO date string
}

export interface ProjectTodo {
  id: string;
  title: string;
  completed: boolean;
  icon?: string;
  dueDate?: string; // ISO date string
  startDate?: string; // ISO date string
  createdAt: string; // ISO date string
  assignees?: User[];
  projectId: string;
}

export interface ProjectDocument {
  id: string;
  name: string;
  type: string; // pdf, doc, xls, etc.
  size: string;
  url: string;
  createdAt: string; // ISO date string
  createdBy: User;
  projectId: string;
}

export interface ProjectLink {
  title: string;
  url: string;
  projectId: string;
}

export interface AIReport {
  id: string;
  title: string;
  summary: string;
  date: string; // ISO date string
  projectId: string;
  content?: string;
  isNew?: boolean;
}

export interface Comment {
  id: string;
  user: User;
  text: string;
  timestamp: string;
}

export interface Attachment {
  id: string;
  name: string;
  url?: string;
  type: 'image' | 'document' | 'other';
  size: string;
  thumbnailUrl?: string;
}

export interface TimeLog {
  id: string;
  user: User;
  duration: string;
  timestamp: string; // ISO date string
  notes?: string;
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  status: 'todo' | 'inprogress' | 'review' | 'done';
  priority: 'High' | 'Medium' | 'Low';
  dueDate?: string; // ISO date string
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  createdBy: User;
  assignees: User[];
  tag: string;
  project: Project;
  attachments: Array<{
    id: string;
    name: string;
    type: string;
    // ... other attachment properties
  }>;
  comments: Array<{
    id: string;
    text: string;
    user: User;
    // ... other comment properties
  }>;
  timeLogs?: TimeLog[];
  chatMessages?: ChatMessage[];
}

export interface KanbanColumn {
  id: string;
  title: string;
  taskIds: string[];
}

export interface Board {
  id: string;
  title: string;
  columns: KanbanColumn[];
}

export interface ChatMessage {
  id: string;
  user: User;
  content: string;
  timestamp: string; // ISO date string
  attachments?: Attachment[];
  replyTo?: ChatMessage;
}