import React, { useState } from "react";
    import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>b, <PERSON><PERSON><PERSON>, Badge } from "@heroui/react";
    import { Icon } from "@iconify/react";

    interface WeeklyReport {
      id: string;
      weekRange: string;
      summary: string;
      insights: string[];
      productivity: {
        score: number;
        change: number;
      };
      focusTime: {
        hours: number;
        change: number;
      };
      completedTasks: {
        count: number;
        change: number;
      };
      recommendations: string[];
      detailedAnalysis: string;
    }

    // Mock data for weekly reports
    const weeklyReports: WeeklyReport[] = [
      {
        id: "current",
        weekRange: `${new Date(new Date().setDate(new Date().getDate() - new Date().getDay())).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${new Date(new Date().setDate(new Date().getDate() + (6 - new Date().getDay()))).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`,
        summary: "You've shown exceptional productivity this week, completing tasks 23% faster than your average. Your focus peaks between 10am-12pm, suggesting this is your optimal work window.",
        insights: [
          "You spent 42% more time on development tasks compared to last week",
          "Your most productive day was Wednesday with 8.5 hours of focused work",
          "You took fewer breaks this week, which may impact long-term productivity"
        ],
        productivity: {
          score: 8.7,
          change: 1.2
        },
        focusTime: {
          hours: 32,
          change: 4
        },
        completedTasks: {
          count: 24,
          change: 7
        },
        recommendations: [
          "Consider scheduling more complex tasks during your 10am-12pm productivity peak",
          "Taking structured breaks (5 minutes every hour) could improve overall focus",
          "Your progress on Project Alpha is ahead of schedule - consider allocating some time to Project Beta which is falling behind"
        ],
        detailedAnalysis: "This week shows a significant improvement in your focus metrics. You maintained deep work sessions averaging 47 minutes (up from 32 minutes last week), which correlates with your increased task completion rate. Time spent in meetings decreased by 15%, allowing for more dedicated development time. Your task switching rate has improved, with fewer context switches per hour (2.3 down from 3.8).\n\nYour work pattern indicates a strong preference for morning productivity, with 68% of your high-focus work occurring before 2pm. This suggests scheduling your most demanding tasks in the morning would optimize your natural productivity cycle.\n\nWhile overall metrics improved, we noticed slightly longer work hours (+1.2h/day on average), which may not be sustainable long-term without adequate breaks. Your current work-rest ratio is 9:1, while research suggests 5:1 is optimal for knowledge workers."
      },
      {
        id: "prev-1",
        weekRange: `${new Date(new Date().setDate(new Date().getDate() - new Date().getDay() - 7)).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${new Date(new Date().setDate(new Date().getDate() - new Date().getDay() - 1)).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`,
        summary: "Your consistency has been impressive this week, with steady daily progress. However, frequent context switching may be impacting your deep work capabilities.",
        insights: [
          "Meeting time increased by 23% this week, reducing available focused work time",
          "Task completion rate decreased slightly compared to your monthly average",
          "Your most productive hours shifted to afternoons (2pm-4pm)"
        ],
        productivity: {
          score: 7.5,
          change: -0.3
        },
        focusTime: {
          hours: 28,
          change: -2
        },
        completedTasks: {
          count: 17,
          change: -3
        },
        recommendations: [
          "Consider batching meetings to specific days or time blocks",
          "Your context switching rate is high - try using the Pomodoro technique",
          "Schedule more challenging tasks during your afternoon productivity peak"
        ],
        detailedAnalysis: "This week showed a slight decline in overall productivity metrics, primarily driven by an increase in meeting time and context switching. Your average deep work session length decreased to 32 minutes (down from 38 minutes), and you experienced more interruptions per hour (average 4.2, up from 3.5).\n\nInterestingly, your productivity pattern has shifted, with your peak focus hours now occurring between 2pm-4pm rather than in the morning. This may be due to the increased morning meetings, forcing deep work into the afternoon slots.\n\nTask completion velocity decreased by 15% compared to your monthly average, though the complexity of tasks remained consistent. This suggests the interruptions and context switching are the primary factors affecting your output rather than task difficulty.\n\nWhile your overall hours remained consistent, the quality of those hours appears to have been impacted by fragmentation. Consider implementing stronger focus protection mechanisms."
      },
      {
        id: "prev-2",
        weekRange: `${new Date(new Date().setDate(new Date().getDate() - new Date().getDay() - 14)).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${new Date(new Date().setDate(new Date().getDate() - new Date().getDay() - 8)).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`,
        summary: "This was your most balanced week this month, with excellent work-rest ratios and consistent deep focus periods throughout each day.",
        insights: [
          "You maintained an optimal work-rest ratio of 5:1 throughout the week",
          "Task estimation accuracy improved to 92% (from 78% last week)",
          "Your collaboration time was highly productive with clear outcomes"
        ],
        productivity: {
          score: 7.8,
          change: 0.5
        },
        focusTime: {
          hours: 30,
          change: 2
        },
        completedTasks: {
          count: 20,
          change: 3
        },
        recommendations: [
          "Continue with the current balance of solo and collaborative work",
          "Your task estimation technique has improved - consider documenting your approach",
          "You might benefit from slightly longer deep work blocks (try 50min instead of 40min)"
        ],
        detailedAnalysis: "This week represents your most balanced performance this month. You achieved an excellent harmony between focused individual work and collaborative sessions, with clear boundaries between different modes of work.\n\nYour deep work sessions averaged 38 minutes, with an optimal frequency of breaks (approximately every 40-45 minutes). These structured breaks appear to have enhanced overall productivity rather than diminishing it.\n\nTask estimation accuracy showed remarkable improvement at 92%, which suggests your planning capabilities are developing well. This improved planning appears to have reduced stress levels (as indicated by reduced time pressure markers in your work pattern).\n\nYour collaborative sessions were highly focused with clear outcomes, showing a 27% improvement in meeting efficiency compared to previous weeks. Consider documenting what made these interactions so effective for future reference.\n\nOverall, this week provides an excellent template for sustainable high performance that you may want to replicate in future weeks."
      }
    ];

    export const WeeklyAIReport: React.FC = () => {
      const [selectedKey, setSelectedKey] = useState("current");
      
      const currentReport = weeklyReports.find(report => report.id === selectedKey) || weeklyReports[0];
      
      return (
        <div>
          <Tabs 
            aria-label="Weekly Reports" 
            selectedKey={selectedKey} 
            onSelectionChange={setSelectedKey}
            color="primary"
            variant="underlined"
            classNames={{
              tabList: "gap-6",
              cursor: "bg-primary",
              tab: "text-white",
            }}
          >
            {weeklyReports.map((report) => (
              <Tab 
                key={report.id} 
                title={
                  <div className="flex items-center gap-2">
                    {report.id === "current" && (
                      <Badge color="primary" content="" placement="top-right">
                        <Icon icon="lucide:calendar" />
                      </Badge>
                    )}
                    <span>{report.id === "current" ? "Current Week" : `Previous Week ${report.id.split("-")[1]}`}</span>
                  </div>
                }
              >
                <Card className="bg-custom-card border-custom-border shadow-none">
                  <CardBody className="p-6">
                    <div className="mb-6">
                      <h3 className="text-lg text-white font-medium mb-1">
                        Week of {currentReport.weekRange}
                      </h3>
                      <p className="text-custom-muted">
                        <Icon icon="lucide:sparkles" className="inline-block mr-1 text-primary" />
                        AI-generated performance analysis
                      </p>
                    </div>
                    
                    <div className="mb-6">
                      <h4 className="text-md text-white font-medium mb-3">Summary</h4>
                      <div className="border-l-2 border-primary pl-4 py-1 mb-4">
                        <p className="text-white">
                          {currentReport.summary}
                        </p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      <div className="bg-custom-sidebar rounded-md p-4">
                        <div className="text-custom-muted text-xs mb-1">Productivity Score</div>
                        <div className="flex items-end gap-2">
                          <span className="text-white text-2xl font-semibold">{currentReport.productivity.score.toFixed(1)}</span>
                          <span className={`text-${currentReport.productivity.change >= 0 ? 'success' : 'danger'} text-sm mb-1 flex items-center`}>
                            <Icon icon={currentReport.productivity.change >= 0 ? "lucide:trend-up" : "lucide:trend-down"} className="mr-1" />
                            {currentReport.productivity.change >= 0 ? '+' : ''}{currentReport.productivity.change.toFixed(1)}
                          </span>
                        </div>
                      </div>
                      
                      <div className="bg-custom-sidebar rounded-md p-4">
                        <div className="text-custom-muted text-xs mb-1">Focus Time</div>
                        <div className="flex items-end gap-2">
                          <span className="text-white text-2xl font-semibold">{currentReport.focusTime.hours}h</span>
                          <span className={`text-${currentReport.focusTime.change >= 0 ? 'success' : 'danger'} text-sm mb-1 flex items-center`}>
                            <Icon icon={currentReport.focusTime.change >= 0 ? "lucide:trend-up" : "lucide:trend-down"} className="mr-1" />
                            {currentReport.focusTime.change >= 0 ? '+' : ''}{currentReport.focusTime.change}h
                          </span>
                        </div>
                      </div>
                      
                      <div className="bg-custom-sidebar rounded-md p-4">
                        <div className="text-custom-muted text-xs mb-1">Completed Tasks</div>
                        <div className="flex items-end gap-2">
                          <span className="text-white text-2xl font-semibold">{currentReport.completedTasks.count}</span>
                          <span className={`text-${currentReport.completedTasks.change >= 0 ? 'success' : 'danger'} text-sm mb-1 flex items-center`}>
                            <Icon icon={currentReport.completedTasks.change >= 0 ? "lucide:trend-up" : "lucide:trend-down"} className="mr-1" />
                            {currentReport.completedTasks.change >= 0 ? '+' : ''}{currentReport.completedTasks.change}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="mb-6">
                      <h4 className="text-md text-white font-medium mb-3">Key Insights</h4>
                      <div className="space-y-2">
                        {currentReport.insights.map((insight, index) => (
                          <div key={index} className="flex items-start gap-3">
                            <div className="p-1 bg-primary/20 rounded-full mt-0.5">
                              <Icon icon="lucide:lightbulb" className="text-primary text-sm" />
                            </div>
                            <p className="text-custom-muted flex-1">{insight}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <Divider className="my-6 bg-custom-border" />
                    
                    <div className="mb-6">
                      <h4 className="text-md text-white font-medium mb-3">Recommendations</h4>
                      <div className="space-y-3">
                        {currentReport.recommendations.map((recommendation, index) => (
                          <div key={index} className="flex items-start gap-3">
                            <div className="p-1 bg-success/20 rounded-full mt-0.5">
                              <Icon icon="lucide:check" className="text-success text-sm" />
                            </div>
                            <p className="text-custom-muted flex-1">{recommendation}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-md text-white font-medium mb-3">Detailed Analysis</h4>
                      <div className="bg-custom-sidebar rounded-md p-4 text-custom-muted">
                        {currentReport.detailedAnalysis.split('\n\n').map((paragraph, index) => (
                          <p key={index} className={index > 0 ? 'mt-4' : ''}>{paragraph}</p>
                        ))}
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </Tab>
            ))}
          </Tabs>
        </div>
      );
    };