import React, { useState, useMemo } from "react";
import { <PERSON>, CardBody, Tooltip, Dropdown, DropdownTrigger, DropdownMenu, DropdownI<PERSON>, Button } from "@heroui/react";
import { Icon } from "@iconify/react";

interface HeatmapProps {
  days?: number;
  cellSize?: number;
  gap?: number;
  colors?: Record<number, string>;
  tooltip?: (date: string, duration: string) => React.ReactNode;
}

export const Heatmap: React.FC<HeatmapProps> = ({
  days = 30,
  cellSize = 10,
  gap = 2,
  colors = {
    0: '#2A2D3C',
    1: '#3E71D1',
    2: '#1B84FF',
    3: '#4799FF'
  },
  tooltip = (date, duration) => `${duration} – ${date}`
}) => {
  // Generate random data for demonstration
  const generateHeatmapData = () => {
    const data = [];
    const now = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(now.getDate() - i);
      
      // Random activity level (0-3)
      const level = Math.floor(Math.random() * 4);
      
      // Random duration between 0-8 hours
      const hours = level === 0 ? 0 : Math.floor(Math.random() * 8) + 1;
      const minutes = Math.floor(Math.random() * 60);
      
      data.push({
        date,
        level,
        duration: level === 0 ? '0h' : `${hours}h ${minutes}m`
      });
    }
    
    return data;
  };
  
  const [activeTooltip, setActiveTooltip] = useState<number | null>(null);
  
  const heatmapData = useMemo(() => generateHeatmapData(), [days]);
  
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      weekday: 'short',
      day: 'numeric', 
      month: 'short'
    });
  };
  
  return (
    <Card className="bg-custom-card border-custom-border shadow-none ">
      <CardBody className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-white text-lg font-medium">Activity Heatmap</h3>
        </div>
        
        <div className="overflow-x-auto pb-6">
          <div className="flex flex-wrap items-end gap-1" style={{ gap: `${gap}px` }}>
            {heatmapData.map((day, idx) => (
              <Tooltip
                key={idx}
                content={
                  <div className="px-2 py-1">
                    <div className="font-medium">{formatDate(day.date)}</div>
                    <div>{day.duration}</div>
                  </div>
                }
                isOpen={activeTooltip === idx}
                onOpenChange={(open) => {
                  if (open) setActiveTooltip(idx);
                  else if (activeTooltip === idx) setActiveTooltip(null);
                }}
                placement="top"
                classNames={{
                  base: 'bg-[#1E222B] text-white text-xs shadow-lg',
                  content: 'py-2 px-3 rounded-md',
                }}
              >
                <div
                  className="flex flex-col items-center"
                  onMouseEnter={() => setActiveTooltip(idx)}
                  onMouseLeave={() => setActiveTooltip(null)}
                >
                  <div
                    style={{
                      width: cellSize,
                      height: cellSize,
                      backgroundColor: colors[day.level] || colors[0],
                      borderRadius: '2px',
                    }}
                    className="transition-colors duration-200 hover:opacity-80 cursor-pointer"
                  />
                </div>
              </Tooltip>
            ))}
          </div>
        </div>
        
      </CardBody>
    </Card>
    
  );
};