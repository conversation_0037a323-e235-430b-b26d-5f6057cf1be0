import React from "react";
import { Card, CardBody, Tooltip } from "@heroui/react";
import { Icon } from "@iconify/react";

type DayData = {
  name: string;
  hours: number;
  color: string;
};

export const WeeklyOverview: React.FC = () => {
  const weekData: DayData[] = [
    { name: "<PERSON>", hours: 7.5, color: "#1B84FF" },
    { name: "<PERSON><PERSON>", hours: 5.8, color: "#1B84FF" },
    { name: "Wed", hours: 8.2, color: "#1B84FF" },
    { name: "Thu", hours: 6.5, color: "#1B84FF" },
    { name: "Fri", hours: 7.0, color: "#1B84FF" },
    { name: "Sa<PERSON>", hours: 2.0, color: "#7D8597" },
    { name: "<PERSON>", hours: 0, color: "#7D8597" },
  ];

  const maxHours = Math.max(...weekData.map(day => day.hours));
  
  // Function to calculate bar height as percentage of max hours
  const getBarHeight = (hours: number): string => {
    const minHeight = 5; // Minimum height in percentage for visibility
    if (hours === 0) return `${minHeight}%`;
    return `${Math.max(minHeight, (hours / maxHours) * 100)}%`;
  };

  return (
    <div className="h-64">
      <div className="flex items-end justify-between h-full">
        {weekData.map((day, index) => (
          <div key={index} className="flex flex-col items-center w-full">
            <Tooltip
              content={
                <div className="py-1 px-2">
                  <div className="font-medium">{day.name}</div>
                  <div>{day.hours} hours</div>
                </div>
              }
              placement="top"
            >
              <div className="flex flex-col items-center w-full">
                <div 
                  className="w-4/5 rounded-t-sm transition-all duration-200 hover:opacity-80"
                  style={{ 
                    height: getBarHeight(day.hours), 
                    backgroundColor: day.color,
                  }}
                />
                <div className="text-xs text-custom-muted mt-2">{day.name}</div>
              </div>
            </Tooltip>
          </div>
        ))}
      </div>
    </div>
  );
};