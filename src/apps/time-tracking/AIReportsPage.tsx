import React, { useState } from "react";
    import { 
      <PERSON>, 
      <PERSON><PERSON><PERSON>, 
      <PERSON><PERSON>, 
      <PERSON>, 
      <PERSON><PERSON><PERSON>, 
      <PERSON><PERSON>, 
      <PERSON><PERSON><PERSON><PERSON><PERSON>, 
      <PERSON><PERSON><PERSON><PERSON><PERSON>, 
      <PERSON><PERSON><PERSON><PERSON>, 
      <PERSON><PERSON><PERSON><PERSON>er, 
      useDisclosure,
      Dropdown,
      DropdownTrigger,
      DropdownMenu,
      DropdownItem,
      Badge,
      Avatar,
      Tabs,
      Tab,
      Pagination
    } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import { AIReportModal } from "./AIReportModal";

    interface AIReport {
      id: string;
      title: string;
      week: string;
      weekRange: string;
      date: string;
      summary: string;
      type: "weekly" | "monthly" | "quarterly";
      productivityScore: number;
      productivityChange: number;
      status: "new" | "read";
      insights: string[];
    }

    const mockReports: AIReport[] = [
      {
        id: "1",
        title: "Weekly Performance Analysis",
        week: "Current Week",
        weekRange: `${new Date(new Date().setDate(new Date().getDate() - new Date().getDay())).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${new Date(new Date().setDate(new Date().getDate() + (6 - new Date().getDay()))).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`,
        date: new Date().toISOString(),
        summary: "You've shown exceptional productivity this week, completing tasks 23% faster than your average. Your focus peaks between 10am-12pm, suggesting this is your optimal work window.",
        type: "weekly",
        productivityScore: 8.7,
        productivityChange: 1.2,
        status: "new",
        insights: [
          "You spent 42% more time on development tasks compared to last week",
          "Your most productive day was Wednesday with 8.5 hours of focused work",
          "You took fewer breaks this week, which may impact long-term productivity"
        ]
      },
      {
        id: "2",
        title: "Weekly Performance Analysis",
        week: "Previous Week",
        weekRange: `${new Date(new Date().setDate(new Date().getDate() - new Date().getDay() - 7)).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${new Date(new Date().setDate(new Date().getDate() - new Date().getDay() - 1)).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`,
        date: new Date(new Date().setDate(new Date().getDate() - 7)).toISOString(),
        summary: "Your consistency has been impressive this week, with steady daily progress. However, frequent context switching may be impacting your deep work capabilities.",
        type: "weekly",
        productivityScore: 7.5,
        productivityChange: -0.3,
        status: "read",
        insights: [
          "Meeting time increased by 23% this week, reducing available focused work time",
          "Task completion rate decreased slightly compared to your monthly average",
          "Your most productive hours shifted to afternoons (2pm-4pm)"
        ]
      },
      {
        id: "3",
        title: "Weekly Performance Analysis",
        week: "Two Weeks Ago",
        weekRange: `${new Date(new Date().setDate(new Date().getDate() - new Date().getDay() - 14)).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${new Date(new Date().setDate(new Date().getDate() - new Date().getDay() - 8)).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`,
        date: new Date(new Date().setDate(new Date().getDate() - 14)).toISOString(),
        summary: "This was your most balanced week this month, with excellent work-rest ratios and consistent deep focus periods throughout each day.",
        type: "weekly",
        productivityScore: 7.8,
        productivityChange: 0.5,
        status: "read",
        insights: [
          "You maintained an optimal work-rest ratio of 5:1 throughout the week",
          "Task estimation accuracy improved to 92% (from 78% last week)",
          "Your collaboration time was highly productive with clear outcomes"
        ]
      },
      {
        id: "4",
        title: "Monthly Performance Overview",
        week: "October 2023",
        weekRange: "Oct 1 - Oct 31",
        date: new Date(new Date().setDate(new Date().getDate() - 30)).toISOString(),
        summary: "October was your most productive month this quarter, with significant improvements in task estimation and project delivery timeliness.",
        type: "monthly",
        productivityScore: 8.2,
        productivityChange: 0.8,
        status: "read",
        insights: [
          "You completed 43 tasks this month, a 15% increase from last month",
          "Your average focus session duration increased to 52 minutes",
          "You spent 18% less time in meetings compared to September"
        ]
      },
      {
        id: "5",
        title: "Quarterly Performance Review",
        week: "Q3 2023",
        weekRange: "Jul 1 - Sep 30",
        date: new Date(new Date().setDate(new Date().getDate() - 60)).toISOString(),
        summary: "Q3 showed consistent growth in your productivity metrics, with September being your strongest month. Your project leadership skills have notably improved.",
        type: "quarterly",
        productivityScore: 7.9,
        productivityChange: 1.0,
        status: "read",
        insights: [
          "You led 3 major project completions this quarter",
          "Cross-team collaboration increased by 27% compared to Q2",
          "Your technical documentation contributions received high praise"
        ]
      }
    ];

    export const AIReportsPage: React.FC = () => {
      const [activeType, setActiveType] = useState<"all" | "weekly" | "monthly" | "quarterly">("all");
      const [selectedReport, setSelectedReport] = useState<AIReport | null>(null);
      const { isOpen, onOpen, onOpenChange } = useDisclosure();
      
      const filteredReports = activeType === "all" 
        ? mockReports 
        : mockReports.filter(report => report.type === activeType);
      
      const handleReportClick = (report: AIReport) => {
        setSelectedReport(report);
        onOpen();
      };
      
      const getChipColor = (type: string) => {
        switch (type) {
          case "weekly": return "primary";
          case "monthly": return "secondary";
          case "quarterly": return "success";
          default: return "default";
        }
      };

      return (
        <div className="p-6 max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-semibold text-white mb-1">AI Reports</h1>
              <p className="text-custom-muted">
                AI-generated insights on your performance and productivity
              </p>
            </div>
            <div className="flex gap-2">
              <Button 
                variant="flat" 
                color="default" 
                className={activeType === "all" ? "bg-custom-sidebar text-white" : "text-custom-muted"}
                onPress={() => setActiveType("all")}
              >
                All
              </Button>
              <Button 
                variant="flat" 
                color="default" 
                className={activeType === "weekly" ? "bg-custom-sidebar text-white" : "text-custom-muted"}
                onPress={() => setActiveType("weekly")}
              >
                Weekly
              </Button>
              <Button 
                variant="flat" 
                color="default" 
                className={activeType === "monthly" ? "bg-custom-sidebar text-white" : "text-custom-muted"}
                onPress={() => setActiveType("monthly")}
              >
                Monthly
              </Button>
              <Button 
                variant="flat" 
                color="default" 
                className={activeType === "quarterly" ? "bg-custom-sidebar text-white" : "text-custom-muted"}
                onPress={() => setActiveType("quarterly")}
              >
                Quarterly
              </Button>
            </div>
          </div>
          
          <div className="space-y-4">
            {filteredReports.map(report => (
              <Card 
                isPressable
                key={report.id} 
                className="bg-custom-card border-custom-border shadow-none cursor-pointer hover:bg-custom-sidebar transition-colors"
                onPress={() => handleReportClick(report)}
              >
                <CardBody className="p-5">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center gap-3">
                        <Icon icon="lucide:sparkles" className="text-primary text-lg" />
                        <h3 className="text-white text-lg font-medium">{report.title}</h3>
                        {report.status === "new" && (
                          <Badge color="primary" className="ml-2" size="sm">New</Badge>
                        )}
                      </div>
                      
                      <div className="mt-2 flex items-center gap-2">
                        <Chip 
                          size="sm" 
                          color={getChipColor(report.type)} 
                          variant="flat"
                          className="capitalize"
                        >
                          {report.type}
                        </Chip>
                        <span className="text-custom-muted text-sm">{report.weekRange}</span>
                      </div>
                      
                      <p className="text-custom-body mt-3 line-clamp-2">{report.summary}</p>
                      
                      <div className="mt-4 flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <div className="p-1 rounded-full bg-custom-background">
                            <Icon icon="lucide:trending-up" className="text-primary text-sm" />
                          </div>
                          <div>
                            <div className="text-xs text-custom-muted">Productivity</div>
                            <div className="text-white text-md font-medium">{report.productivityScore.toFixed(1)}</div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <div className="p-1 rounded-full bg-custom-background">
                            <Icon icon="lucide:lightbulb" className="text-primary text-sm" />
                          </div>
                          <div>
                            <div className="text-xs text-custom-muted">Insights</div>
                            <div className="text-white text-md font-medium">{report.insights.length}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <Button 
                      isIconOnly 
                      variant="light" 
                      className="bg-transparent hover:bg-custom-background text-custom-muted"
                      onPress={(e) => {
                        e.stopPropagation();
                        handleReportClick(report);
                      }}
                    >
                      <Icon icon="lucide:chevron-right" />
                    </Button>
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>
          
          {/* AI Report Modal */}
          {selectedReport && (
            <AIReportModal 
              report={selectedReport}
              isOpen={isOpen}
              onOpenChange={onOpenChange}
            />
          )}

          <Pagination isCompact showControls initialPage={1} total={10} className="mt-6" />
        </div>
      );
    };