import React, { useState, useMemo, useEffect } from "react";
import { 
  Card, 
  CardBody, 
  Button, 
  Input,
  Select,
  SelectItem,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Checkbox,
  Chip,
  Badge,
  Tooltip,
  Avatar
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { parseDate, getLocalTimeZone, CalendarDate } from "@internationalized/date";
import { DateRangePicker } from "@heroui/react";
import type { RangeValue } from "@heroui/react";
import { useTimeTracking } from "../../context/TimeTrackingContext";
import {Pagination} from "@heroui/react";
// Enhanced TimeEntry interface with income calculation
interface EnhancedTimeEntry {
  id: string;
  startTime: Date;
  endTime: Date;
  duration: string;
  project: string;
  task: string;
  notes: string;
  user?: {
    id: string;
    name: string;
    avatar: string;
    role?: string;
    hourlyRate?: number; // Optional hourly rate for income calculation
  };
  hourlyRate?: number; // Optional task-specific hourly rate
  calculatedIncome?: number; // Calculated income based on duration and rate
}

export const TimeTrackingReportsPage: React.FC = () => {
  const { timeLogs } = useTimeTracking();
  
  // Determine initial date range (earliest log -> today)
  const initialDateRange = React.useMemo<RangeValue<CalendarDate>>(() => {
    if (timeLogs.length === 0) {
      const oneYearAgo = new Date();
      oneYearAgo.setDate(oneYearAgo.getDate() - 365);
      return {
        start: parseDate(oneYearAgo.toISOString().split('T')[0]),
        end: parseDate(new Date().toISOString().split('T')[0])
      };
    }
    const earliest = timeLogs.reduce((min, log) => log.startTime < min ? log.startTime : min, timeLogs[0].startTime);
    return {
      start: parseDate(earliest.toISOString().split('T')[0]),
      end: parseDate(new Date().toISOString().split('T')[0])
    };
  }, [timeLogs]);

  // Enhanced filters
  const [dateRange, setDateRange] = useState<RangeValue<CalendarDate>>(initialDateRange);
  const [projectFilter, setProjectFilter] = useState<string>("all");
  const [taskFilter, setTaskFilter] = useState<string>("all");
  const [userFilter, setUserFilter] = useState<string>("all");
  const [minHours, setMinHours] = useState<string>("");
  const [maxHours, setMaxHours] = useState<string>("");
  const [hasNotes, setHasNotes] = useState<boolean>(false);
  const [hasIncome, setHasIncome] = useState<boolean>(false);
  const [sortBy, setSortBy] = useState<string>("date");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [showFilters, setShowFilters] = useState<boolean>(false);
  
  // For summary widgets
  const [summaryData, setSummaryData] = useState({
    totalHours: 0,
    totalIncome: 0,
    mostActiveDay: { date: "", hours: 0 },
    mostProductiveTask: { task: "", hours: 0 }
  });

  // Get unique projects for filter dropdown
  const projects = useMemo(() => {
    const uniqueProjects = new Set<string>();
    timeLogs.forEach(log => {
      if (log.project) uniqueProjects.add(log.project);
    });
    return ["all", ...Array.from(uniqueProjects)];
  }, [timeLogs]);

  // Get unique tasks for filter dropdown
  const tasks = useMemo(() => {
    const uniqueTasks = new Set<string>();
    timeLogs.forEach(log => {
      if (log.task) uniqueTasks.add(log.task);
    });
    return ["all", ...Array.from(uniqueTasks)];
  }, [timeLogs]);

  // Get unique users for filter dropdown
  const users = useMemo(() => {
    const uniqueUsers = new Set<string>();
    timeLogs.forEach(log => {
      if (log.user?.name) uniqueUsers.add(log.user.name);
    });
    return ["all", ...Array.from(uniqueUsers)];
  }, [timeLogs]);

  // Calculate income for each time entry
  const enhancedLogs: EnhancedTimeEntry[] = useMemo(() => {
    return timeLogs.map(log => {
      // In a real app, you would get the rate from the user profile or project settings
      const hourlyRate = log.user?.hourlyRate || 50; // Default $50 per hour
      
      // Calculate duration in hours
      const [hours, minutes, seconds] = log.duration.split(':').map(Number);
      const durationHours = hours + (minutes / 60) + (seconds / 3600);
      
      // Calculate income
      const calculatedIncome = Math.round(durationHours * hourlyRate * 100) / 100;
      
      return {
        ...log,
        hourlyRate,
        calculatedIncome
      };
    });
  }, [timeLogs]);

  // Filter and sort logs
  const filteredLogs = useMemo(() => {
    let filtered = [...enhancedLogs];
    
    // Apply date range filter
    if (dateRange) {
      const startDate = new Date(dateRange.start.year, dateRange.start.month - 1, dateRange.start.day);
      const endDate = new Date(dateRange.end.year, dateRange.end.month - 1, dateRange.end.day);
      endDate.setHours(23, 59, 59, 999); // Include all entries from end date
      
      filtered = filtered.filter(log => {
        const logDate = new Date(log.startTime);
        return logDate >= startDate && logDate <= endDate;
      });
    }
    
    // Apply project filter
    if (projectFilter !== "all") {
      filtered = filtered.filter(log => log.project === projectFilter);
    }
    
    // Apply task filter
    if (taskFilter !== "all") {
      filtered = filtered.filter(log => log.task === taskFilter);
    }
    
    // Apply user filter
    if (userFilter !== "all") {
      filtered = filtered.filter(log => log.user?.name === userFilter);
    }
    
    // Apply min hours filter
    if (minHours) {
      const minHoursValue = parseFloat(minHours);
      filtered = filtered.filter(log => {
        const [h, m, s] = log.duration.split(':').map(Number);
        const durationHours = h + (m / 60) + (s / 3600);
        return durationHours >= minHoursValue;
      });
    }
    
    // Apply max hours filter
    if (maxHours) {
      const maxHoursValue = parseFloat(maxHours);
      filtered = filtered.filter(log => {
        const [h, m, s] = log.duration.split(':').map(Number);
        const durationHours = h + (m / 60) + (s / 3600);
        return durationHours <= maxHoursValue;
      });
    }
    
    // Apply has notes filter
    if (hasNotes) {
      filtered = filtered.filter(log => log.notes && log.notes.trim() !== '');
    }
    
    // Apply has income filter
    if (hasIncome) {
      filtered = filtered.filter(log => log.calculatedIncome && log.calculatedIncome > 0);
    }
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(log => 
        log.task.toLowerCase().includes(query) ||
        log.notes.toLowerCase().includes(query) ||
        log.project.toLowerCase().includes(query) ||
        log.user?.name?.toLowerCase().includes(query)
      );
    }
    
    // Apply sorting
    const sortFunction = (a: EnhancedTimeEntry, b: EnhancedTimeEntry) => {
      let result = 0;
      
      switch (sortBy) {
        case "date":
          result = a.startTime.getTime() - b.startTime.getTime();
          break;
        case "project":
          result = a.project.localeCompare(b.project);
          break;
        case "task":
          result = a.task.localeCompare(b.task);
          break;
        case "duration":
          const getDurationSeconds = (duration: string) => {
            const [hours, minutes, seconds] = duration.split(":").map(Number);
            return (hours * 3600) + (minutes * 60) + seconds;
          };
          result = getDurationSeconds(a.duration) - getDurationSeconds(b.duration);
          break;
        case "income":
          result = (a.calculatedIncome || 0) - (b.calculatedIncome || 0);
          break;
        case "user":
          result = a.user?.name?.localeCompare(b.user?.name || "");
          break;
        default:
          break;
      }
      
      return sortDirection === "asc" ? result : -result;
    };
    
    return filtered.sort(sortFunction);
  }, [
    enhancedLogs, 
    dateRange, 
    projectFilter, 
    taskFilter, 
    userFilter, 
    minHours, 
    maxHours, 
    hasNotes, 
    hasIncome,
    sortBy, 
    sortDirection, 
    searchQuery
  ]);

  // Calculate summary data whenever filtered logs change
  useEffect(() => {
    // Calculate total hours
    const totalHours = filteredLogs.reduce((total, log) => {
      const [h, m, s] = log.duration.split(':').map(Number);
      return total + h + (m / 60) + (s / 3600);
    }, 0);
    
    // Calculate total income
    const totalIncome = filteredLogs.reduce((total, log) => 
      total + (log.calculatedIncome || 0), 0);
    
    // Find most active day
    const dayHours: Record<string, number> = {};
    filteredLogs.forEach(log => {
      const dateStr = log.startTime.toISOString().split('T')[0];
      const [h, m, s] = log.duration.split(':').map(Number);
      const hours = h + (m / 60) + (s / 3600);
      
      dayHours[dateStr] = (dayHours[dateStr] || 0) + hours;
    });
    
    let mostActiveDay = { date: "", hours: 0 };
    Object.entries(dayHours).forEach(([date, hours]) => {
      if (hours > mostActiveDay.hours) {
        mostActiveDay = { date, hours };
      }
    });
    
    // Find most productive task
    const taskHours: Record<string, number> = {};
    filteredLogs.forEach(log => {
      const [h, m, s] = log.duration.split(':').map(Number);
      const hours = h + (m / 60) + (s / 3600);
      
      taskHours[log.task] = (taskHours[log.task] || 0) + hours;
    });
    
    let mostProductiveTask = { task: "", hours: 0 };
    Object.entries(taskHours).forEach(([task, hours]) => {
      if (hours > mostProductiveTask.hours) {
        mostProductiveTask = { task, hours };
      }
    });
    
    setSummaryData({
      totalHours,
      totalIncome,
      mostActiveDay,
      mostProductiveTask
    });
  }, [filteredLogs]);

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString(undefined, { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric'
    });
  };

  // Format time for display
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit'
    });
  };

  // Format duration from "07:30:00" to "7h 30m" for display
  const formatDuration = (duration: string) => {
    const [hours, minutes] = duration.split(':').map(Number);
    return `${hours}h ${minutes.toString().padStart(2, '0')}m`;
  };

  const handleExport = () => {
    // In a real app, you would generate an Excel file here
    // For now, just show what data would be exported
    console.log("Exporting data to Excel:", filteredLogs);
    const fileName = `time-report-${new Date().toISOString().split('T')[0]}.xlsx`;
    alert(`Exporting ${filteredLogs.length} records to ${fileName}`);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Summary widgets */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card className="bg-custom-card border-custom-border shadow-none">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-custom-muted text-sm">Total Time Logged</p>
                <h3 className="text-white text-2xl font-semibold">
                  {Math.floor(summaryData.totalHours)}h {Math.round((summaryData.totalHours % 1) * 60)}m
                </h3>
              </div>
              <div className="p-3 rounded-full bg-primary/20">
                <Icon icon="lucide:clock" className="text-primary text-xl" />
              </div>
            </div>
          </CardBody>
        </Card>
        
        <Card className="bg-custom-card border-custom-border shadow-none">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-custom-muted text-sm">Total Income</p>
                <h3 className="text-white text-2xl font-semibold">
                  ${summaryData.totalIncome.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </h3>
              </div>
              <div className="p-3 rounded-full bg-success/20">
                <Icon icon="lucide:dollar-sign" className="text-success text-xl" />
              </div>
            </div>
          </CardBody>
        </Card>
        
        <Card className="bg-custom-card border-custom-border shadow-none">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-custom-muted text-sm">Most Active Day</p>
                <h3 className="text-white text-xl font-semibold truncate">
                  {summaryData.mostActiveDay.date ? (
                    <>
                      {new Date(summaryData.mostActiveDay.date).toLocaleDateString(undefined, { 
                        month: 'short', 
                        day: 'numeric' 
                      })}
                      <span className="text-custom-muted text-sm ml-2">
                        ({summaryData.mostActiveDay.hours.toFixed(1)}h)
                      </span>
                    </>
                  ) : (
                    "No data"
                  )}
                </h3>
              </div>
              <div className="p-3 rounded-full bg-warning/20">
                <Icon icon="lucide:calendar" className="text-warning text-xl" />
              </div>
            </div>
          </CardBody>
        </Card>
        
        <Card className="bg-custom-card border-custom-border shadow-none">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-custom-muted text-sm">Most Productive Task</p>
                <h3 className="text-white text-lg font-semibold truncate" style={{ maxWidth: '160px' }}>
                  {summaryData.mostProductiveTask.task ? (
                    <>
                      {summaryData.mostProductiveTask.task}
                      <span className="text-custom-muted text-sm ml-2">
                        ({summaryData.mostProductiveTask.hours.toFixed(1)}h)
                      </span>
                    </>
                  ) : (
                    "No data"
                  )}
                </h3>
              </div>
              <div className="p-3 rounded-full bg-primary/20">
                <Icon icon="lucide:check-circle" className="text-primary text-xl" />
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
      
      <Card className="bg-custom-card border-custom-border shadow-none mb-6">
        <CardBody>
          <div className="flex flex-col gap-4">
            {/* Primary filters */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-1">
                <DateRangePicker 
                  label="Date Range"
                  value={dateRange}
                  onChange={setDateRange}
                  className="w-full"
                  labelPlacement="outside-left"
                  classNames={{
                    base: "bg-custom-card border-custom-border"
                  }}
                />
              </div>
              
              <div className="flex items-center gap-2 md:col-span-2">
                <Input
                  placeholder="Search records..."
                  value={searchQuery}
                  onValueChange={setSearchQuery}
                  startContent={<Icon icon="lucide:search" className="text-custom-muted" />}
                  classNames={{
                    base: "flex-1",
                    inputWrapper: "bg-custom-sidebar border-custom-border",
                  }}
                />
                
                <Button 
                  variant="flat" 
                  startContent={<Icon icon="lucide:filter" />} 
                  className="bg-custom-sidebar"
                  onPress={() => setShowFilters(!showFilters)}
                >
                  Filter
                </Button>
                
                <Button 
                  color="primary"
                  startContent={<Icon icon="lucide:download" />}
                  onPress={handleExport}
                >
                  Export
                </Button>
              </div>
            </div>

            {showFilters && (
              <div className="grid grid-cols-2 gap-3 bg-custom-sidebar p-4 rounded-lg mt-4">
                {/* Project Select */}
                <Select
                  label="Project"
                  selectedKeys={[projectFilter] as any}
                  onSelectionChange={(keys) => {
                    const key = (keys as Set<string>).values().next().value;
                    setProjectFilter(key || "all");
                  }}
                  classNames={{
                    trigger: "bg-custom-card border-custom-border"
                  }}
                >
                  {projects.map((project) => (
                    <SelectItem key={project} textValue={project}>
                      {project === "all" ? "All Projects" : project}
                    </SelectItem>
                  ))}
                </Select>

                {/* Task Select */}
                <Select
                  label="Task"
                  selectedKeys={[taskFilter] as any}
                  onSelectionChange={(keys) => {
                    const key = (keys as Set<string>).values().next().value;
                    setTaskFilter(key || "all");
                  }}
                  classNames={{
                    trigger: "bg-custom-card border-custom-border"
                  }}
                >
                  {tasks.map((task) => (
                    <SelectItem key={task} textValue={task}>
                      {task === "all" ? "All Tasks" : task}
                    </SelectItem>
                  ))}
                </Select>

                {/* User Select */}
                <Select
                  label="User"
                  selectedKeys={[userFilter] as any}
                  onSelectionChange={(keys) => {
                    const key = (keys as Set<string>).values().next().value;
                    setUserFilter(key || "all");
                  }}
                  classNames={{
                    trigger: "bg-custom-card border-custom-border"
                  }}
                >
                  {users.map((user) => (
                    <SelectItem key={user} textValue={user}>
                      {user === "all" ? "All Users" : user}
                    </SelectItem>
                  ))}
                </Select>

                {/* Min / Max Hours */}
                <Input
                  type="number"
                  min="0"
                  step="0.5"
                  label="Min Hours"
                  placeholder="0"
                  value={minHours}
                  onValueChange={setMinHours}
                  classNames={{
                    inputWrapper: "bg-custom-card border-custom-border"
                  }}
                />
                <Input
                  type="number"
                  min="0"
                  step="0.5"
                  label="Max Hours"
                  placeholder="24"
                  value={maxHours}
                  onValueChange={setMaxHours}
                  classNames={{
                    inputWrapper: "bg-custom-card border-custom-border"
                  }}
                />

                {/* Checkboxes spanning 2 cols */}
                <div className="col-span-2 flex gap-4 mt-1">
                  <Checkbox isSelected={hasNotes} onValueChange={setHasNotes} color="primary">
                    Has Notes
                  </Checkbox>
                  <Checkbox isSelected={hasIncome} onValueChange={setHasIncome} color="primary">
                    Has Income
                  </Checkbox>
                </div>

                {/* Action buttons */}
                <Button 
                  color="danger" 
                  variant="flat" 
                  onPress={() => {
                    setProjectFilter("all");
                    setTaskFilter("all");
                    setUserFilter("all");
                    setMinHours("");
                    setMaxHours("");
                    setHasNotes(false);
                    setHasIncome(false);
                    setSearchQuery("");
                  }}
                >
                  Reset Filters
                </Button>
                <Button color="primary" onPress={() => setShowFilters(false)}>
                  Apply Filters
                </Button>
              </div>
            )}
          </div>
          
          {/* Table with report data */}
          <Table 
            aria-label="Time logs table"
            removeWrapper
            classNames={{
              base: "mt-6 overflow-x-auto",
              th: "bg-custom-card text-custom-muted border-b border-custom-border",
              td: "border-b border-custom-border py-3"
            }}
          >
            <TableHeader>
              <TableColumn 
                onClick={() => {
                  setSortBy("date");
                  setSortDirection(prev => sortBy === "date" ? (prev === "asc" ? "desc" : "asc") : "desc");
                }}
                className="cursor-pointer"
              >
                <div className="flex items-center gap-1">
                  DATE
                  {sortBy === "date" && (
                    <Icon icon={sortDirection === "asc" ? "lucide:arrow-up" : "lucide:arrow-down"} className="w-3 h-3" />
                  )}
                </div>
              </TableColumn>
              <TableColumn 
                onClick={() => {
                  setSortBy("project");
                  setSortDirection(prev => sortBy === "project" ? (prev === "asc" ? "desc" : "asc") : "asc");
                }}
                className="cursor-pointer"
              >
                <div className="flex items-center gap-1">
                  PROJECT
                  {sortBy === "project" && (
                    <Icon icon={sortDirection === "asc" ? "lucide:arrow-up" : "lucide:arrow-down"} className="w-3 h-3" />
                  )}
                </div>
              </TableColumn>
              <TableColumn 
                onClick={() => {
                  setSortBy("task");
                  setSortDirection(prev => sortBy === "task" ? (prev === "asc" ? "desc" : "asc") : "asc");
                }}
                className="cursor-pointer"
              >
                <div className="flex items-center gap-1">
                  TASK
                  {sortBy === "task" && (
                    <Icon icon={sortDirection === "asc" ? "lucide:arrow-up" : "lucide:arrow-down"} className="w-3 h-3" />
                  )}
                </div>
              </TableColumn>
              <TableColumn 
                onClick={() => {
                  setSortBy("duration");
                  setSortDirection(prev => sortBy === "duration" ? (prev === "asc" ? "desc" : "asc") : "desc");
                }}
                className="cursor-pointer"
              >
                <div className="flex items-center gap-1">
                  TIME LOGGED
                  {sortBy === "duration" && (
                    <Icon icon={sortDirection === "asc" ? "lucide:arrow-up" : "lucide:arrow-down"} className="w-3 h-3" />
                  )}
                </div>
              </TableColumn>
              <TableColumn>NOTES</TableColumn>
              <TableColumn 
                onClick={() => {
                  setSortBy("income");
                  setSortDirection(prev => sortBy === "income" ? (prev === "asc" ? "desc" : "asc") : "desc");
                }}
                className="cursor-pointer"
              >
                <div className="flex items-center gap-1">
                  INCOME
                  {sortBy === "income" && (
                    <Icon icon={sortDirection === "asc" ? "lucide:arrow-up" : "lucide:arrow-down"} className="w-3 h-3" />
                  )}
                </div>
              </TableColumn>

              <TableColumn>ACTIONS</TableColumn>
            </TableHeader>
            <TableBody emptyContent="No time logs found">
              {filteredLogs.map((log) => (
                <TableRow key={log.id}>
                  <TableCell>
                    <div className="flex flex-col">
                      <span className="font-medium text-sm">{formatDate(log.startTime)}</span>
                      <span className="text-xs text-custom-muted">
                        {formatTime(log.startTime)} - {formatTime(log.endTime)}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                      <span className="font-medium">{log.project}</span>
                    
                  </TableCell>
                  <TableCell>
                    <Tooltip content={log.task}>
                      <span className="truncate block max-w-[150px]">{log.task}</span>
                    </Tooltip>
                  </TableCell>
                  <TableCell>
                    <Chip
                      variant="flat" 
                      color="primary"
                      radius="sm"
                      className="bg-primary/10 border-none"
                    >
                      {formatDuration(log.duration)}
                    </Chip>
                  </TableCell>
                  <TableCell>
                    {log.notes ? (
                      <Tooltip content={log.notes}>
                        <span className="truncate block max-w-[120px]">{log.notes}</span>
                      </Tooltip>
                    ) : (
                      <span className="text-custom-muted text-xs">No notes</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">${log.calculatedIncome?.toFixed(2)}</span>
                    <div className="text-xs text-custom-muted">@${log.hourlyRate}/hr</div>
                  </TableCell>

                  <TableCell>
                    <div className="flex items-center gap-1">

                      <Dropdown>
                        <DropdownTrigger>
                          <Button isIconOnly size="sm" variant="light">
                            <Icon icon="lucide:more-vertical" className="text-custom-muted" />
                          </Button>
                        </DropdownTrigger>
                        <DropdownMenu 
                          aria-label="Time log actions"
                          className="bg-custom-card text-custom-text"
                        >
                          <DropdownItem key="edit" startContent={<Icon icon="lucide:edit-2" />}>Edit</DropdownItem>
                          <DropdownItem key="duplicate" startContent={<Icon icon="lucide:copy" />}>Duplicate</DropdownItem>
                          <DropdownItem key="export" startContent={<Icon icon="lucide:download" />}>Export</DropdownItem>
                          <DropdownItem 
                            key="delete" 
                            className="text-danger" 
                            color="danger"
                          >
                            Delete
                          </DropdownItem>
                        </DropdownMenu>
                      </Dropdown>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>
      <Pagination isCompact showControls initialPage={1} total={10} className="mb-4" />
      {/* Footer summary */}
      <Card className="bg-custom-card border-custom-border shadow-none">
        <CardBody className="py-3 px-4">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-3">
            <div className="text-custom-muted text-sm">
              Showing {filteredLogs.length} of {enhancedLogs.length} time logs
            </div>
            
            <div className="flex gap-6">
              <div className="flex flex-col">
                <span className="text-xs text-custom-muted">Total Time</span>
                <span className="text-sm text-white font-semibold">
                  {Math.floor(summaryData.totalHours)}h {Math.round((summaryData.totalHours % 1) * 60)}m
                </span>
              </div>
              
              <div className="flex flex-col">
                <span className="text-xs text-custom-muted">Total Income</span>
                <span className="text-sm text-white font-semibold">
                  ${summaryData.totalIncome.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </span>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};