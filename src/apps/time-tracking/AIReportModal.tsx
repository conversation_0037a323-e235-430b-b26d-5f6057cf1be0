import React, { useState } from "react";
    import { 
      <PERSON><PERSON>, 
      <PERSON><PERSON><PERSON><PERSON><PERSON>, 
      <PERSON><PERSON><PERSON><PERSON><PERSON>, 
      <PERSON><PERSON><PERSON><PERSON>, 
      <PERSON><PERSON><PERSON><PERSON><PERSON>, 
      <PERSON><PERSON>, 
      <PERSON><PERSON>,
      <PERSON><PERSON>,
      <PERSON>,
      Card<PERSON><PERSON>,
      <PERSON><PERSON><PERSON>,
      <PERSON><PERSON>,
      Chip
    } from "@heroui/react";
    import { Icon } from "@iconify/react";

    interface AIReport {
      id: string;
      title: string;
      week: string;
      weekRange: string;
      date: string;
      summary: string;
      type: "weekly" | "monthly" | "quarterly";
      productivityScore: number;
      productivityChange: number;
      status: "new" | "read";
      insights: string[];
    }

    interface AIReportModalProps {
      report: AIReport;
      isOpen: boolean;
      onOpenChange: (isOpen: boolean) => void;
    }

    export const AIReportModal: React.FC<AIReportModalProps> = ({
      report,
      isOpen,
      onOpenChange
    }) => {
      if (!isOpen) return null;
      const [activeTab, setActiveTab] = useState("overview");

      // Mock report detail data - in a real app this would come from an API
      const reportDetails = {
        summary: report.summary,
        insights: report.insights,
        recommendations: [
          "Consider scheduling more complex tasks during your 10am-12pm productivity peak",
          "Taking structured breaks (5 minutes every hour) could improve overall focus",
          "Your progress on Project Alpha is ahead of schedule - consider allocating some time to Project Beta which is falling behind"
        ],
        detailedAnalysis: "This week shows a significant improvement in your focus metrics. You maintained deep work sessions averaging 47 minutes (up from 32 minutes last week), which correlates with your increased task completion rate. Time spent in meetings decreased by 15%, allowing for more dedicated development time. Your task switching rate has improved, with fewer context switches per hour (2.3 down from 3.8).\n\nYour work pattern indicates a strong preference for morning productivity, with 68% of your high-focus work occurring before 2pm. This suggests scheduling your most demanding tasks in the morning would optimize your natural productivity cycle.\n\nWhile overall metrics improved, we noticed slightly longer work hours (+1.2h/day on average), which may not be sustainable long-term without adequate breaks. Your current work-rest ratio is 9:1, while research suggests 5:1 is optimal for knowledge workers.",
        metrics: {
          focusTime: {
            hours: 32,
            change: 4
          },
          completedTasks: {
            count: 24,
            change: 7
          },
          avgTaskTime: {
            minutes: 47,
            change: 15
          },
          meetingTime: {
            hours: 5.5,
            change: -1.2
          },
          contextSwitches: {
            count: 2.3,
            change: -1.5
          },
          workRestRatio: {
            ratio: "9:1",
            optimal: "5:1"
          }
        },
        charts: {
          focusHours: [
            {
              day: "Monday",
              hours: 6.5
            },
            {
              day: "Tuesday",
              hours: 7.2
            },
            {
              day: "Wednesday",
              hours: 8.5
            },
            {
              day: "Thursday",
              hours: 5.8
            },
            {
              day: "Friday",
              hours: 4.0
            }
          ],
          taskCategories: [
            {
              category: "Development",
              percentage: 65
            },
            {
              category: "Meetings",
              percentage: 15
            },
            {
              category: "Planning",
              percentage: 12
            },
            {
              category: "Documentation",
              percentage: 8
            }
          ]
        }
      };

      return (
        <Modal 
          isOpen={isOpen} 
          onOpenChange={onOpenChange}
          size="3xl"
          classNames={{
            base: "bg-custom-card",
            header: "border-b border-custom-border",
            body: "p-0",
            footer: "border-t border-custom-border",
          }}
        >
          <ModalContent className="max-h-[90vh] overflow-y-auto bg-custom-card">
            {(onClose) => (
              <>
                <ModalHeader className="flex flex-col gap-1 text-white">
                  <div className="flex items-center gap-2">
                    <Icon icon="lucide:sparkles" className="text-primary" />
                    <span>AI Performance Report</span>
                    <Chip 
                      size="sm" 
                      color={
                        report.type === "weekly" ? "primary" : 
                        report.type === "monthly" ? "secondary" : 
                        "success"
                      } 
                      variant="flat"
                      className="capitalize ml-2"
                    >
                      {report.type}
                    </Chip>
                  </div>
                  <p className="text-custom-muted text-sm mt-1">
                    {report.weekRange} • {report.week}
                  </p>
                </ModalHeader>
                
                <ModalBody>
                  <Tabs 
                    aria-label="Report Tabs" 
                    selectedKey={activeTab}
                    onSelectionChange={setActiveTab as any}
                    color="primary"
                    variant="underlined"
                    classNames={{
                      tabList: "px-6 pt-3",
                      cursor: "bg-primary",
                      tab: "text-white px-6",
                      panel: "p-0"
                    }}
                  >
                    <Tab 
                      key="overview" 
                      title={
                        <div className="flex items-center gap-1">
                          <Icon icon="lucide:layout-dashboard" />
                          <span>Overview</span>
                        </div>
                      }
                    >
                      <div className="p-6">
                        <div className="mb-6">
                          <h3 className="text-lg text-white font-medium mb-3">Summary</h3>
                          <div className="border-l-2 border-primary pl-4 py-1">
                            <p className="text-custom-body">
                              {reportDetails.summary}
                            </p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                          <Card className="bg-custom-sidebar border-custom-border shadow-none">
                            <CardBody className="p-4">
                              <div className="flex items-center justify-between">
                                <div>
                                  <p className="text-custom-muted text-xs mb-1">Productivity Score</p>
                                  <h3 className="text-white text-2xl font-semibold">
                                    {report.productivityScore.toFixed(1)}
                                  </h3>
                                </div>
                                <div className={`p-2 rounded-full ${report.productivityChange >= 0 ? 'bg-success/20' : 'bg-danger/20'}`}>
                                  <Icon 
                                    icon={report.productivityChange >= 0 ? "lucide:trending-up" : "lucide:trending-down"} 
                                    className={`text-xl ${report.productivityChange >= 0 ? 'text-success' : 'text-danger'}`} 
                                  />
                                </div>
                              </div>
                              <div className="mt-2">
                                <span className={`text-sm ${report.productivityChange >= 0 ? 'text-success' : 'text-danger'}`}>
                                  {report.productivityChange >= 0 ? '+' : ''}{report.productivityChange.toFixed(1)} from previous {report.type}
                                </span>
                              </div>
                            </CardBody>
                          </Card>
                          
                          <Card className="bg-custom-sidebar border-custom-border shadow-none">
                            <CardBody className="p-4">
                              <div className="flex items-center justify-between">
                                <div>
                                  <p className="text-custom-muted text-xs mb-1">Focus Time</p>
                                  <h3 className="text-white text-2xl font-semibold">
                                    {reportDetails.metrics.focusTime.hours}h
                                  </h3>
                                </div>
                                <div className={`p-2 rounded-full ${reportDetails.metrics.focusTime.change >= 0 ? 'bg-success/20' : 'bg-danger/20'}`}>
                                  <Icon 
                                    icon={reportDetails.metrics.focusTime.change >= 0 ? "lucide:clock" : "lucide:clock"} 
                                    className={`text-xl ${reportDetails.metrics.focusTime.change >= 0 ? 'text-success' : 'text-danger'}`} 
                                  />
                                </div>
                              </div>
                              <div className="mt-2">
                                <span className={`text-sm ${reportDetails.metrics.focusTime.change >= 0 ? 'text-success' : 'text-danger'}`}>
                                  {reportDetails.metrics.focusTime.change >= 0 ? '+' : ''}{reportDetails.metrics.focusTime.change}h from previous {report.type}
                                </span>
                              </div>
                            </CardBody>
                          </Card>
                          
                          <Card className="bg-custom-sidebar border-custom-border shadow-none">
                            <CardBody className="p-4">
                              <div className="flex items-center justify-between">
                                <div>
                                  <p className="text-custom-muted text-xs mb-1">Completed Tasks</p>
                                  <h3 className="text-white text-2xl font-semibold">
                                    {reportDetails.metrics.completedTasks.count}
                                  </h3>
                                </div>
                                <div className={`p-2 rounded-full ${reportDetails.metrics.completedTasks.change >= 0 ? 'bg-success/20' : 'bg-danger/20'}`}>
                                  <Icon 
                                    icon={reportDetails.metrics.completedTasks.change >= 0 ? "lucide:check-circle" : "lucide:alert-circle"} 
                                    className={`text-xl ${reportDetails.metrics.completedTasks.change >= 0 ? 'text-success' : 'text-danger'}`} 
                                  />
                                </div>
                              </div>
                              <div className="mt-2">
                                <span className={`text-sm ${reportDetails.metrics.completedTasks.change >= 0 ? 'text-success' : 'text-danger'}`}>
                                  {reportDetails.metrics.completedTasks.change >= 0 ? '+' : ''}{reportDetails.metrics.completedTasks.change} from previous {report.type}
                                </span>
                              </div>
                            </CardBody>
                          </Card>
                        </div>
                        
                        <div className="mb-6">
                          <h3 className="text-lg text-white font-medium mb-3">Key Insights</h3>
                          <div className="space-y-2">
                            {reportDetails.insights.map((insight, index) => (
                              <div key={index} className="flex items-start gap-3">
                                <div className="p-1 bg-primary/20 rounded-full mt-0.5">
                                  <Icon icon="lucide:lightbulb" className="text-primary text-sm" />
                                </div>
                                <p className="text-custom-body">{insight}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        <div className="mb-6">
                          <h3 className="text-lg text-white font-medium mb-3">Recommendations</h3>
                          <div className="space-y-3">
                            {reportDetails.recommendations.map((recommendation, index) => (
                              <div key={index} className="flex items-start gap-3">
                                <div className="p-1 bg-success/20 rounded-full mt-0.5">
                                  <Icon icon="lucide:check" className="text-success text-sm" />
                                </div>
                                <p className="text-custom-body">{recommendation}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </Tab>
                    
                    <Tab 
                      key="analysis" 
                      title={
                        <div className="flex items-center gap-1">
                          <Icon icon="lucide:bar-chart-2" />
                          <span>Detailed Analysis</span>
                        </div>
                      }
                    >
                      <div className="p-6">
                        <div className="mb-6">
                          <h3 className="text-lg text-white font-medium mb-3">Detailed Analysis</h3>
                          <div className="bg-custom-sidebar rounded-md p-4 text-custom-body">
                            {reportDetails.detailedAnalysis.split('\n\n').map((paragraph, index) => (
                              <p key={index} className={index > 0 ? 'mt-4' : ''}>{paragraph}</p>
                            ))}
                          </div>
                        </div>
                        
                        <Divider className="my-6 bg-custom-border" />
                        
                        <div className="mb-6">
                          <h3 className="text-lg text-white font-medium mb-4">Key Metrics</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <Card className="bg-custom-sidebar border-custom-border shadow-none">
                              <CardBody className="p-4">
                                <div className="flex items-center justify-between mb-2">
                                  <p className="text-custom-muted text-sm">Average Task Time</p>
                                  <Chip size="sm" color="primary" variant="flat">
                                    {reportDetails.metrics.avgTaskTime.change > 0 ? '+' : ''}{reportDetails.metrics.avgTaskTime.change}min
                                  </Chip>
                                </div>
                                <h3 className="text-white text-xl font-semibold">
                                  {reportDetails.metrics.avgTaskTime.minutes} minutes
                                </h3>
                              </CardBody>
                            </Card>
                            
                            <Card className="bg-custom-sidebar border-custom-border shadow-none">
                              <CardBody className="p-4">
                                <div className="flex items-center justify-between mb-2">
                                  <p className="text-custom-muted text-sm">Meeting Time</p>
                                  <Chip 
                                    size="sm" 
                                    color={reportDetails.metrics.meetingTime.change < 0 ? "success" : "danger"} 
                                    variant="flat"
                                  >
                                    {reportDetails.metrics.meetingTime.change > 0 ? '+' : ''}{reportDetails.metrics.meetingTime.change}h
                                  </Chip>
                                </div>
                                <h3 className="text-white text-xl font-semibold">
                                  {reportDetails.metrics.meetingTime.hours} hours
                                </h3>
                              </CardBody>
                            </Card>
                            
                          </div>
                        </div>
                        
                        <Divider className="my-6 bg-custom-border" />
                        
                        <div>
                          <h3 className="text-lg text-white font-medium mb-4">Focus Hours by Day</h3>
                          <div className="bg-custom-sidebar rounded-md p-4">
                            <div className="grid grid-cols-5 gap-2 h-40 items-end">
                              {reportDetails.charts.focusHours.map((day) => (
                                <div key={day.day} className="flex flex-col items-center h-full">
                                  <div 
                                    className="bg-primary/60 hover:bg-primary transition-colors w-full rounded-t-md"
                                    style={{ height: `${(day.hours / 10) * 100}%` }}
                                  ></div>
                                  <div className="mt-2 text-xs text-custom-muted">{day.day.slice(0, 3)}</div>
                                  <div className="mt-1 text-xs text-white">{day.hours}h</div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </Tab>
                    
                    <Tab 
                      key="compare" 
                      title={
                        <div className="flex items-center gap-1">
                          <Icon icon="lucide:git-compare" />
                          <span>Compare Reports</span>
                        </div>
                      }
                    >
                      <div className="p-6">
                        <div className="flex justify-center items-center h-64 text-custom-muted flex-col">
                          <Icon icon="lucide:line-chart" className="text-4xl mb-4" />
                          <p>Report comparison feature coming soon</p>
                          <p className="text-sm mt-2">You'll be able to compare multiple reports to track trends over time</p>
                        </div>
                      </div>
                    </Tab>
                  </Tabs>
                </ModalBody>
                
              </>
            )}
          </ModalContent>
        </Modal>
      );
    };