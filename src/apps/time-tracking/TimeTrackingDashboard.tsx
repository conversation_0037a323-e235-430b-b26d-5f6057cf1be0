import React from "react";
import { <PERSON>rid, <PERSON>, CardBody, Divider } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTimeTracking } from "../../context/TimeTrackingContext";
import { TimeTracker } from "../../components/time-tracker";
import { WorkHoursReport } from "../../components/work-hours-report";
import { WeeklyOverview } from "./components/WeeklyOverview";

export const TimeTrackingDashboard: React.FC = () => {
  const { timeLogs } = useTimeTracking();
  
  // Calculate weekly statistics
  const getTotalHoursThisWeek = () => {
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
    startOfWeek.setHours(0, 0, 0, 0);
    
    return timeLogs
      .filter(log => new Date(log.startTime) >= startOfWeek)
      .reduce((total, log) => {
        const [hours, minutes, seconds] = log.duration.split(':').map(Number);
        return total + hours + (minutes / 60) + (seconds / 3600);
      }, 0)
      .toFixed(1);
  };
  
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Time tracker component */}
        <div className="md:col-span-1">
          <TimeTracker />
        </div>
        
        {/* Weekly overview and stats */}
        <div className="md:col-span-2">
          <Card className="bg-custom-card border-custom-border shadow-none mb-6">
            <CardBody>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-white text-lg font-medium">Weekly Overview</h3>
                <div className="flex items-center text-custom-muted text-sm">
                  <span>This Week</span>
                  <Icon icon="lucide:chevron-down" className="ml-1" />
                </div>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
                <div className="bg-custom-sidebar rounded-lg p-4">
                  <div className="text-custom-muted text-sm mb-1">Hours Tracked</div>
                  <div className="text-white text-2xl font-semibold">{getTotalHoursThisWeek()}</div>
                  <div className="flex items-center text-success text-xs mt-2">
                    <Icon icon="lucide:arrow-up" className="mr-1" />
                    <span>+12% from last week</span>
                  </div>
                </div>
                
                <div className="bg-custom-sidebar rounded-lg p-4">
                  <div className="text-custom-muted text-sm mb-1">Productivity</div>
                  <div className="text-white text-2xl font-semibold">87%</div>
                  <div className="flex items-center text-success text-xs mt-2">
                    <Icon icon="lucide:arrow-up" className="mr-1" />
                    <span>+5% from last week</span>
                  </div>
                </div>
                
                <div className="bg-custom-sidebar rounded-lg p-4">
                  <div className="text-custom-muted text-sm mb-1">Tasks Completed</div>
                  <div className="text-white text-2xl font-semibold">24</div>
                  <div className="flex items-center text-danger text-xs mt-2">
                    <Icon icon="lucide:arrow-down" className="mr-1" />
                    <span>-3% from last week</span>
                  </div>
                </div>
              </div>
              
              <WeeklyOverview />
            </CardBody>
          </Card>
          
          {/* Work hours report section */}
          <WorkHoursReport />
        </div>
      </div>
    </div>
  );
};