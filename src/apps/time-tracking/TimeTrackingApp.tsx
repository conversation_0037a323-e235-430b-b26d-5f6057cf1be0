import React, { useState } from "react";
import { TauriIcon as Icon } from "../../components/TauriIcon";
import { TimeTracker } from "../../components/time-tracker";
import { WorkHoursReport } from "../../components/work-hours-report";
import { Heatmap } from "./Heatmap";
import { Card, CardBody, Divider, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Button, Tabs, Tab, useDisclosure } from "@heroui/react";
import { WeeklyAIReport } from "./WeeklyAIReport";
import { useUser } from "../../context/UserContext";

export const TimeTrackingApp = () => {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const { activityStats } = useUser();

  // Get default values from activityStats state or use 0
  const getDefaultValue = (slug: string): number => {
    const stat = activityStats.find(s => s.slug === slug);
    return stat ? stat.value : 0;
  };




  
  return (
    <div className="p-5 w-full">

      <div className="grid grid-cols-12 gap-6">
        {/* Left side - Timer */}
        <div className="col-span-12 md:col-span-5 lg:col-span-5">
          <TimeTracker />
        </div>
        
        {/* Right side - Replace WeeklySummary with Heatmap */}
        <div className="col-span-12 md:col-span-7 lg:col-span-7">
          <Heatmap 
            days={60}
            cellSize={13}
            gap={8}
            colors={{
              0: '#2A2D3C',
              1: '#3E71D1',
              2: '#1B84FF',
              3: '#4799FF'
            }}
            tooltip={(date, duration) => `${duration} – ${date}`}
          />
          {/* Weekly AI Report Card */}
      <div className="mt-6">
        <Card className="bg-custom-card border-custom-border shadow-none">
          <CardBody className="p-6">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-2">
                <div className="p-2 rounded-full bg-primary/20">
                  <Icon icon="lucide:sparkles" className="text-primary text-xl" />
                </div>
                <h3 className="text-white text-lg font-medium">Weekly AI Report</h3>
              </div>
              <Button 
                variant="flat" 
                color="primary" 
                size="sm"
                endContent={<Icon icon="lucide:chevron-right" />}
                onPress={onOpen}
              >
                Show More
              </Button>
            </div>
            
            <div className="text-custom-muted mb-3 text-sm">
              <span className="text-primary font-medium">AI-generated insights</span> for the week of {new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - {
                (() => {
                  const endOfWeek = new Date();
                  endOfWeek.setDate(endOfWeek.getDate() + (6 - endOfWeek.getDay()));
                  return endOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                })()
              }
            </div>
            
            <div className="border-l-2 border-primary pl-4 py-1 mb-4">
              <p className="text-white text-sm mb-2">
                You've shown exceptional productivity this week, completing tasks 23% faster than your average. 
                Your focus peaks between 10am-12pm, suggesting this is your optimal work window.
              </p>
            </div>
            
            <div className="grid grid-cols-3 gap-4">
              <div className="bg-custom-sidebar rounded-md p-3">
                <div className="text-custom-muted text-xs mb-1">Productivity Score</div>
                <div className="flex items-end gap-2">
                  <span className="text-white text-2xl font-semibold">8.7</span>
                  <span className="text-success text-sm mb-1 flex items-center">
                    <Icon icon="lucide:trend-up" className="mr-1" /> +1.2
                  </span>
                </div>
              </div>

              <div className="bg-custom-sidebar rounded-md p-3">
                <div className="text-custom-muted text-xs mb-1">Focus Time</div>
                <div className="flex items-end gap-2">
                  <span className="text-white text-2xl font-semibold">32h</span>
                  <span className="text-success text-sm mb-1 flex items-center">
                    <Icon icon="lucide:trend-up" className="mr-1" /> +4h
                  </span>
                </div>
              </div>

              <div className="bg-custom-sidebar rounded-md p-3">
                <div className="text-custom-muted text-xs mb-1">Completed Tasks</div>
                <div className="flex items-end gap-2">
                  <span className="text-white text-2xl font-semibold">24</span>
                  <span className="text-success text-sm mb-1 flex items-center">
                    <Icon icon="lucide:trend-up" className="mr-1" /> +7
                  </span>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
        </div>
      </div>

      

      {/* Weekly AI Report Modal */}
      <Modal 
        isOpen={isOpen} 
        onOpenChange={onOpenChange}
        size="3xl"
        classNames={{
          base: "bg-custom-card",
          header: "border-b border-custom-border",
          body: "py-6",
          footer: "border-t border-custom-border",
        }}
      >
        <ModalContent className="max-h-[90vh] overflow-y-auto bg-custom-card">
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1 text-white">
                AI Performance Reports
              </ModalHeader>
              <ModalBody>
                <WeeklyAIReport />
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  Close
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
      
       {/* Weekly overview and stats */}
        <div className="md:col-span-2 mt-8">
        <div className="grid grid-cols-4 gap-4 mb-6">
          {/* Always show the same 4 cards with fixed icons */}
          {activityStats.length > 0 ? (
            // Use API data if available, but keep fixed icons and layout
            <>
              {/* Hours This Month */}
              <Card className="bg-custom-card border-custom-border shadow-none">
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-custom-muted text-sm">
                        {activityStats.find(s => s.slug === 'hours_this_month')?.title || 'Hours This Month'}
                      </p>
                      <h3 className="text-white text-2xl font-semibold">
                        {activityStats.find(s => s.slug === 'hours_this_month') ?
                          `${activityStats.find(s => s.slug === 'hours_this_month')?.value}h` :
                          `${getDefaultValue('hours_this_month')}h`}
                      </h3>
                    </div>
                    <div className="p-3 rounded-full bg-primary/20">
                      <Icon icon="lucide:clock" className="text-primary text-xl" />
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Income This Month */}
              <Card className="bg-custom-card border-custom-border shadow-none">
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-custom-muted text-sm">
                        {activityStats.find(s => s.slug === 'income_this_month')?.title || 'Income This Month'}
                      </p>
                      <h3 className="text-white text-2xl font-semibold">
                        ${activityStats.find(s => s.slug === 'income_this_month')?.value || getDefaultValue('income_this_month')}
                      </h3>
                    </div>
                    <div className="p-3 rounded-full bg-success/20">
                      <Icon icon="lucide:dollar-sign" className="text-success text-xl" />
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Missing Hours Today */}
              <Card className="bg-custom-card border-custom-border shadow-none">
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-custom-muted text-sm">
                        {activityStats.find(s => s.slug === 'missing_hours_today')?.title || 'Missing Hours Today'}
                      </p>
                      <h3 className="text-white text-2xl font-semibold">
                        {activityStats.find(s => s.slug === 'missing_hours_today') ?
                          `${activityStats.find(s => s.slug === 'missing_hours_today')?.value}h` :
                          `${getDefaultValue('missing_hours_today')}h`}
                      </h3>
                    </div>
                    <div className="p-3 rounded-full bg-warning/20">
                      <Icon icon="lucide:alert-circle" className="text-warning text-xl" />
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Daily Commitment */}
              <Card className="bg-custom-card border-custom-border shadow-none">
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-custom-muted text-sm">
                        {activityStats.find(s => s.slug === 'your_daily_commitment')?.title || 'Your Daily Commitment'}
                      </p>
                      <h3 className="text-white text-2xl font-semibold">
                        {activityStats.find(s => s.slug === 'your_daily_commitment') ?
                          `${activityStats.find(s => s.slug === 'your_daily_commitment')?.value}h` :
                          `${getDefaultValue('your_daily_commitment')}h`}
                      </h3>
                    </div>
                    <div className="p-3 rounded-full bg-primary/20">
                      <Icon icon="lucide:target" className="text-primary text-xl" />
                    </div>
                  </div>
                </CardBody>
              </Card>
            </>
          ) : (
            // Fallback to local calculations if API data not available
            <>
              {/* Monthly Hours */}
              <Card className="bg-custom-card border-custom-border shadow-none">
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-custom-muted text-sm">Hours This Month</p>
                      <h3 className="text-white text-2xl font-semibold">{getDefaultValue('hours_this_month')}h</h3>
                    </div>
                    <div className="p-3 rounded-full bg-primary/20">
                      <Icon icon="lucide:clock" className="text-primary text-xl" />
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Monthly Income */}
              <Card className="bg-custom-card border-custom-border shadow-none">
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-custom-muted text-sm">Income This Month</p>
                      <h3 className="text-white text-2xl font-semibold">${getDefaultValue('income_this_month')}</h3>
                      </div>
                    <div className="p-3 rounded-full bg-success/20">
                      <Icon icon="lucide:dollar-sign" className="text-success text-xl" />
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Missing Hours Today */}
              <Card className="bg-custom-card border-custom-border shadow-none">
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-custom-muted text-sm">Missing Hours Today</p>
                      <h3 className="text-white text-2xl font-semibold">{getDefaultValue('missing_hours_today')}h</h3>
                      </div>
                    <div className="p-3 rounded-full bg-warning/20">
                      <Icon icon="lucide:alert-circle" className="text-warning text-xl" />
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Daily Commitment */}
              <Card className="bg-custom-card border-custom-border shadow-none">
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-custom-muted text-sm">Your Daily Commitment</p>
                      <h3 className="text-white text-2xl font-semibold">{getDefaultValue('your_daily_commitment')}h</h3>
                      </div>
                    <div className="p-3 rounded-full bg-primary/20">
                      <Icon icon="lucide:target" className="text-primary text-xl" />
                    </div>
                  </div>
                </CardBody>
              </Card>
            </>
          )}
          </div>
      </div>

      {/* Work hours reports */}
      <WorkHoursReport />
    </div>
  );
};