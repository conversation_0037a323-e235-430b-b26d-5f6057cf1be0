import React, { createContext, useContext, useState } from "react";
    import type {
      Challenge,
      Kudos,
      Recommendation,
      Month,
      User
    } from "../types";
    import { 
      mockChallenges, 
      mockKudos, 
      mockRecommendations, 
      mockMonths,
      mockUsers
    } from "../data/mockData";

    interface PulseBoardContextType {
      // Data
      challenges: Challenge[];
      kudos: Kudos[];
      recommendations: Recommendation[];
      months: Month[];
      users: User[];
      currentUser: User;
      
      // Current state
      currentMonth: string;
      remainingKudos: number;
      
      // Actions
      giveKudos: (to: string, count: number) => void;
      addRecommendation: (recommendation: Omit<Recommendation, 'id' | 'date'>) => void;
      
      // Modals
      isKudosModalOpen: boolean;
      setKudosModalOpen: (open: boolean) => void;
      isRecommendationModalOpen: boolean;
      setRecommendationModalOpen: (open: boolean) => void;
    }

    const PulseBoardContext = createContext<PulseBoardContextType | undefined>(undefined);

    export const PulseBoardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
      const [challenges, setChallenges] = useState<Challenge[]>(mockChallenges);
      const [kudos, setKudos] = useState<Kudos[]>(mockKudos);
      const [recommendations, setRecommendations] = useState<Recommendation[]>(mockRecommendations);
      const [months, setMonths] = useState<Month[]>(mockMonths);
      const [users, setUsers] = useState<User[]>(mockUsers);
      
      // Current user (in a real app, this would come from auth)
      const currentUser = users.find(u => u.id === 'user1') || users[0];
      
      // Current month in "YYYY-MM" format
      const currentMonth = "2023-11";
      
      // Calculate remaining kudos for current user in current month
      const remainingKudos = 5 - kudos
        .filter(k => k.from === currentUser.id && k.month === currentMonth)
        .reduce((total, k) => total + k.count, 0);
      
      // Modal states
      const [isKudosModalOpen, setKudosModalOpen] = useState(false);
      const [isRecommendationModalOpen, setRecommendationModalOpen] = useState(false);
      
      // Give kudos to a user
      const giveKudos = (to: string, count: number) => {
        if (count <= 0 || count > remainingKudos) return;
        
        const newKudos: Kudos = {
          id: `kudos-${Date.now()}`,
          from: currentUser.id,
          to,
          count,
          month: currentMonth,
          date: new Date().toISOString(),
        };
        
        setKudos([...kudos, newKudos]);
      };
      
      // Add a recommendation
      const addRecommendation = (recommendation: Omit<Recommendation, 'id' | 'date'>) => {
        const newRecommendation: Recommendation = {
          id: `rec-${Date.now()}`,
          ...recommendation,
          date: new Date().toISOString(),
        };
        
        setRecommendations([...recommendations, newRecommendation]);
      };

      return (
        <PulseBoardContext.Provider
          value={{
            challenges,
            kudos,
            recommendations,
            months,
            users,
            currentUser,
            currentMonth,
            remainingKudos,
            giveKudos,
            addRecommendation,
            isKudosModalOpen,
            setKudosModalOpen,
            isRecommendationModalOpen,
            setRecommendationModalOpen
          }}
        >
          {children}
        </PulseBoardContext.Provider>
      );
    };

    export const usePulseBoard = () => {
      const context = useContext(PulseBoardContext);
      if (context === undefined) {
        throw new Error("usePulseBoard must be used within a PulseBoardProvider");
      }
      return context;
    };