import React, { useMemo } from "react";
import { Icon } from "@iconify/react";
import { Dashboard } from "./pages/Dashboard";
import { ChallengesHistory } from "./pages/ChallengesHistory";
import { KudosTracker } from "./pages/KudosTracker";
import { PeerRecommendations } from "./pages/PeerRecommendations";
import { PulseBoardProvider } from "./context/PulseBoardContext";
import { useLocation } from "react-router-dom";

export const PulseBoardApp: React.FC = () => {
  const location = useLocation();
  const activeSection = useMemo(() => {
    const segment = location.pathname.split("/")[2] || "dashboard";
    return ["dashboard","challenges","kudos","recommendations"].includes(segment) ? segment : "dashboard";
  }, [location.pathname]);
  
  // Render the active section content
  const renderActiveSection = () => {
    switch (activeSection) {
      case "challenges":
        return <ChallengesHistory />;
      case "kudos":
        return <KudosTracker />;
      case "recommendations":
        return <PeerRecommendations />;
      case "dashboard":
      default:
        return <Dashboard />;
    }
  };
  
  return (
    <PulseBoardProvider>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-semibold text-white flex items-center">
              <Icon icon="lucide:zap" className="text-yellow-400 mr-2" /> 
              PulseBoard
            </h1>
            <p className="text-custom-muted">Team recognition and monthly challenges</p>
          </div>
          <div className="text-white flex items-center">
            <span>{new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}</span>
            <Icon icon="lucide:calendar" className="ml-2" />
          </div>
        </div>
        
        {/* Render content based on active section from sidebar */}
        {renderActiveSection()}
      </div>
    </PulseBoardProvider>
  );
};