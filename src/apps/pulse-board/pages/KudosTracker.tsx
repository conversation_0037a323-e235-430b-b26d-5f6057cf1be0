import React, { useState } from "react";
import { Card, CardBody, Button, Avatar, AvatarGroup, Tooltip, Chip, Divider, <PERSON>bs, Tab } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import { motion } from "framer-motion";
    import { usePulseBoard } from "../context/PulseBoardContext";
    import { KudosModal } from "../components/KudosModal";

    export const KudosTracker: React.FC = () => {
      const { 
        kudos, 
        months, 
        users, 
        currentUser, 
        remainingKudos,
        setKudosModalOpen 
      } = usePulseBoard();
      
      const [selectedMonth, setSelectedMonth] = useState<string>(months.find(m => m.isActive)?.id || months[0].id);
      const [viewType, setViewType] = useState<'given' | 'received'>('given');
      
      // Get if selected month is the current month (editable)
      const isCurrentMonth = months.find(m => m.id === selectedMonth)?.isActive || false;
      
      // Get kudos for selected month
      const givenKudos = kudos.filter(k => k.from === currentUser.id && k.month === selectedMonth);
      const receivedKudos = kudos.filter(k => k.to === currentUser.id && k.month === selectedMonth);
      
      // Find user by ID
      const findUser = (id: string) => users.find(u => u.id === id);
      
      // Format date
      const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        });
      };
      
      // Get month name from ID
      const getMonthName = (monthId: string) => {
        const month = months.find(m => m.id === monthId);
        return month ? month.name : monthId;
      };
      
      // Aggregate kudos by recipient
      const kudosByUser = () => {
        const result: Record<string, { count: number, user: any }> = {};
        
        if (viewType === 'given') {
          givenKudos.forEach(k => {
            const user = findUser(k.to);
            if (user) {
              if (!result[k.to]) {
                result[k.to] = { count: 0, user };
              }
              result[k.to].count += k.count;
            }
          });
        } else {
          receivedKudos.forEach(k => {
            const user = findUser(k.from);
            if (user) {
              if (!result[k.from]) {
                result[k.from] = { count: 0, user };
              }
              result[k.from].count += k.count;
            }
          });
        }
        
        return Object.values(result).sort((a, b) => b.count - a.count);
      };
      
      return (
        <div className="space-y-6">
          {/* Kudos Modal */}
          <KudosModal />
          
          {/* Month selector and controls */}
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <div className="overflow-x-auto pb-2">
              <div className="flex gap-2">
                {months.map((month) => (
                  <motion.div
                    key={month.id}
                    whileHover={{ y: -2 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      className={`min-w-[160px] ${selectedMonth === month.id ? 'bg-primary/20 border-primary' : 'bg-custom-card border-custom-border'}`}
                      variant={selectedMonth === month.id ? "bordered" : "flat"}
                      onPress={() => setSelectedMonth(month.id)}
                      startContent={<Icon icon="lucide:calendar" />}
                      endContent={
                        month.isActive ? (
                      <Chip color="success" variant="flat" size="sm">Current</Chip>
                        ) : month.isFuture ? (
                      <Chip color="default" variant="flat" size="sm">Upcoming</Chip>
                        ) : null
                      }
                      isDisabled={month.isFuture}
                    >
                      {month.name}
                    </Button>
                  </motion.div>
                ))}
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant={viewType === 'given' ? 'solid' : 'flat'}
                color={viewType === 'given' ? 'primary' : 'default'}
                onPress={() => setViewType('given')}
                startContent={<Icon icon="lucide:send" />}
              >
                Given
              </Button>
              <Button
                variant={viewType === 'received' ? 'solid' : 'flat'}
                color={viewType === 'received' ? 'primary' : 'default'}
                onPress={() => setViewType('received')}
                startContent={<Icon icon="lucide:heart-handshake" />}
              >
                Received
              </Button>
              {isCurrentMonth && (
                <Button
                  color="success"
                  isDisabled={remainingKudos === 0}
                  startContent={<Icon icon="lucide:plus" />}
                  onPress={() => setKudosModalOpen(true)}
                >
                  Give Kudos
                </Button>
              )}
            </div>
          </div>
          
          {/* Kudos summary card */}
          <Card className="bg-custom-card border-custom-border">
            <CardBody className="p-5">
              <div className="flex flex-col md:flex-row justify-between items-center gap-4">
                <div className="flex items-center">
                  <div className="p-3 bg-custom-sidebar rounded-lg">
                    <Icon 
                      icon={viewType === 'given' ? "lucide:gift" : "lucide:award"} 
                      className="text-primary text-2xl" 
                    />
                  </div>
                  <div className="ml-3">
                    <h3 className="font-medium text-white">
                      Kudos {viewType === 'given' ? 'Given' : 'Received'} - {getMonthName(selectedMonth)}
                    </h3>
                    <p className="text-xs text-custom-muted mt-1">
                      {viewType === 'given' 
                        ? `${givenKudos.reduce((total, k) => total + k.count, 0)} of 5 kudos given` 
                        : `${receivedKudos.reduce((total, k) => total + k.count, 0)} kudos received`
                      }
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-4 flex-wrap justify-center">
                  <div className="text-center">
                    <p className="text-2xl font-semibold text-white">
                      {viewType === 'given' 
                        ? givenKudos.reduce((total, k) => total + k.count, 0) 
                        : receivedKudos.reduce((total, k) => total + k.count, 0)
                      }
                    </p>
                    <p className="text-xs text-custom-muted">Total Kudos</p>
                  </div>
                  
                  <div className="text-center">
                    <p className="text-2xl font-semibold text-white">
                      {viewType === 'given' 
                        ? new Set(givenKudos.map(k => k.to)).size 
                        : new Set(receivedKudos.map(k => k.from)).size
                      }
                    </p>
                    <p className="text-xs text-custom-muted">
                      {viewType === 'given' ? 'Recipients' : 'Senders'}
                    </p>
                  </div>
                  
                  {isCurrentMonth && viewType === 'given' && (
                    <div className="text-center">
                      <p className="text-2xl font-semibold text-white">
                        {remainingKudos}
                      </p>
                      <p className="text-xs text-custom-muted">Remaining</p>
                    </div>
                  )}
                </div>
              </div>
            </CardBody>
          </Card>
          
          {/* Kudos summary by user */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="bg-custom-card border-custom-border lg:col-span-1">
              <CardBody>
                <h3 className="font-medium text-white mb-4">
                  {viewType === 'given' ? 'Recipients' : 'Senders'} Summary
                </h3>
                
                {kudosByUser().length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <Icon 
                      icon={viewType === 'given' ? "lucide:send-x" : "lucide:inbox-x"} 
                      className="text-4xl text-custom-muted mb-2" 
                    />
                    <p className="text-custom-muted">
                      No kudos {viewType === 'given' ? 'given' : 'received'} this month
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {kudosByUser().map((item, index) => (
                      <div 
                        key={item.user.id} 
                        className="flex justify-between items-center"
                      >
                        <div className="flex items-center">
                          <Avatar src={item.user.avatar} className="mr-3" />
                          <div>
                            <p className="text-white">{item.user.name}</p>
                            <p className="text-xs text-custom-muted">{item.user.department}</p>
                          </div>
                        </div>
                    <Chip 
                          color={index === 0 ? "primary" : "default"} 
                          variant={index === 0 ? "solid" : "flat"}
                      size="md"
                        >
                          {item.count}
                    </Chip>
                      </div>
                    ))}
                  </div>
                )}
              </CardBody>
            </Card>
            
            {/* Kudos details list */}
            <Card className="bg-custom-card border-custom-border lg:col-span-2">
              <CardBody>
                <h3 className="font-medium text-white mb-4">
                  Kudos Details
                </h3>
                
                {(viewType === 'given' ? givenKudos : receivedKudos).length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <Icon 
                      icon={viewType === 'given' ? "lucide:heart-off" : "lucide:inbox-x"} 
                      className="text-6xl text-custom-muted mb-4" 
                    />
                    <h3 className="text-white font-medium mb-2">
                      No kudos {viewType === 'given' ? 'given' : 'received'} yet
                    </h3>
                    <p className="text-custom-muted max-w-md mb-6">
                      {viewType === 'given' 
                        ? isCurrentMonth 
                          ? "You haven't given any kudos this month. Share your appreciation with your teammates!" 
                          : "You didn't give any kudos during this month."
                        : "You haven't received any kudos yet this month."
                      }
                    </p>
                    
                    {isCurrentMonth && viewType === 'given' && (
                      <Button 
                        color="success" 
                        startContent={<Icon icon="lucide:gift" />}
                        onPress={() => setKudosModalOpen(true)}
                        isDisabled={remainingKudos === 0}
                      >
                        Give Kudos Now
                      </Button>
                    )}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {(viewType === 'given' ? givenKudos : receivedKudos)
                      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                      .map((kudos) => {
                        const otherUser = findUser(viewType === 'given' ? kudos.to : kudos.from);
                        if (!otherUser) return null;
                        
                        return (
                          <Card key={kudos.id} className="bg-custom-sidebar border-custom-border">
                            <CardBody className="p-4">
                              <div className="flex justify-between items-start">
                                <div className="flex items-center">
                                  <Avatar src={otherUser.avatar} className="mr-3" />
                                  <div>
                                    <p className="text-white">
                                      {viewType === 'given' ? 'To: ' : 'From: '}
                                      {otherUser.name}
                                    </p>
                                    <p className="text-xs text-custom-muted">
                                      {formatDate(kudos.date)}
                                    </p>
                                  </div>
                                </div>
                                
                                <div className="flex items-center">
                              <Chip 
                                    color="danger"
                                    variant="flat"
                                size="md"
                                  >
                                    {kudos.count}
                              </Chip>
                                </div>
                              </div>
                              
                              {kudos.message && (
                                <div className="mt-3 bg-custom-card p-3 rounded-md">
                                  <p className="text-custom-text text-sm">{kudos.message}</p>
                                </div>
                              )}
                            </CardBody>
                          </Card>
                        );
                      })}
                  </div>
                )}
              </CardBody>
            </Card>
          </div>
        </div>
      );
    };