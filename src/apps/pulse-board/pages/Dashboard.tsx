import React from "react";
    import { Card, CardBody, CardFooter, Button, Progress, Badge, Avatar, AvatarGroup, Tooltip } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import { motion } from "framer-motion";
    import { usePulseBoard } from "../context/PulseBoardContext";
    import { KudosModal } from "../components/KudosModal";
    import { RecommendationModal } from "../components/RecommendationModal";

    export const Dashboard: React.FC = () => {
      const { 
        challenges, 
        kudos, 
        users, 
        currentUser, 
        currentMonth, 
        remainingKudos,
        setKudosModalOpen,
        setRecommendationModalOpen
      } = usePulseBoard();
      
      // Filter challenges for current month
      const currentChallenges = challenges.filter(c => c.month === currentMonth);
      
      // Calculate kudos stats
      const givenKudos = kudos.filter(k => k.from === currentUser.id && k.month === currentMonth);
      const receivedKudos = kudos.filter(k => k.to === currentUser.id && k.month === currentMonth);
      const givenCount = givenKudos.reduce((total, k) => total + k.count, 0);
      const receivedCount = receivedKudos.reduce((total, k) => total + k.count, 0);
      
      // Find user by ID
      const findUser = (id: string) => users.find(u => u.id === id);
      
      return (
        <div className="space-y-6">
          {/* Kudos and Recommendations Modals */}
          <KudosModal />
          <RecommendationModal />
          
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-custom-card border-custom-border">
              <CardBody className="pb-2">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-white flex items-center">
                    <Icon icon="lucide:heart" className="text-red-500 mr-2" />
                    Your Kudos
                  </h3>
                  <Badge content={5 - givenCount} color="danger">
                    <Icon icon="lucide:gift" className="text-xl text-custom-muted" />
                  </Badge>
                </div>
                
                <div className="mb-4">
                  <div className="flex justify-between mb-1">
                    <span className="text-sm text-custom-muted">Given</span>
                    <span className="text-sm text-white">{givenCount}/5</span>
                  </div>
                  <Progress 
                    value={givenCount * 20} 
                    color="primary" 
                    className="h-2"
                  />
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm text-custom-muted">Received</span>
                    <span className="text-sm text-white">{receivedCount}</span>
                  </div>
                  <Progress 
                    value={Math.min(receivedCount * 10, 100)} 
                    color="success" 
                    className="h-2"
                  />
                </div>
              </CardBody>
              
              <CardFooter>
                <Button 
                  color="primary" 
                  fullWidth 
                  startContent={<Icon icon="lucide:send" />}
                  isDisabled={remainingKudos === 0}
                  onPress={() => setKudosModalOpen(true)}
                >
                  {remainingKudos > 0 ? `Give Kudos (${remainingKudos} left)` : "All Kudos Given"}
                </Button>
              </CardFooter>
            </Card>
            
            <Card className="bg-custom-card border-custom-border">
              <CardBody className="pb-2">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-white flex items-center">
                    <Icon icon="lucide:message-square-quote" className="text-blue-500 mr-2" />
                    Recommendations
                  </h3>
                  <Badge color="primary">New</Badge>
                </div>
                
                <div className="h-[104px] flex flex-col justify-center items-center text-center">
                  <Icon icon="lucide:message-square-plus" className="text-4xl text-custom-muted mb-2" />
                  <p className="text-custom-muted">Share feedback about your teammates</p>
                </div>
              </CardBody>
              
              <CardFooter>
                <Button 
                  color="secondary" 
                  fullWidth 
                  startContent={<Icon icon="lucide:edit" />}
                  onPress={() => setRecommendationModalOpen(true)}
                >
                  Write Recommendation
                </Button>
              </CardFooter>
            </Card>
            
            <Card className="bg-custom-card border-custom-border">
              <CardBody className="pb-2">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-white flex items-center">
                    <Icon icon="lucide:trophy" className="text-yellow-400 mr-2" />
                    Your Badges
                  </h3>
                  <span className="text-sm text-custom-muted">
                    {currentChallenges.filter(c => c.winners?.includes(currentUser.id)).length} earned
                  </span>
                </div>
                
                <div className="flex gap-2 flex-wrap">
                  {currentChallenges.filter(c => c.winners?.includes(currentUser.id)).map((challenge) => (
                    <Tooltip key={challenge.id} content={challenge.title}>
                      <div className="bg-custom-sidebar p-2 rounded-md">
                        <motion.div
                          whileHover={{ scale: 1.1 }}
                          className="text-2xl"
                        >
                          <Icon icon={challenge.icon} className="text-primary" />
                        </motion.div>
                      </div>
                    </Tooltip>
                  ))}
                  
                  {currentChallenges.filter(c => c.winners?.includes(currentUser.id)).length === 0 && (
                    <div className="h-[104px] flex flex-col justify-center items-center text-center w-full">
                      <Icon icon="lucide:medal" className="text-4xl text-custom-muted mb-2" />
                      <p className="text-custom-muted">Complete challenges to earn badges</p>
                    </div>
                  )}
                </div>
              </CardBody>
              
              <CardFooter>
                <Button 
                  color="default" 
                  variant="flat"
                  fullWidth 
                  startContent={<Icon icon="lucide:chevrons-right" />}
                >
                  View All Badges
                </Button>
              </CardFooter>
            </Card>
          </div>
          
          {/* Monthly Challenges */}
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-white">Monthly Challenges</h2>
              <Badge color="primary" variant="flat">{currentMonth.split('-')[1]}/{currentMonth.split('-')[0]}</Badge>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {currentChallenges.map((challenge) => (
                <motion.div 
                  key={challenge.id}
                  whileHover={{ y: -5, transition: { duration: 0.2 } }}
                  className="h-full"
                >
                  <Card className="bg-custom-card border-custom-border h-full">
                    <CardBody className="p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex items-center">
                          <div className="p-3 bg-custom-sidebar rounded-lg">
                            <Icon icon={challenge.icon} className="text-primary text-2xl" />
                          </div>
                          <div className="ml-3">
                            <h3 className="font-medium text-white">{challenge.title}</h3>
                            <p className="text-xs text-custom-muted flex items-center mt-1">
                              <Icon icon="lucide:star" className="mr-1" />
                              <span>{challenge.points} points</span>
                            </p>
                          </div>
                        </div>
                        
                        <Badge color="primary" variant="flat" className="capitalize">
                          {challenge.category}
                        </Badge>
                      </div>
                      
                      <p className="text-custom-muted text-sm my-3">
                        {challenge.description}
                      </p>
                      
                      <div className="mt-4">
                        {challenge.winners && challenge.winners.length > 0 ? (
                          <div>
                            <p className="text-xs text-custom-muted mb-2">Winners</p>
                            <AvatarGroup max={3} className="justify-start">
                              {challenge.winners.map(winnerId => {
                                const winner = findUser(winnerId);
                                return winner ? (
                                  <Tooltip key={winnerId} content={winner.name}>
                                    <Avatar 
                                      src={winner.avatar}
                                      className="border-2 border-primary"
                                    />
                                  </Tooltip>
                                ) : null;
                              })}
                            </AvatarGroup>
                          </div>
                        ) : (
                          <div className="text-center">
                            <p className="text-custom-muted text-xs">
                              {new Date().getDate() < 25 
                                ? "Winners will be announced soon"
                                : "Winners will be announced at the end of the month"}
                            </p>
                          </div>
                        )}
                      </div>
                    </CardBody>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      );
    };