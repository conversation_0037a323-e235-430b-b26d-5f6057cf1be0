import React, { useState } from "react";
import { Card, CardBody, Chip, Avatar, AvatarGroup, Tooltip, Button, Tabs, Tab } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import { motion } from "framer-motion";
    import { usePulseBoard } from "../context/PulseBoardContext";

    export const ChallengesHistory: React.FC = () => {
      const { challenges, months, users } = usePulseBoard();
      const [selectedMonth, setSelectedMonth] = useState<string>(months.find(m => m.isActive)?.id || months[0].id);
      const [selectedCategory, setSelectedCategory] = useState<string>("all");
      
      // Get challenges for selected month
      const monthChallenges = challenges.filter(c => c.month === selectedMonth);
      
      // Filter by category if needed
      const filteredChallenges = selectedCategory === "all" 
        ? monthChallenges 
        : monthChallenges.filter(c => c.category === selectedCategory);
      
      // Find user by ID
      const findUser = (id: string) => users.find(u => u.id === id);
      
      // Get month name from ID
      const getMonthName = (monthId: string) => {
        const month = months.find(m => m.id === monthId);
        return month ? month.name : monthId;
      };
      
      return (
        <div className="space-y-6">
          {/* Month selector */}
          <div className="overflow-x-auto pb-2">
            <div className="flex gap-2">
              {months.map((month) => (
                <motion.div
                  key={month.id}
                  whileHover={{ y: -2 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    className={`min-w-[160px] ${selectedMonth === month.id ? 'bg-primary/20 border-primary' : 'bg-custom-card border-custom-border'}`}
                    variant={selectedMonth === month.id ? "bordered" : "flat"}
                    onPress={() => setSelectedMonth(month.id)}
                    startContent={<Icon icon="lucide:calendar" />}
                    endContent={
                      month.isActive ? (
                    <Chip color="success" variant="flat" size="sm">Current</Chip>
                      ) : month.isFuture ? (
                    <Chip color="default" variant="flat" size="sm">Upcoming</Chip>
                      ) : null
                    }
                    isDisabled={month.isFuture}
                  >
                    {month.name}
                  </Button>
                </motion.div>
              ))}
            </div>
          </div>
          
          {/* Category filter */}
          <Tabs
            aria-label="Challenge Categories"
            selectedKey={selectedCategory}
            onSelectionChange={(key) => setSelectedCategory(key as string)}
            variant="light"
            classNames={{
              tabList: "bg-custom-card p-0.5 rounded-lg",
              cursor: "bg-primary",
              tab: "text-white py-1.5 px-3"
            }}
          >
            <Tab key="all" title="All Categories" />
            <Tab key="performance" title="Performance" />
            <Tab key="collaboration" title="Collaboration" />
            <Tab key="learning" title="Learning" />
            <Tab key="social" title="Social" />
          </Tabs>
          
          {/* Month stats */}
          {!months.find(m => m.id === selectedMonth)?.isFuture && (
            <Card className="bg-custom-card border-custom-border">
              <CardBody className="p-4">
                <div className="flex flex-col md:flex-row justify-between items-center gap-4">
                  <div className="flex items-center">
                    <div className="p-3 bg-custom-sidebar rounded-lg">
                      <Icon icon="lucide:calendar-check" className="text-primary text-2xl" />
                    </div>
                    <div className="ml-3">
                      <h3 className="font-medium text-white">{getMonthName(selectedMonth)} Overview</h3>
                      <p className="text-xs text-custom-muted mt-1">
                        {filteredChallenges.length} challenges available
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex gap-4 flex-wrap justify-center">
                    <div className="text-center">
                      <p className="text-2xl font-semibold text-white">
                        {filteredChallenges.filter(c => c.winners && c.winners.length > 0).length}
                      </p>
                      <p className="text-xs text-custom-muted">Challenges Completed</p>
                    </div>
                    
                    <div className="text-center">
                      <p className="text-2xl font-semibold text-white">
                        {filteredChallenges.reduce((total, c) => total + (c.winners?.length || 0), 0)}
                      </p>
                      <p className="text-xs text-custom-muted">Winners Total</p>
                    </div>
                    
                    <div className="text-center">
                      <p className="text-2xl font-semibold text-white">
                        {filteredChallenges.reduce((total, c) => total + c.points, 0)}
                      </p>
                      <p className="text-xs text-custom-muted">Points Available</p>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          )}
          
          {/* Challenge cards */}
          {months.find(m => m.id === selectedMonth)?.isFuture ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Icon icon="lucide:calendar-clock" className="text-6xl text-custom-muted mb-4" />
              <h3 className="text-xl font-medium text-white mb-2">Future Challenges</h3>
              <p className="text-custom-muted text-center max-w-md mb-6">
                Challenges for this month aren't available yet. They will be revealed when the month begins.
              </p>
            </div>
          ) : filteredChallenges.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Icon icon="lucide:search-x" className="text-6xl text-custom-muted mb-4" />
              <h3 className="text-xl font-medium text-white mb-2">No Challenges Found</h3>
              <p className="text-custom-muted text-center max-w-md mb-6">
                No challenges were found for the selected filters. Try changing the category or month.
              </p>
              <Button 
                variant="flat"
                color="primary"
                startContent={<Icon icon="lucide:refresh-cw" />}
                onPress={() => {
                  setSelectedCategory("all");
                }}
              >
                Reset Filters
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredChallenges.map((challenge) => (
                <motion.div 
                  key={challenge.id}
                  whileHover={{ y: -5, transition: { duration: 0.2 } }}
                  className="h-full"
                >
                  <Card className="bg-custom-card border-custom-border h-full">
                    <CardBody className="p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex items-center">
                          <div className="p-3 bg-custom-sidebar rounded-lg">
                            <Icon icon={challenge.icon} className="text-primary text-2xl" />
                          </div>
                          <div className="ml-3">
                            <h3 className="font-medium text-white">{challenge.title}</h3>
                            <div className="flex items-center mt-1">
                              <Chip size="sm" variant="flat" color="primary" className="capitalize text-xs">
                                {challenge.category}
                              </Chip>
                              <span className="text-xs text-custom-muted ml-2 flex items-center">
                                <Icon icon="lucide:star" className="mr-1" />
                                {challenge.points}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <p className="text-custom-muted text-sm my-3">
                        {challenge.description}
                      </p>
                      
                      <div className="mt-4">
                        {challenge.winners && challenge.winners.length > 0 ? (
                          <div>
                            <p className="text-xs text-custom-muted mb-2">Winners</p>
                            <AvatarGroup max={3} className="justify-start">
                              {challenge.winners.map(winnerId => {
                                const winner = findUser(winnerId);
                                return winner ? (
                                  <Tooltip key={winnerId} content={winner.name}>
                                    <Avatar 
                                      src={winner.avatar}
                                      className="border-2 border-primary"
                                    />
                                  </Tooltip>
                                ) : null;
                              })}
                            </AvatarGroup>
                          </div>
                        ) : (
                          <div className="text-center py-2">
                            <p className="text-custom-muted text-xs">
                              {months.find(m => m.id === selectedMonth)?.isActive
                                ? "Winners will be announced at the end of the month"
                                : "No winners for this challenge"}
                            </p>
                          </div>
                        )}
                      </div>
                    </CardBody>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      );
    };