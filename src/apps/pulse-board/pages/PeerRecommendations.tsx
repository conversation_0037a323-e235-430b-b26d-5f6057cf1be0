import React, { useState } from "react";
    import { Card, CardBody, Button, Avatar, Chip, Divider, Tooltip, RadioGroup, Radio } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import { motion } from "framer-motion";
    import { usePulseBoard } from "../context/PulseBoardContext";
    import { RecommendationModal } from "../components/RecommendationModal";

    export const PeerRecommendations: React.FC = () => {
      const { 
        recommendations, 
        users, 
        currentUser,
        setRecommendationModalOpen
      } = usePulseBoard();
      
      const [filterType, setFilterType] = useState<'all' | 'given' | 'received'>('all');
      const [anonymousFilter, setAnonymousFilter] = useState<'all' | 'anonymous' | 'named'>('all');
      
      // Filter recommendations based on current filters
      const filteredRecommendations = recommendations.filter(rec => {
        // Filter by type (given/received/all)
        if (filterType === 'given' && rec.from !== currentUser.id) return false;
        if (filterType === 'received' && rec.to !== currentUser.id) return false;
        
        // Filter by anonymous/named
        if (anonymousFilter === 'anonymous' && !rec.isAnonymous) return false;
        if (anonymousFilter === 'named' && rec.isAnonymous) return false;
        
        return true;
      }).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      
      // Find user by ID
      const findUser = (id: string) => users.find(u => u.id === id);
      
      // Format date
      const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        });
      };
      
      return (
        <div className="space-y-6">
          {/* Recommendation Modal */}
          <RecommendationModal />
          
          {/* Controls and filters */}
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <RadioGroup
              orientation="horizontal"
              value={filterType}
              onValueChange={setFilterType as (value: string) => void}
              classNames={{
                wrapper: "bg-custom-card p-1 rounded-lg"
              }}
            >
              <Radio value="all">All</Radio>
              <Radio value="given">Given</Radio>
              <Radio value="received">Received</Radio>
            </RadioGroup>
            
            <div className="flex gap-2">
              <RadioGroup
                orientation="horizontal"
                value={anonymousFilter}
                onValueChange={setAnonymousFilter as (value: string) => void}
                classNames={{
                  wrapper: "bg-custom-card p-1 rounded-lg"
                }}
              >
                <Radio value="all">All</Radio>
                <Radio value="named">Named</Radio>
                <Radio value="anonymous">Anonymous</Radio>
              </RadioGroup>
              
              <Button
                color="primary"
                startContent={<Icon icon="lucide:plus" />}
                onPress={() => setRecommendationModalOpen(true)}
              >
                Write Recommendation
              </Button>
            </div>
          </div>
          
          {/* Recommendations */}
          {filteredRecommendations.length === 0 ? (
            <Card className="bg-custom-card border-custom-border">
              <CardBody className="flex flex-col items-center justify-center py-12 text-center">
                <Icon icon="lucide:message-square-x" className="text-6xl text-custom-muted mb-4" />
                <h3 className="text-white font-medium text-xl mb-2">No recommendations found</h3>
                <p className="text-custom-muted max-w-md mb-6">
                  {filterType === 'all' 
                    ? "There are no recommendations matching your filters."
                    : filterType === 'given'
                      ? "You haven't given any recommendations yet."
                      : "You haven't received any recommendations yet."
                  }
                </p>
                <Button 
                  color="primary" 
                  startContent={<Icon icon="lucide:edit" />}
                  onPress={() => setRecommendationModalOpen(true)}
                >
                  Write Your First Recommendation
                </Button>
              </CardBody>
            </Card>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {filteredRecommendations.map((rec) => {
                const fromUser = rec.isAnonymous ? null : findUser(rec.from);
                const toUser = findUser(rec.to);
                
                if (!toUser) return null;
                
                return (
                  <motion.div
                    key={rec.id}
                    whileHover={{ y: -3, transition: { duration: 0.2 } }}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card className="bg-custom-card border-custom-border h-full">
                      <CardBody className="p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div className="flex items-center">
                            {rec.isAnonymous ? (
                              <div className="w-10 h-10 bg-custom-sidebar rounded-full flex items-center justify-center mr-3">
                                <Icon icon="lucide:user" className="text-custom-muted text-xl" />
                              </div>
                            ) : (
                              <Avatar src={fromUser?.avatar || ''} className="mr-3" />
                            )}
                            <div>
                              <p className="text-white">
                                {rec.isAnonymous ? 'Anonymous' : fromUser?.name || 'Anonymous'}
                              </p>
                              <div className="flex items-center text-xs text-custom-muted">
                                <span>To: {toUser.name}</span>
                                {rec.isAnonymous && (
                                  <Chip size="sm" variant="flat" color="default" className="ml-2">
                                    Anonymous
                                  </Chip>
                                )}
                              </div>
                            </div>
                          </div>
                          
                          <div className="text-xs text-custom-muted">
                            {formatDate(rec.date)}
                          </div>
                        </div>
                        
                        <div className="bg-custom-sidebar p-4 rounded-md mb-3">
                          <p className="text-custom-text">{rec.message}</p>
                        </div>
                        
                        {rec.tags && rec.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {rec.tags.map((tag) => (
                              <Chip key={tag} size="sm" variant="flat" color="primary">
                                #{tag}
                              </Chip>
                            ))}
                          </div>
                        )}
                      </CardBody>
                    </Card>
                  </motion.div>
                );
              })}
            </div>
          )}
        </div>
      );
    };