export interface User {
      id: string;
      name: string;
      avatar: string;
      department: string;
      role: string;
    }

    export interface Challenge {
      id: string;
      month: string; // In format "YYYY-MM"
      title: string;
      description: string;
      icon: string;
      winners?: string[]; // User IDs of winners
      badge: string;
      category: 'performance' | 'collaboration' | 'learning' | 'social';
      points: number;
    }

    export interface Kudos {
      id: string;
      from: string; // User ID who gave kudos
      to: string;   // User ID who received kudos
      count: number;
      month: string; // In format "YYYY-MM"
      date: string;  // ISO date string
      message?: string;
    }

    export interface Recommendation {
      id: string;
      from: string;   // User ID or "anonymous"
      to: string;     // User ID
      message: string;
      isAnonymous: boolean;
      date: string;   // ISO date string
      tags?: string[];
    }

    export interface Month {
      id: string;     // In format "YYYY-MM"
      name: string;   // Display name (e.g., "November 2023")
      isActive: boolean; // Whether this is the current month
      isFuture: boolean; // Whether this is a future month
      stats?: {
        participationRate: number;
        totalKudos: number;
        totalRecommendations: number;
      };
    }