import type { Challenge, Kudos, Month, Recommendation, User } from "../types";

    export const mockUsers: User[] = [
      {
        id: "user1",
        name: "<PERSON>",
        avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=1",
        department: "Engineering",
        role: "Frontend Developer"
      },
      {
        id: "user2",
        name: "<PERSON>",
        avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=2",
        department: "Design",
        role: "UI/UX Designer"
      },
      {
        id: "user3",
        name: "<PERSON>",
        avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=3",
        department: "Engineering",
        role: "Backend Developer"
      },
      {
        id: "user4",
        name: "<PERSON>",
        avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=4",
        department: "Product",
        role: "Product Manager"
      },
      {
        id: "user5",
        name: "<PERSON>",
        avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=5",
        department: "Marketing",
        role: "Marketing Lead"
      },
      {
        id: "user6",
        name: "<PERSON>",
        avatar: "https://img.heroui.chat/image/avatar?w=200&h=200&u=6",
        department: "Engineering",
        role: "DevOps Engineer"
      }
    ];

    export const mockMonths: Month[] = [
      {
        id: "2023-09",
        name: "September 2023",
        isActive: false,
        isFuture: false,
        stats: {
          participationRate: 82,
          totalKudos: 24,
          totalRecommendations: 8
        }
      },
      {
        id: "2023-10",
        name: "October 2023",
        isActive: false,
        isFuture: false,
        stats: {
          participationRate: 91,
          totalKudos: 29,
          totalRecommendations: 12
        }
      },
      {
        id: "2023-11",
        name: "November 2023",
        isActive: true,
        isFuture: false,
        stats: {
          participationRate: 75,
          totalKudos: 18,
          totalRecommendations: 9
        }
      },
      {
        id: "2023-12",
        name: "December 2023",
        isActive: false,
        isFuture: true
      }
    ];

    export const mockChallenges: Challenge[] = [
      {
        id: "ch-1",
        month: "2023-11",
        title: "Knowledge Sharing Hero",
        description: "Share your knowledge through at least 3 internal presentations or documentation.",
        icon: "lucide:book-open",
        badge: "knowledge-badge",
        category: "learning",
        points: 100,
        winners: ["user2"]
      },
      {
        id: "ch-2",
        month: "2023-11",
        title: "Bug Crusher",
        description: "Fix the most critical bugs within the month.",
        icon: "lucide:bug",
        badge: "bug-badge",
        category: "performance",
        points: 150
      },
      {
        id: "ch-3",
        month: "2023-11",
        title: "Top Kudos Receiver",
        description: "Receive the most kudos from your teammates this month.",
        icon: "lucide:heart",
        badge: "kudos-badge",
        category: "social",
        points: 120,
        winners: ["user5"]
      },
      {
        id: "ch-4",
        month: "2023-11",
        title: "Team Player",
        description: "Demonstrate exceptional collaboration and support across departments.",
        icon: "lucide:users",
        badge: "team-badge",
        category: "collaboration",
        points: 100
      },
      {
        id: "ch-5",
        month: "2023-11",
        title: "Deadline Dominator",
        description: "Complete all tasks before their deadlines consistently.",
        icon: "lucide:clock",
        badge: "deadline-badge",
        category: "performance",
        points: 80,
        winners: ["user3", "user6"]
      },
      {
        id: "ch-6",
        month: "2023-10",
        title: "Innovation Master",
        description: "Propose the best innovative solution to a company challenge.",
        icon: "lucide:lightbulb",
        badge: "innovation-badge",
        category: "collaboration",
        points: 150,
        winners: ["user4"]
      },
      {
        id: "ch-7",
        month: "2023-10",
        title: "Customer Happiness Champion",
        description: "Receive the highest customer satisfaction ratings.",
        icon: "lucide:smile",
        badge: "customer-badge",
        category: "performance",
        points: 120,
        winners: ["user1", "user5"]
      },
      {
        id: "ch-8",
        month: "2023-10",
        title: "Learning Explorer",
        description: "Complete the most learning courses and share insights with the team.",
        icon: "lucide:compass",
        badge: "learning-badge",
        category: "learning",
        points: 90,
        winners: ["user2"]
      }
    ];

    export const mockKudos: Kudos[] = [
      {
        id: "kudos-1",
        from: "user1",
        to: "user2",
        count: 2,
        month: "2023-11",
        date: "2023-11-05T10:30:00.000Z",
        message: "Thanks for your help with the design review!"
      },
      {
        id: "kudos-2",
        from: "user3",
        to: "user1",
        count: 1,
        month: "2023-11",
        date: "2023-11-06T14:15:00.000Z",
        message: "Great collaboration on the API integration."
      },
      {
        id: "kudos-3",
        from: "user2",
        to: "user5",
        count: 3,
        month: "2023-11",
        date: "2023-11-10T09:45:00.000Z",
        message: "Thanks for the marketing support on the product launch!"
      },
      {
        id: "kudos-4",
        from: "user4",
        to: "user3",
        count: 2,
        month: "2023-11",
        date: "2023-11-12T16:20:00.000Z"
      },
      {
        id: "kudos-5",
        from: "user5",
        to: "user1",
        count: 1,
        month: "2023-11",
        date: "2023-11-15T11:10:00.000Z",
        message: "Excellent work on the frontend optimization!"
      },
      {
        id: "kudos-6",
        from: "user6",
        to: "user2",
        count: 2,
        month: "2023-11",
        date: "2023-11-18T15:30:00.000Z"
      },
      {
        id: "kudos-7",
        from: "user1",
        to: "user5",
        count: 3,
        month: "2023-10",
        date: "2023-10-08T13:40:00.000Z",
        message: "Outstanding presentation to the client!"
      },
      {
        id: "kudos-8",
        from: "user2",
        to: "user4",
        count: 2,
        month: "2023-10",
        date: "2023-10-12T10:15:00.000Z"
      }
    ];

    export const mockRecommendations: Recommendation[] = [
      {
        id: "rec-1",
        from: "user2",
        to: "user1",
        message: "Alex is an exceptional frontend developer who consistently delivers clean, efficient code. Their attention to detail and problem-solving skills have been instrumental in the success of our latest project.",
        isAnonymous: false,
        date: "2023-11-08T11:20:00.000Z",
        tags: ["coding", "problem-solving"]
      },
      {
        id: "rec-2",
        from: "user3",
        to: "user1",
        message: "Always willing to help teammates and share knowledge. A true team player who elevates the entire group.",
        isAnonymous: true,
        date: "2023-11-10T09:30:00.000Z",
        tags: ["teamwork", "knowledge-sharing"]
      },
      {
        id: "rec-3",
        from: "user1",
        to: "user4",
        message: "Emma's product vision and leadership have been crucial to our team's success. She effectively communicates requirements and ensures everyone is aligned with project goals.",
        isAnonymous: false,
        date: "2023-11-12T14:45:00.000Z",
        tags: ["leadership", "communication"]
      },
      {
        id: "rec-4",
        from: "user5",
        to: "user2",
        message: "Sophia's design skills are exceptional. She consistently delivers intuitive and beautiful interfaces that our users love.",
        isAnonymous: false,
        date: "2023-11-15T16:10:00.000Z",
        tags: ["design", "creativity"]
      },
      {
        id: "rec-5",
        from: "user4",
        to: "user6",
        message: "Olivia's DevOps expertise has significantly improved our deployment process. She's always proactive in identifying and resolving potential issues.",
        isAnonymous: true,
        date: "2023-11-18T10:50:00.000Z",
        tags: ["devops", "proactive"]
      }
    ];