import React, { useState } from "react";
    import { 
      <PERSON><PERSON>, 
      <PERSON><PERSON><PERSON><PERSON><PERSON>, 
      <PERSON><PERSON><PERSON><PERSON><PERSON>, 
      <PERSON>dal<PERSON>ody, 
      <PERSON>dal<PERSON>ooter,
      Button,
      Avatar,
      Card,
      Textarea,
      Divider,
      Switch,
      Input,
      Chip
    } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import { usePulseBoard } from "../context/PulseBoardContext";

    export const RecommendationModal: React.FC = () => {
      const { 
        isRecommendationModalOpen, 
        setRecommendationModalOpen, 
        users, 
        currentUser, 
        addRecommendation
      } = usePulseBoard();
      
      const [selectedUser, setSelectedUser] = useState<string | null>(null);
      const [isAnonymous, setIsAnonymous] = useState<boolean>(false);
      const [message, setMessage] = useState<string>('');
      const [tag, setTag] = useState<string>('');
      const [tags, setTags] = useState<string[]>([]);
      
      // Reset modal state when opening
      React.useEffect(() => {
        if (isRecommendationModalOpen) {
          setSelectedUser(null);
          setIsAnonymous(false);
          setMessage('');
          setTag('');
          setTags([]);
        }
      }, [isRecommendationModalOpen]);
      
      // Handle adding a tag
      const handleAddTag = () => {
        if (tag.trim() && !tags.includes(tag.trim())) {
          setTags([...tags, tag.trim()]);
          setTag('');
        }
      };
      
      // Handle removing a tag
      const handleRemoveTag = (tagToRemove: string) => {
        setTags(tags.filter(t => t !== tagToRemove));
      };
      
      // Handle submitting recommendation
      const handleSubmit = () => {
        if (!selectedUser || !message.trim()) return;
        
        addRecommendation({
          from: currentUser.id,
          to: selectedUser,
          message: message.trim(),
          isAnonymous,
          tags: tags.length > 0 ? tags : undefined
        });
        
        setRecommendationModalOpen(false);
      };
      
      return (
        <Modal 
          isOpen={isRecommendationModalOpen} 
          onOpenChange={setRecommendationModalOpen}
          size="3xl"
          classNames={{
            base: "bg-custom-card text-white",
            header: "border-b border-custom-border",
            footer: "border-t border-custom-border",
            closeButton: "text-white hover:bg-custom-border"
          }}
        >
          <ModalContent>
            {(onClose) => (
              <>
                <ModalHeader className="flex flex-col gap-1">
                  <div className="flex items-center">
                    <Icon icon="lucide:message-square-quote" className="text-blue-500 mr-2" />
                    Write a Recommendation
                  </div>
                </ModalHeader>
                
                <Divider />
                
                <ModalBody>
                  <div className="mb-6">
                    <p className="text-white mb-2">Who do you want to recommend?</p>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                      {users
                        .filter(user => user.id !== currentUser.id)
                        .map(user => (
                          <Card 
                            key={user.id}
                            isPressable
                            onPress={() => setSelectedUser(user.id)}
                            className={`border-2 ${selectedUser === user.id ? 'border-primary' : 'border-custom-border'} bg-custom-sidebar`}
                          >
                            <div className="p-3 flex items-center">
                              <Avatar src={user.avatar} className="mr-3" />
                              <div>
                                <p className="text-white">{user.name}</p>
                                <p className="text-xs text-custom-muted">{user.department}</p>
                              </div>
                            </div>
                          </Card>
                        ))
                      }
                    </div>
                  </div>
                  
                  {selectedUser && (
                    <>
                      <div className="mb-6">
                        <p className="text-white mb-2">Your recommendation</p>
                        <Textarea
                          placeholder="Write your recommendation here..."
                          value={message}
                          onValueChange={setMessage}
                          minRows={5}
                          classNames={{
                            inputWrapper: "bg-custom-sidebar border-custom-border",
                          }}
                        />
                      </div>
                      
                      <div className="mb-6">
                        <p className="text-white mb-2">Add tags (optional)</p>
                        <div className="flex gap-2 mb-2">
                          <Input
                            placeholder="Add a tag (e.g., teamwork)"
                            value={tag}
                            onValueChange={setTag}
                            classNames={{
                              inputWrapper: "bg-custom-sidebar border-custom-border",
                            }}
                          />
                          <Button
                            color="primary"
                            isIconOnly
                            onPress={handleAddTag}
                            isDisabled={!tag.trim()}
                          >
                            <Icon icon="lucide:plus" />
                          </Button>
                        </div>
                        
                        {tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {tags.map((t) => (
                              <Chip 
                                key={t} 
                                onClose={() => handleRemoveTag(t)}
                                variant="flat"
                                color="primary"
                              >
                                #{t}
                              </Chip>
                            ))}
                          </div>
                        )}
                      </div>
                      
                      <div>
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-white">Make this anonymous?</p>
                            <p className="text-xs text-custom-muted">
                              {isAnonymous 
                                ? "Your name will not be shown with this recommendation" 
                                : "Your name will be visible with this recommendation"
                              }
                            </p>
                          </div>
                          <Switch
                            checked={isAnonymous}
                            onChange={() => setIsAnonymous(!isAnonymous)}
                          />
                        </div>
                      </div>
                    </>
                  )}
                </ModalBody>
                
                <Divider />
                
                <ModalFooter>
                  <Button 
                    auto 
                    variant="flat" 
                    onPress={onClose}
                  >
                    Cancel
                  </Button>
                  <Button 
                    auto 
                    color="primary"
                    onPress={handleSubmit}
                    isDisabled={!selectedUser || !message.trim()}
                  >
                    Submit Recommendation
                  </Button>
                </ModalFooter>
              </>
            )}
          </ModalContent>
        </Modal>
      );
    };