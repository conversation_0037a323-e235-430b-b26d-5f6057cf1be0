import React, { useState } from "react";
    import { 
      <PERSON><PERSON>, 
      <PERSON><PERSON><PERSON><PERSON><PERSON>, 
      <PERSON><PERSON><PERSON><PERSON><PERSON>, 
      <PERSON>dal<PERSON><PERSON>, 
      <PERSON>dal<PERSON><PERSON>er,
      Button,
      Avatar,
      Card,
      Textarea,
      Divider
    } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import { motion } from "framer-motion";
    import { usePulseBoard } from "../context/PulseBoardContext";

    export const KudosModal: React.FC = () => {
      const { 
        isKudosModalOpen, 
        setKudosModalOpen, 
        users, 
        currentUser, 
        remainingKudos,
        giveKudos
      } = usePulseBoard();
      
      const [selectedUser, setSelectedUser] = useState<string | null>(null);
      const [kudosCount, setKudosCount] = useState<number>(1);
      const [message, setMessage] = useState<string>('');
      
      // Reset modal state when opening
      React.useEffect(() => {
        if (isKudosModalOpen) {
          setSelectedUser(null);
          setKudosCount(1);
          setMessage('');
        }
      }, [isKudosModalOpen]);
      
      // Handle giving kudos
      const handleGiveKudos = () => {
        if (!selectedUser || kudosCount <= 0 || kudosCount > remainingKudos) return;
        
        giveKudos(selectedUser, kudosCount);
        setKudosModalOpen(false);
      };
      
      return (
        <Modal 
          isOpen={isKudosModalOpen} 
          onOpenChange={setKudosModalOpen}
          size="3xl"
          classNames={{
            base: "bg-custom-card text-white",
            header: "border-b border-custom-border",
            footer: "border-t border-custom-border",
            closeButton: "text-white hover:bg-custom-border"
          }}
        >
          <ModalContent>
            {(onClose) => (
              <>
                <ModalHeader className="flex flex-col gap-1">
                  <div className="flex items-center">
                    <Icon icon="lucide:heart" className="text-red-500 mr-2" />
                    Give Kudos
                  </div>
                </ModalHeader>
                
                <Divider />
                
                <ModalBody>
                  <div className="mb-4">
                    <p className="text-custom-muted mb-1">Remaining Kudos</p>
                    <div className="flex items-center">
                      <div className="flex">
                        {Array.from({ length: remainingKudos }).map((_, i) => (
                          <motion.div 
                            key={i}
                            initial={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ delay: i * 0.1 }}
                          >
                            <Icon icon="lucide:heart-handshake" className="text-2xl text-red-500" />
                          </motion.div>
                        ))}
                        {Array.from({ length: 5 - remainingKudos }).map((_, i) => (
                          <motion.div key={i}>
                            <Icon icon="lucide:heart-handshake" className="text-2xl text-custom-muted opacity-30" />
                          </motion.div>
                        ))}
                      </div>
                      <span className="text-custom-muted ml-2">
                        You have {remainingKudos} kudos left to give this month
                      </span>
                    </div>
                  </div>
                  
                  <div className="mb-6">
                    <p className="text-white mb-2">Who do you want to recognize?</p>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                      {users
                        .filter(user => user.id !== currentUser.id)
                        .map(user => (
                          <Card 
                            key={user.id}
                            isPressable
                            onPress={() => setSelectedUser(user.id)}
                            className={`border-2 ${selectedUser === user.id ? 'border-primary' : 'border-custom-border'} bg-custom-sidebar`}
                          >
                            <div className="p-3 flex items-center">
                              <Avatar src={user.avatar} className="mr-3" />
                              <div>
                                <p className="text-white">{user.name}</p>
                                <p className="text-xs text-custom-muted">{user.department}</p>
                              </div>
                            </div>
                          </Card>
                        ))
                      }
                    </div>
                  </div>
                  
                  {selectedUser && (
                    <>
                      <div className="mb-6">
                        <p className="text-white mb-2">How many kudos do you want to give?</p>
                        <div className="flex gap-3">
                          {[1, 2, 3, 4, 5].map((num) => (
                            <Button
                              key={num}
                              variant={kudosCount === num ? 'solid' : 'flat'}
                              color={kudosCount === num ? 'primary' : 'default'}
                              onPress={() => setKudosCount(num)}
                              isDisabled={num > remainingKudos}
                              className="px-6"
                            >
                              {num}
                            </Button>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <p className="text-white mb-2">Add a message (optional)</p>
                        <Textarea
                          placeholder="Write why you're giving kudos..."
                          value={message}
                          onValueChange={setMessage}
                          minRows={3}
                          classNames={{
                            inputWrapper: "bg-custom-sidebar border-custom-border",
                          }}
                        />
                      </div>
                    </>
                  )}
                </ModalBody>
                
                <Divider />
                
                <ModalFooter>
                  <Button 
                    auto 
                    variant="flat" 
                    onPress={onClose}
                  >
                    Cancel
                  </Button>
                  <Button 
                    auto 
                    color="primary"
                    onPress={handleGiveKudos}
                    isDisabled={!selectedUser || remainingKudos === 0}
                  >
                    Give Kudos
                  </Button>
                </ModalFooter>
              </>
            )}
          </ModalContent>
        </Modal>
      );
    };