import React, { useState, useRef, useCallback } from "react";
import { Card, CardHeader, CardBody, Input, Button, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter } from "@heroui/react";
import { Icon } from "@iconify/react";
import { FileExplorer } from "./components/FileExplorer";
import { FilePermissionsModal } from "./components/FilePermissionsModal";
import { FileContextMenu } from "./components/FileContextMenu";
import { useFileManager } from "./hooks/useFileManager";
import type { FileItem, FolderItem, FileSection } from "./types";

export const FileApp = () => {
  const [activeSection, setActiveSection] = useState<FileSection>("my-files");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedItem, setSelectedItem] = useState<FileItem | FolderItem | null>(null);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [permissionsModalOpen, setPermissionsModalOpen] = useState(false);
  const [newFolderModalOpen, setNewFolderModalOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState("New Folder");
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    item: FileItem | FolderItem | null;
  }>({
    visible: false,
    x: 0,
    y: 0,
    item: null
  });

  const fileExplorerRef = useRef<HTMLDivElement>(null);

  const { 
    files, 
    folders, 
    currentPath,
    navigateTo,
    createFolder,
    deleteItem,
    renameItem,
    generateShareLink,
    disableShareLink,
    updatePermissions
  } = useFileManager(activeSection);

  // Handle context menu
  const handleContextMenu = useCallback((e: React.MouseEvent, item: FileItem | FolderItem) => {
    e.preventDefault();
    e.stopPropagation();
    
    setContextMenu({
      visible: true,
      x: e.clientX,
      y: e.clientY,
      item
    });
    
    // Add event listener to close context menu when clicking outside
    const handleClickOutside = () => {
      setContextMenu(prev => ({ ...prev, visible: false }));
      document.removeEventListener("click", handleClickOutside);
    };
    
    document.addEventListener("click", handleClickOutside);
  }, []);

  // Close context menu
  const closeContextMenu = () => {
    setContextMenu(prev => ({ ...prev, visible: false }));
  };

  // Handle context menu actions
  const handleContextMenuAction = (action: string) => {
    const item = contextMenu.item;
    if (!item) return;
    
    switch (action) {
      case "rename":
        setSelectedItem(item);
        setPermissionsModalOpen(true);
        break;
      case "access":
        setSelectedItem(item);
        setPermissionsModalOpen(true);
        break;
      case "copy":
        // Handle copy logic
        break;
      case "startCopy":
        // just close context menu, modal handled inside menu component
        break;
      case "delete":
        deleteItem(item.id, item.type);
        break;
      default:
        break;
    }
    
    closeContextMenu();
  };

  // Handle click outside file explorer to close context menu
  const handleExplorerClick = () => {
    closeContextMenu();
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex items-center justify-between mb-6 flex-wrap gap-4">
        <div className="flex items-center gap-2 flex-wrap">
          <span
            className="text-white text-2xl font-semibold cursor-pointer"
            onClick={() => navigateTo("/")}
          >
            File Manager
          </span>
        {currentPath !== "/" && (
          <>
            <Icon icon="lucide:chevron-right" className="text-custom-muted" />
              {currentPath
                .split("/")
                .filter(Boolean)
                .map((folder, index, array) => (
              <React.Fragment key={folder}>
                <Button
                  size="sm"
                  variant="light"
                  onPress={() => navigateTo("/" + array.slice(0, index + 1).join("/"))}
                  className="px-2 h-7"
                >
                  {folder}
                </Button>
                {index < array.length - 1 && (
                  <Icon icon="lucide:chevron-right" className="text-custom-muted" />
                )}
              </React.Fragment>
            ))}
          </>
          )}
        </div>
        <div className="flex items-center gap-3">
          <Input
            placeholder="Search files..."
            value={searchQuery}
            onValueChange={setSearchQuery}
            startContent={<Icon icon="lucide:search" className="text-custom-muted" />}
            classNames={{
              inputWrapper: "bg-custom-card border-custom-border h-10 w-[240px]",
            }}
            radius="lg"
            variant="bordered"
            size="sm"
          />
          
          <Button
            color="primary"
            startContent={<Icon icon="lucide:upload" />}
          >
            Upload
          </Button>
          
          <Button
            variant="flat"
            className="bg-custom-card border-custom-border"
            startContent={<Icon icon="lucide:folder-plus" />}
            onPress={() => {
              setNewFolderName("New Folder");
              setNewFolderModalOpen(true);
            }}
          >
            New Folder
          </Button>

          <div className="flex items-center gap-2">
          <Button
            isIconOnly
            variant="light"
            className={viewMode === "grid" ? "text-primary" : "text-custom-muted"}
            onPress={() => setViewMode("grid")}
          >
            <Icon icon="lucide:grid" />
          </Button>
          <Button
            isIconOnly
            variant="light"
            className={viewMode === "list" ? "text-primary" : "text-custom-muted"}
            onPress={() => setViewMode("list")}
          >
            <Icon icon="lucide:list" />
          </Button>
        </div>
        </div>
      </div>


      {/* <div className="flex gap-6 flex-wrap mb-6">
        <Card className="bg-custom-card border-custom-border shadow-none p-4 w-[200px]">
          <div className="flex items-center justify-center mb-4">
            <Icon icon="lucide:file-text" className="text-primary text-5xl" />
          </div>
          <div className="text-center">
            <h3 className="text-white text-base font-medium">Documents</h3>
            <p className="text-custom-muted text-sm">125 files</p>
          </div>
        </Card>
        
        <Card className="bg-custom-card border-custom-border shadow-none p-4 w-[200px]">
          <div className="flex items-center justify-center mb-4">
            <Icon icon="lucide:image" className="text-green-500 text-5xl" />
          </div>
          <div className="text-center">
            <h3 className="text-white text-base font-medium">Images</h3>
            <p className="text-custom-muted text-sm">87 files</p>
          </div>
        </Card>
        
        <Card className="bg-custom-card border-custom-border shadow-none p-4 w-[200px]">
          <div className="flex items-center justify-center mb-4">
            <Icon icon="lucide:music" className="text-purple-500 text-5xl" />
          </div>
          <div className="text-center">
            <h3 className="text-white text-base font-medium">Audio</h3>
            <p className="text-custom-muted text-sm">45 files</p>
          </div>
        </Card>
        
        <Card className="bg-custom-card border-custom-border shadow-none p-4 w-[200px]">
          <div className="flex items-center justify-center mb-4">
            <Icon icon="lucide:video" className="text-red-500 text-5xl" />
          </div>
          <div className="text-center">
            <h3 className="text-white text-base font-medium">Videos</h3>
            <p className="text-custom-muted text-sm">32 files</p>
          </div>
        </Card>
      </div> */}

      {/* File navigation and actions */}
      <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
        

      </div>

      {/* Breadcrumbs */}
      <div className="flex items-center gap-2 mb-4 text-custom-muted text-sm">

      </div>

      {/* File Explorer */}
      <div ref={fileExplorerRef} onClick={handleExplorerClick}>
        <FileExplorer
          files={files}
          folders={folders}
          searchQuery={searchQuery}
          viewMode={viewMode}
          onContextMenu={handleContextMenu}
          onItemClick={(item) => {
            if (item.type === 'folder') {
              navigateTo(`${currentPath}${item.name}/`);
            }
          }}
          onAccessSettings={(item) => {
            setSelectedItem(item);
            setPermissionsModalOpen(true);
          }}
        />
      </div>

      {/* Context Menu */}
      <FileContextMenu
        visible={contextMenu.visible}
        x={contextMenu.x}
        y={contextMenu.y}
        onAction={handleContextMenuAction}
        item={contextMenu.item}
      />

      {/* Permissions Modal */}
      <FilePermissionsModal
        isOpen={permissionsModalOpen}
        onClose={() => setPermissionsModalOpen(false)}
        item={selectedItem}
        onUpdatePermissions={updatePermissions}
        onGenerateShareLink={generateShareLink}
        onDisableShareLink={disableShareLink}
      />

      {/* New Folder Modal */}
      <Modal isOpen={newFolderModalOpen} onClose={() => setNewFolderModalOpen(false)} classNames={{ base: "bg-custom-card text-white" }}>
        <ModalContent>
          {(onCloseModal) => (
            <>
              <ModalHeader>Create New Folder</ModalHeader>
              <ModalBody>
                <Input
                  label="Folder Name"
                  value={newFolderName}
                  onChange={(e) => setNewFolderName(e.target.value)}
                  variant="bordered"
                  classNames={{ inputWrapper: "bg-custom-sidebar border-custom-border" }}
                />
              </ModalBody>
              <ModalFooter>
                <Button variant="bordered" onPress={onCloseModal}>Cancel</Button>
                <Button color="primary" isDisabled={!newFolderName.trim()} onPress={() => { createFolder(newFolderName.trim()); onCloseModal(); }}>
                  Create
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};