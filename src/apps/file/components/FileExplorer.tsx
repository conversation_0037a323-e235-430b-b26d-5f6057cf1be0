import React from "react";
import { <PERSON>, <PERSON>Header, CardBody } from "@heroui/react";
import { FileGrid } from "./FileGrid";
import { FileList } from "./FileList";
import type { FileItem, FolderItem } from "../types";

interface FileExplorerProps {
  files: FileItem[];
  folders: FolderItem[];
  searchQuery: string;
  onContextMenu: (e: React.MouseEvent, item: FileItem | FolderItem) => void;
  onItemClick: (item: FileItem | FolderItem) => void;
  onAccessSettings: (item: FileItem | FolderItem) => void;
  viewMode: "grid" | "list";
}

export const FileExplorer: React.FC<FileExplorerProps> = ({
  files,
  folders,
  searchQuery,
  onContextMenu,
  onItemClick,
  onAccessSettings,
  viewMode
}) => {
  // Filter files and folders based on search query
  const filteredFolders = folders.filter(folder => 
    folder.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  const filteredFiles = files.filter(file => 
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-1">
      {viewMode === "grid" ? (
        <FileGrid
          files={filteredFiles}
          folders={filteredFolders}
          onContextMenu={onContextMenu}
          onItemClick={onItemClick}
          onAccessSettings={onAccessSettings}
        />
      ) : (
        <FileList
          files={filteredFiles}
          folders={filteredFolders}
          onContextMenu={onContextMenu}
          onItemClick={onItemClick}
          onAccessSettings={onAccessSettings}
        />
      )}
    </div>
  );
};