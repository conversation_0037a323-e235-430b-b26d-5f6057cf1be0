import React from "react";
    import { <PERSON>, <PERSON>Header, TableColumn, TableBody, TableRow, TableCell, Button, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import type { FileItem, FolderItem } from "../types";

    interface FileListProps {
      files: FileItem[];
      folders: FolderItem[];
      onContextMenu: (e: React.MouseEvent, item: FileItem | FolderItem) => void;
      onItemClick: (item: FileItem | FolderItem) => void;
      onAccessSettings: (item: FileItem | FolderItem) => void;
    }

    export const FileList: React.FC<FileListProps> = ({
      files,
      folders,
      onContextMenu,
      onItemClick,
      onAccessSettings
    }) => {
      // Function to get icon based on file type
      const getFileIcon = (fileType: string): string => {
        switch (fileType) {
          case "image":
            return "lucide:image";
          case "document":
            return "lucide:file-text";
          case "video":
            return "lucide:video";
          case "audio":
            return "lucide:music";
          case "pdf":
            return "lucide:file";
          default:
            return "lucide:file";
        }
      };

      const getFileIconColor = (fileType: string): string => {
        switch (fileType) {
          case "image":
            return "text-green-500";
          case "document":
            return "text-primary";
          case "video":
            return "text-red-500";
          case "audio":
            return "text-purple-500";
          case "pdf":
            return "text-orange-500";
          default:
            return "text-custom-muted";
        }
      };

      // Combine folders and files for the table
      const items = [
        ...folders.map(folder => ({
          ...folder,
          icon: "lucide:folder",
          iconColor: "text-yellow-500",
          size: `${folder.items} items`,
          lastModified: folder.modifiedAt
        })),
        ...files.map(file => ({
          ...file,
          icon: getFileIcon(file.type),
          iconColor: getFileIconColor(file.type),
          lastModified: file.modifiedAt
        }))
      ];

      return (
        <Table 
          removeWrapper 
          aria-label="Files and folders"
          classNames={{
            table: "min-w-full",
            th: "bg-custom-sidebar border-b border-custom-border",
            td: "border-b border-custom-border"
          }}
        >
          <TableHeader>
            <TableColumn>Name</TableColumn>
            <TableColumn>Owner</TableColumn>
            <TableColumn>Last Modified</TableColumn>
            <TableColumn>Size</TableColumn>
            <TableColumn>Actions</TableColumn>
          </TableHeader>
          <TableBody>
            {items.map(item => (
              <TableRow 
                key={item.id}
                className="hover:bg-custom-border/20 cursor-pointer"
                onContextMenu={(e) => onContextMenu(e, item)}
                onClick={() => onItemClick(item)}
              >
                <TableCell>
                  <div className="flex items-center">
                    <div className={`mr-3 ${item.iconColor}`}>
                      <Icon icon={item.icon} className="text-xl" />
                    </div>
                    <span className="text-white">{item.name}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <span className="text-custom-text">{item.owner}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <span className="text-custom-muted">
                    {new Date(item.lastModified).toLocaleDateString()}
                  </span>
                </TableCell>
                <TableCell>
                  <span className="text-custom-muted">{item.size}</span>
                </TableCell>
                <TableCell>
                  <Dropdown>
                    <DropdownTrigger>
                      <Button 
                        isIconOnly 
                        variant="light" 
                        size="sm" 
                        className="text-custom-muted"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Icon icon="lucide:more-vertical" className="text-sm" />
                      </Button>
                    </DropdownTrigger>
                    <DropdownMenu aria-label="Item actions">
                      <DropdownItem 
                        key="rename" 
                        startContent={<Icon icon="lucide:edit" />}
                      >
                        Rename
                      </DropdownItem>
                      <DropdownItem 
                        key="access" 
                        startContent={<Icon icon="lucide:lock" />}
                        onPress={() => {
                          onAccessSettings(item);
                          return false; // Prevent closing the dropdown
                        }}
                      >
                        Access Settings
                      </DropdownItem>
                      <DropdownItem 
                        key="delete" 
                        startContent={<Icon icon="lucide:trash" />}
                        className="text-danger"
                      >
                        Delete
                      </DropdownItem>
                    </DropdownMenu>
                  </Dropdown>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      );
    };