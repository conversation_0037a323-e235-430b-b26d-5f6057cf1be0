import React from "react";
    import { But<PERSON>, Dropdown, Dropdown<PERSON>rigger, DropdownMenu, DropdownItem } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import type { FileItem, FolderItem } from "../types";

    interface FileGridProps {
      files: FileItem[];
      folders: FolderItem[];
      onContextMenu: (e: React.MouseEvent, item: FileItem | FolderItem) => void;
      onItemClick: (item: FileItem | FolderItem) => void;
      onAccessSettings: (item: FileItem | FolderItem) => void;
    }

    export const FileGrid: React.FC<FileGridProps> = ({
      files,
      folders,
      onContextMenu,
      onItemClick,
      onAccessSettings
    }) => {
      // Function to get icon based on file type
      const getFileIcon = (fileType: string): string => {
        switch (fileType) {
          case "image":
            return "lucide:image";
          case "document":
            return "lucide:file-text";
          case "video":
            return "lucide:video";
          case "audio":
            return "lucide:music";
          case "pdf":
            return "lucide:file";
          default:
            return "lucide:file";
        }
      };

      const getFileIconColor = (fileType: string): string => {
        switch (fileType) {
          case "image":
            return "text-green-500";
          case "document":
            return "text-primary";
          case "video":
            return "text-red-500";
          case "audio":
            return "text-purple-500";
          case "pdf":
            return "text-orange-500";
          default:
            return "text-custom-muted";
        }
      };

      const handleTouchStart = (item: FileItem | FolderItem) => {
        // Store the item for potential long press
        let longPressTimer = setTimeout(() => {
          // Create a custom event that simulates right-click
          const event = new MouseEvent("contextmenu", {
            bubbles: true,
            cancelable: true,
            view: window
          }) as any;
          
          // Get the element from the DOM
          const element = document.getElementById(`file-${item.id}`);
          if (element) {
            element.dispatchEvent(event);
          }
        }, 500); // 500ms for long press

        // Store the timer in the element's data attribute to clear it on touch end
        return longPressTimer;
      };

      return (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 p-0">
          {/* Folders */}
          {folders.map(folder => (
            <div
              key={folder.id}
              id={`file-${folder.id}`}
              className="relative bg-custom-sidebar rounded-lg p-4 flex flex-col items-center cursor-pointer hover:bg-custom-border/30 transition-colors"
              onClick={() => onItemClick(folder)}
              onContextMenu={(e) => onContextMenu(e, folder)}
              onTouchStart={() => {
                const timer = handleTouchStart(folder);
                return () => clearTimeout(timer);
              }}
            >
              <div className="mb-3 text-yellow-500 flex items-center justify-center w-16 h-16">
                <Icon icon="lucide:folder" className="text-5xl" />
              </div>
              <p className="text-white text-sm font-medium text-center break-all">
                {folder.name}
              </p>
              <p className="text-custom-muted text-xs mt-1">
                {folder.items} items
              </p>
            </div>
          ))}
          
          {/* Files */}
          {files.map(file => (
            <div
              key={file.id}
              id={`file-${file.id}`}
              className="relative bg-custom-sidebar rounded-lg p-4 flex flex-col items-center cursor-pointer hover:bg-custom-border/30 transition-colors"
              onClick={() => onItemClick(file)}
              onContextMenu={(e) => onContextMenu(e, file)}
              onTouchStart={() => {
                const timer = handleTouchStart(file);
                return () => clearTimeout(timer);
              }}
            >
              <div className={`mb-3 ${getFileIconColor(file.type)} flex items-center justify-center w-16 h-16`}>
                <Icon icon={getFileIcon(file.type)} className="text-5xl" />
              </div>
              <p className="text-white text-sm font-medium text-center break-all">
                {file.name}
              </p>
              <p className="text-custom-muted text-xs mt-1">
                {file.size}
              </p>
            </div>
          ))}
        </div>
      );
    };