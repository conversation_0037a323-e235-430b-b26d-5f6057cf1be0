import React, { useEffect, useRef, useState } from "react";
    import { Icon } from "@iconify/react";
    import type { FileItem, FolderItem } from "../types";
import { <PERSON><PERSON>, <PERSON>dalContent, <PERSON><PERSON><PERSON>eader, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Input } from "@heroui/react";

    interface FileContextMenuProps {
      visible: boolean;
      x: number;
      y: number;
      item: FileItem | FolderItem | null;
      onAction: (action: string) => void;
    }

    export const FileContextMenu: React.FC<FileContextMenuProps> = ({
      visible,
      x,
      y,
      item,
      onAction
    }) => {
  const [copyModalOpen, setCopyModalOpen] = useState(false);
  const [destination, setDestination] = useState("");
      const menuRef = useRef<HTMLDivElement>(null);

      // Adjust position if menu would go off screen
      const adjustedPosition = () => {
        if (!menuRef.current) return { x, y };
        
        const { width, height } = menuRef.current.getBoundingClientRect();
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        
        let adjustedX = x;
        let adjustedY = y;
        
        if (x + width > windowWidth) {
          adjustedX = windowWidth - width - 10;
        }
        
        if (y + height > windowHeight) {
          adjustedY = windowHeight - height - 10;
        }
        
        return { x: adjustedX, y: adjustedY };
      };
      
  if (!visible && !copyModalOpen) return null;
      
      const position = adjustedPosition();

      return (
        <div
          ref={menuRef}
          className="absolute bg-custom-card border border-custom-border rounded-lg shadow-lg z-50 py-1 min-w-[180px]"
          style={{
            left: `${position.x}px`,
            top: `${position.y}px`
          }}
        >
          <div className="px-3 py-2 border-b border-custom-border">
            <p className="text-white text-xs font-medium truncate">{item.name}</p>
          </div>
          
          <div className="py-1">
            <button
              onClick={() => onAction("open")}
              className="w-full text-left px-3 py-2 hover:bg-custom-border/30 flex items-center text-custom-text"
            >
              <Icon icon={item.type === "folder" ? "lucide:folder-open" : "lucide:file"} className="mr-2" />
              Open
            </button>
            
            <button
              onClick={() => onAction("rename")}
              className="w-full text-left px-3 py-2 hover:bg-custom-border/30 flex items-center text-custom-text"
            >
              <Icon icon="lucide:edit" className="mr-2" />
              Rename
            </button>
            
            <button
              onClick={() => onAction("access")}
              className="w-full text-left px-3 py-2 hover:bg-custom-border/30 flex items-center text-custom-text"
            >
              <Icon icon="lucide:lock" className="mr-2" />
              Access Settings
            </button>
            
            <div className="border-t border-custom-border my-1"></div>
            
            <button
          onClick={(e) => {
            e.stopPropagation();
            onAction("startCopy");
            setDestination("");
            setCopyModalOpen(true);
          }}
              className="w-full text-left px-3 py-2 hover:bg-custom-border/30 flex items-center text-custom-text"
            >
              <Icon icon="lucide:copy" className="mr-2" />
              Copy
            </button>
            
            <div className="border-t border-custom-border my-1"></div>
            
            <button
              onClick={() => onAction("delete")}
              className="w-full text-left px-3 py-2 hover:bg-custom-border/30 flex items-center text-danger"
            >
              <Icon icon="lucide:trash" className="mr-2" />
              Delete
            </button>
          </div>
      {copyModalOpen && (
        <Modal isOpen={copyModalOpen} onClose={() => setCopyModalOpen(false)} classNames={{ base: "bg-custom-card text-white" }}>
          <ModalContent>
            {(onCloseModal) => (
              <>
                <ModalHeader>Copy "{item.name}"</ModalHeader>
                <ModalBody>
                  <Input
                    label="Destination Path"
                    placeholder="/path/to/destination/"
                    value={destination}
                    onChange={(e) => setDestination(e.target.value)}
                    variant="bordered"
                    classNames={{ inputWrapper: "bg-custom-sidebar border-custom-border" }}
                  />
                </ModalBody>
                <ModalFooter>
                  <Button variant="bordered" onPress={onCloseModal}>Cancel</Button>
                  <Button color="primary" onPress={() => { onAction("copy"); onCloseModal(); }}>Copy</Button>
                </ModalFooter>
              </>
            )}
          </ModalContent>
        </Modal>
      )}
        </div>
      );
    };