import React, { useState, useEffect } from "react";
    import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Input, Switch, Avatar, Chip, Divider } from "@heroui/react";
    import { Icon } from "@iconify/react";
    import type { FileItem, FolderItem, User, VisibilityMode } from "../types";

    interface FilePermissionsModalProps {
      isOpen: boolean;
      onClose: () => void;
      item: FileItem | FolderItem | null;
      onUpdatePermissions: (itemId: string, itemType: string, mode: VisibilityMode, users: string[]) => void;
      onGenerateShareLink: (itemId: string, itemType: string) => string;
      onDisableShareLink: (itemId: string, itemType: string) => void;
    }

    // Mock users data
    const mockUsers: User[] = [
      { id: "user1", name: "<PERSON>", avatar: "https://img.heroui.chat/image/avatar?w=40&h=40&u=user1" },
      { id: "user2", name: "<PERSON>", avatar: "https://img.heroui.chat/image/avatar?w=40&h=40&u=user2" },
      { id: "user3", name: "<PERSON>", avatar: "https://img.heroui.chat/image/avatar?w=40&h=40&u=user3" },
      { id: "user4", name: "Morgan Lee", avatar: "https://img.heroui.chat/image/avatar?w=40&h=40&u=user4" },
      { id: "user5", name: "Jordan Rivera", avatar: "https://img.heroui.chat/image/avatar?w=40&h=40&u=user5" },
      { id: "user6", name: "Casey Nguyen", avatar: "https://img.heroui.chat/image/avatar?w=40&h=40&u=user6" },
    ];

    export const FilePermissionsModal: React.FC<FilePermissionsModalProps> = ({
      isOpen,
      onClose,
      item,
      onUpdatePermissions,
      onGenerateShareLink,
      onDisableShareLink
    }) => {
      const [itemName, setItemName] = useState("");
      const [visibilityMode, setVisibilityMode] = useState<VisibilityMode>("none-except");
      const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
      const [searchQuery, setSearchQuery] = useState("");
      const [linkEnabled, setLinkEnabled] = useState(false);
      const [shareableLink, setShareableLink] = useState("");
      const [linkCopied, setLinkCopied] = useState(false);

      // Reset state when modal opens with new item
      useEffect(() => {
        if (item) {
          setItemName(item.name);
          setVisibilityMode(item.visibilityMode || "none-except");
          setSelectedUsers(item.allowedUsers || []);
          setLinkEnabled(!!item.shareLink);
          setShareableLink(item.shareLink || "");
        }
      }, [item, isOpen]);

      // Filter users based on search
      const filteredUsers = mockUsers.filter(user => 
        user.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !selectedUsers.includes(user.id)
      );

      // Handle visibility mode change
      const handleVisibilityModeChange = (mode: VisibilityMode) => {
        setVisibilityMode(mode);
      };

      // Handle user selection
      const handleUserSelect = (userId: string) => {
        setSelectedUsers([...selectedUsers, userId]);
        setSearchQuery("");
      };

      // Handle user removal
      const handleUserRemove = (userId: string) => {
        setSelectedUsers(selectedUsers.filter(id => id !== userId));
      };

      // Handle link toggle
      const handleLinkToggle = (enabled: boolean) => {
        setLinkEnabled(enabled);
        
        if (enabled && item) {
          const link = onGenerateShareLink(item.id, item.type);
          setShareableLink(link);
        } else if (!enabled && item) {
          onDisableShareLink(item.id, item.type);
          setShareableLink("");
        }
      };

      // Handle copy link
      const handleCopyLink = () => {
        navigator.clipboard.writeText(shareableLink);
        setLinkCopied(true);
        setTimeout(() => setLinkCopied(false), 2000);
      };

      // Handle save
      const handleSave = () => {
        if (item) {
          onUpdatePermissions(item.id, item.type, visibilityMode, selectedUsers);
        }
        onClose();
      };

      return (
        <Modal 
          isOpen={isOpen} 
          onClose={onClose}
          size="lg"
          classNames={{
            base: "bg-custom-card text-white",
            header: "border-b border-custom-border",
            footer: "border-t border-custom-border"
          }}
        >
          <ModalContent>
            {(onClose) => (
              <>
                <ModalHeader className="flex flex-col gap-1">
                  Access Settings {item && `for ${item.type === 'folder' ? 'Folder' : 'File'}`}
                </ModalHeader>
                <ModalBody className="max-h-[70vh] overflow-y-auto custom-scrollbar">
                  {item && (
                    <>
                      <div className="mb-4">
                        <Input
                          label="Name"
                          value={itemName}
                          onChange={(e) => setItemName(e.target.value)}
                          variant="bordered"
                          classNames={{
                            inputWrapper: "bg-custom-sidebar border-custom-border"
                          }}
                        />
                      </div>
                      
                      <Divider className="my-4" />
                      
                      <div className="mb-4">
                        <h3 className="text-white font-medium mb-2">🔒 Visibility</h3>
                        
                        <div className="flex flex-col gap-3">
                          <div 
                            className={`p-3 rounded-lg border ${
                              visibilityMode === 'all-except' ? 'border-primary bg-primary/10' : 'border-custom-border bg-custom-sidebar'
                            } cursor-pointer`}
                            onClick={() => handleVisibilityModeChange('all-except')}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <div className={`w-5 h-5 rounded-full mr-2 flex items-center justify-center ${
                                  visibilityMode === 'all-except' ? 'bg-primary' : 'bg-custom-border'
                                }`}>
                                  {visibilityMode === 'all-except' && <Icon icon="lucide:check" className="text-white text-xs" />}
                                </div>
                                <span className="font-medium">Visible to everyone except</span>
                              </div>
                            </div>
                            <p className="text-custom-muted text-sm mt-1 pl-7">
                              All users can see this {item.type}, except for specific users you exclude.
                            </p>
                          </div>
                          
                          <div 
                            className={`p-3 rounded-lg border ${
                              visibilityMode === 'none-except' ? 'border-primary bg-primary/10' : 'border-custom-border bg-custom-sidebar'
                            } cursor-pointer`}
                            onClick={() => handleVisibilityModeChange('none-except')}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <div className={`w-5 h-5 rounded-full mr-2 flex items-center justify-center ${
                                  visibilityMode === 'none-except' ? 'bg-primary' : 'bg-custom-border'
                                }`}>
                                  {visibilityMode === 'none-except' && <Icon icon="lucide:check" className="text-white text-xs" />}
                                </div>
                                <span className="font-medium">Not visible to anyone except</span>
                              </div>
                            </div>
                            <p className="text-custom-muted text-sm mt-1 pl-7">
                              No users can see this {item.type}, except for specific users you include.
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="mb-4">
                        <h3 className="text-white font-medium mb-2">
                          {visibilityMode === 'all-except' ? 'Excluded Users' : 'Included Users'}
                        </h3>
                        
                        <Input
                          placeholder="Search users..."
                          value={searchQuery}
                          onValueChange={setSearchQuery}
                          startContent={<Icon icon="lucide:search" className="text-custom-muted" />}
                          variant="bordered"
                          classNames={{
                            inputWrapper: "bg-custom-sidebar border-custom-border"
                          }}
                        />
                        
                        {searchQuery && filteredUsers.length > 0 && (
                          <div className="mt-2 bg-custom-sidebar border border-custom-border rounded-lg max-h-[160px] overflow-y-auto">
                            {filteredUsers.map(user => (
                              <div 
                                key={user.id}
                                className="flex items-center justify-between p-2 hover:bg-custom-border/30 cursor-pointer"
                                onClick={() => handleUserSelect(user.id)}
                              >
                                <div className="flex items-center">
                                  <Avatar src={user.avatar} size="sm" className="mr-2" />
                                  <span className="text-white text-sm">{user.name}</span>
                                </div>
                                <Button 
                                  isIconOnly 
                                  size="sm" 
                                  variant="flat" 
                                  className="text-custom-muted"
                                >
                                  <Icon icon="lucide:plus" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        )}
                        
                        {selectedUsers.length > 0 && (
                          <div className="mt-3 flex flex-wrap gap-2">
                            {selectedUsers.map(userId => {
                              const user = mockUsers.find(u => u.id === userId);
                              if (!user) return null;
                              
                              return (
                                <Chip 
                                  key={user.id}
                                  onClose={() => handleUserRemove(user.id)}
                                  avatar={<Avatar src={user.avatar} size="sm" />}
                                  variant="flat"
                                  color="default"
                                  classNames={{
                                    base: "bg-custom-sidebar border border-custom-border",
                                    content: "text-white"
                                  }}
                                >
                                  {user.name}
                                </Chip>
                              );
                            })}
                          </div>
                        )}
                      </div>
                      
                      <Divider className="my-4" />
                      
                      <div>
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="text-white font-medium">🔗 Shareable Link</h3>
                          <Switch 
                            isSelected={linkEnabled}
                            onValueChange={handleLinkToggle}
                            color="primary"
                            size="sm"
                          />
                        </div>
                        
                        {linkEnabled && shareableLink && (
                          <div className="relative">
                            <Input
                              value={shareableLink}
                              isReadOnly
                              variant="bordered"
                              classNames={{
                                inputWrapper: "bg-custom-sidebar border-custom-border pr-20"
                              }}
                            />
                            <Button
                              size="sm"
                              variant="flat"
                              color={linkCopied ? "success" : "primary"}
                              className="absolute right-1 top-1/2 transform -translate-y-1/2"
                              onPress={handleCopyLink}
                            >
                              {linkCopied ? (
                                <>
                                  <Icon icon="lucide:check" className="mr-1" />
                                  Copied
                                </>
                              ) : (
                                <>
                                  <Icon icon="lucide:copy" className="mr-1" />
                                  Copy
                                </>
                              )}
                            </Button>
                          </div>
                        )}
                        
                        {linkEnabled && (
                          <p className="text-custom-muted text-xs mt-2">
                            Anyone with this link can view this {item.type}, regardless of permissions.
                          </p>
                        )}
                      </div>
                    </>
                  )}
                </ModalBody>
                <ModalFooter>
                  <Button variant="bordered" onPress={onClose}>
                    Cancel
                  </Button>
                  <Button color="primary" onPress={handleSave}>
                    Save Changes
                  </Button>
                </ModalFooter>
              </>
            )}
          </ModalContent>
        </Modal>
      );
    };