export type FileSection = "my-files" | "shared" | "recent" | "categories";

    export type VisibilityMode = "all-except" | "none-except";

    export interface FileItem {
      id: string;
      name: string;
      type: string;
      path: string;
      section: FileSection;
      size: string;
      owner: string;
      modifiedAt: string;
      visibilityMode?: VisibilityMode;
      allowedUsers?: string[];
      shareLink?: string;
    }

    export interface FolderItem {
      id: string;
      name: string;
      type: "folder";
      path: string;
      section: FileSection;
      items: number;
      owner: string;
      modifiedAt: string;
      visibilityMode?: VisibilityMode;
      allowedUsers?: string[];
      shareLink?: string;
    }

    export interface User {
      id: string;
      name: string;
      avatar: string;
    }