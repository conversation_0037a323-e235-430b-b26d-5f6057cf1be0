import { useState, useEffect } from "react";
    import type { FileItem, FolderItem, FileSection, VisibilityMode } from "../types";
    import { mockFiles, mockFolders } from "../data/mock-data";

    export const useFileManager = (section: FileSection) => {
      const [files, setFiles] = useState<FileItem[]>([]);
      const [folders, setFolders] = useState<FolderItem[]>([]);
      const [currentPath, setCurrentPath] = useState("/");
      
      // Load files and folders based on section and current path
      useEffect(() => {
        // In a real app, this would fetch from an API
        // For this example, we'll filter the mock data based on section and path
        
        const filteredFolders = mockFolders.filter(folder => 
          folder.section === section && folder.path === currentPath
        );
        
        const filteredFiles = mockFiles.filter(file => 
          file.section === section && file.path === currentPath
        );
        
        setFolders(filteredFolders);
        setFiles(filteredFiles);
      }, [section, currentPath]);
      
      // Navigate to a specific path
      const navigateTo = (path: string) => {
        setCurrentPath(path);
      };
      
      // Create a new folder
      const createFolder = (name: string) => {
        const newFolder: FolderItem = {
          id: `folder-${Date.now()}`,
          name,
          type: "folder",
          path: currentPath,
          section,
          items: 0,
          owner: "Current User",
          modifiedAt: new Date().toISOString(),
          visibilityMode: "none-except",
          allowedUsers: [],
          shareLink: ""
        };
        
        setFolders([...folders, newFolder]);
      };
      
      // Delete an item (file or folder)
      const deleteItem = (id: string, type: string) => {
        if (type === "file") {
          setFiles(files.filter(file => file.id !== id));
        } else {
          setFolders(folders.filter(folder => folder.id !== id));
        }
      };
      
      // Rename an item
      const renameItem = (id: string, type: string, newName: string) => {
        if (type === "file") {
          setFiles(files.map(file => 
            file.id === id ? { ...file, name: newName } : file
          ));
        } else {
          setFolders(folders.map(folder => 
            folder.id === id ? { ...folder, name: newName } : folder
          ));
        }
      };
      
      // Generate a shareable link
      const generateShareLink = (id: string, type: string): string => {
        const link = `https://app.example.com/shared/${type}/${id}?token=${Math.random().toString(36).substring(2, 15)}`;
        
        if (type === "file") {
          setFiles(files.map(file => 
            file.id === id ? { ...file, shareLink: link } : file
          ));
        } else {
          setFolders(folders.map(folder => 
            folder.id === id ? { ...folder, shareLink: link } : folder
          ));
        }
        
        return link;
      };
      
      // Disable a shareable link
      const disableShareLink = (id: string, type: string) => {
        if (type === "file") {
          setFiles(files.map(file => 
            file.id === id ? { ...file, shareLink: "" } : file
          ));
        } else {
          setFolders(folders.map(folder => 
            folder.id === id ? { ...folder, shareLink: "" } : folder
          ));
        }
      };
      
      // Update permissions for an item
      const updatePermissions = (id: string, type: string, mode: VisibilityMode, users: string[]) => {
        if (type === "file") {
          setFiles(files.map(file => 
            file.id === id ? { ...file, visibilityMode: mode, allowedUsers: users } : file
          ));
        } else {
          setFolders(folders.map(folder => 
            folder.id === id ? { ...folder, visibilityMode: mode, allowedUsers: users } : folder
          ));
        }
      };
      
      return {
        files,
        folders,
        currentPath,
        navigateTo,
        createFolder,
        deleteItem,
        renameItem,
        generateShareLink,
        disableShareLink,
        updatePermissions
      };
    };