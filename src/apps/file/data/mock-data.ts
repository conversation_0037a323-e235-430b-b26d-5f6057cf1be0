import type { FileItem, FolderItem } from "../types";

    // Mock folders data
    export const mockFolders: FolderItem[] = [
      {
        id: "folder-1",
        name: "Project Documents",
        type: "folder",
        path: "/",
        section: "my-files",
        items: 5,
        owner: "Current User",
        modifiedAt: "2023-06-15T14:30:00Z",
        visibilityMode: "none-except",
        allowedUsers: ["user1", "user3"],
        shareLink: ""
      },
      {
        id: "folder-2",
        name: "Design Assets",
        type: "folder",
        path: "/",
        section: "my-files",
        items: 12,
        owner: "Current User",
        modifiedAt: "2023-07-22T09:45:00Z",
        visibilityMode: "all-except",
        allowedUsers: ["user4"],
        shareLink: "https://app.example.com/shared/folder/folder-2?token=abc123"
      },
      {
        id: "folder-3",
        name: "Team Resources",
        type: "folder",
        path: "/",
        section: "shared",
        items: 8,
        owner: "<PERSON>",
        modifiedAt: "2023-08-05T16:20:00Z",
        visibilityMode: "all-except",
        allowedUsers: [],
        shareLink: ""
      },
      {
        id: "folder-4",
        name: "Marketing Materials",
        type: "folder",
        path: "/",
        section: "shared",
        items: 4,
        owner: "<PERSON>",
        modifiedAt: "2023-08-12T11:15:00Z",
        visibilityMode: "none-except",
        allowedUsers: ["user1", "user2", "user3"],
        shareLink: ""
      }
    ];

    // Mock files data
    export const mockFiles: FileItem[] = [
      {
        id: "file-1",
        name: "Project Proposal.docx",
        type: "document",
        path: "/",
        section: "my-files",
        size: "2.4 MB",
        owner: "Current User",
        modifiedAt: "2023-08-18T13:40:00Z",
        visibilityMode: "none-except",
        allowedUsers: ["user1"],
        shareLink: ""
      },
      {
        id: "file-2",
        name: "Budget.xlsx",
        type: "document",
        path: "/",
        section: "my-files",
        size: "1.8 MB",
        owner: "Current User",
        modifiedAt: "2023-08-20T10:30:00Z",
        visibilityMode: "all-except",
        allowedUsers: ["user4"],
        shareLink: "https://app.example.com/shared/file/file-2?token=def456"
      },
      {
        id: "file-3",
        name: "Logo Design.png",
        type: "image",
        path: "/",
        section: "my-files",
        size: "5.6 MB",
        owner: "Current User",
        modifiedAt: "2023-08-21T09:15:00Z",
        visibilityMode: "none-except",
        allowedUsers: [],
        shareLink: ""
      },
      {
        id: "file-4",
        name: "Presentation.pptx",
        type: "document",
        path: "/",
        section: "shared",
        size: "8.2 MB",
        owner: "Taylor Wilson",
        modifiedAt: "2023-08-19T15:20:00Z",
        visibilityMode: "all-except",
        allowedUsers: [],
        shareLink: ""
      },
      {
        id: "file-5",
        name: "Meeting Recording.mp4",
        type: "video",
        path: "/",
        section: "recent",
        size: "124.5 MB",
        owner: "Current User",
        modifiedAt: "2023-08-22T16:45:00Z",
        visibilityMode: "none-except",
        allowedUsers: ["user1", "user2"],
        shareLink: ""
      },
      {
        id: "file-6",
        name: "Project Brief.pdf",
        type: "pdf",
        path: "/",
        section: "recent",
        size: "3.1 MB",
        owner: "Current User",
        modifiedAt: "2023-08-22T14:10:00Z",
        visibilityMode: "all-except",
        allowedUsers: [],
        shareLink: ""
      }
    ];