import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ody, Checkbox, Chip, Input, Button } from "@heroui/react";
import { Icon } from "@iconify/react";

export const TaskApp = () => {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-white text-2xl font-semibold">Task & Request System</h1>
        <div className="flex items-center">
          <span className="text-white">Today, 14 November</span>
          <Icon icon="lucide:chevron-down" className="text-custom-text ml-2" />
        </div>
      </div>

      <div className="grid grid-cols-12 gap-6">
        <div className="col-span-12 lg:col-span-8">
          <Card className="bg-custom-card border-custom-border shadow-none">
            <CardHeader className="border-b border-custom-border flex items-center justify-between">
              <h3 className="text-white text-lg font-medium">Current Tasks</h3>
              <div className="flex gap-2 items-center">
                <Input
                  placeholder="Search tasks..."
                  startContent={<Icon icon="lucide:search" className="text-custom-muted" />}
                  classNames={{
                    inputWrapper: "bg-custom-sidebar border-custom-border h-9 w-[200px]",
                  }}
                  radius="lg"
                  variant="bordered"
                />
                <Button color="primary" size="sm" radius="lg" variant="flat">
                  <Icon icon="lucide:plus" className="mr-1" /> New Task
                </Button>
              </div>
            </CardHeader>
            <CardBody className="p-0">
              {[...Array(6)].map((_, index) => {
                const status = index % 3 === 0 ? "In Progress" : index % 3 === 1 ? "Pending" : "Completed";
                const statusColor = index % 3 === 0 ? "warning" : index % 3 === 1 ? "secondary" : "success";
                
                return (
                  <div 
                    key={index} 
                    className="flex items-center p-4 border-b border-custom-border hover:bg-custom-border/10"
                  >
                    <Checkbox 
                      color="primary" 
                      isSelected={status === "Completed"}
                      className="mr-3"
                    />
                    <div className="flex-1">
                      <h4 className={`text-sm font-medium ${status === "Completed" ? "text-custom-muted line-through" : "text-white"}`}>
                        Task {index + 1}: {["Update dashboard UI", "Fix responsive issues", "Implement new features", "Review code changes", "Update documentation", "Test functionality"][index % 6]}
                      </h4>
                      <p className="text-custom-muted text-xs">Due in {index + 1} days</p>
                    </div>
                    <Chip color={statusColor} variant="flat" className="mr-3" size="sm">
                      {status}
                    </Chip>
                    <Button isIconOnly variant="light" className="text-custom-muted">
                      <Icon icon="lucide:more-vertical" />
                    </Button>
                  </div>
                );
              })}
            </CardBody>
          </Card>
        </div>
        
        <div className="col-span-12 lg:col-span-4">
          <Card className="bg-custom-card border-custom-border shadow-none mb-6">
            <CardHeader className="border-b border-custom-border">
              <h3 className="text-white text-base font-medium">Leave Requests</h3>
            </CardHeader>
            <CardBody className="p-0">
              {[...Array(3)].map((_, index) => (
                <div 
                  key={index} 
                  className="p-4 border-b border-custom-border"
                >
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="text-white text-sm font-medium">
                      {["Vacation Leave", "Sick Leave", "Personal Leave"][index % 3]}
                    </h4>
                    <Chip color="secondary" variant="flat" size="sm">Pending</Chip>
                  </div>
                  <p className="text-custom-muted text-xs mb-3">
                    {index + 1} {index === 0 ? "day" : "days"} • {["Nov 20-21", "Dec 5", "Dec 10-11"][index % 3]}
                  </p>
                  <div className="flex gap-2">
                    <Button size="sm" color="success" variant="flat" className="flex-1">
                      Approve
                    </Button>
                    <Button size="sm" color="danger" variant="flat" className="flex-1">
                      Decline
                    </Button>
                  </div>
                </div>
              ))}
            </CardBody>
          </Card>
          
          <Card className="bg-custom-card border-custom-border shadow-none">
            <CardHeader className="border-b border-custom-border">
              <h3 className="text-white text-base font-medium">Loan Requests</h3>
            </CardHeader>
            <CardBody className="p-0">
              {[...Array(2)].map((_, index) => (
                <div 
                  key={index} 
                  className="p-4 border-b border-custom-border"
                >
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="text-white text-sm font-medium">
                      ${["1,500", "3,000"][index % 2]} Loan Request
                    </h4>
                    <Chip color="secondary" variant="flat" size="sm">Pending</Chip>
                  </div>
                  <p className="text-custom-muted text-xs mb-3">
                    Requested on {["Nov 15", "Dec 1"][index % 2]} • {["6 months", "12 months"][index % 2]} plan
                  </p>
                  <div className="flex gap-2">
                    <Button size="sm" color="success" variant="flat" className="flex-1">
                      Approve
                    </Button>
                    <Button size="sm" color="danger" variant="flat" className="flex-1">
                      Decline
                    </Button>
                  </div>
                </div>
              ))}
            </CardBody>
          </Card>
        </div>
      </div>
    </div>
  );
};