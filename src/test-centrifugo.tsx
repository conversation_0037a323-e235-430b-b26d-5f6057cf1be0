import React from 'react';
import { useCentrifugo } from './hooks/useCentrifugo';

export const CentrifugoTest: React.FC = () => {
  const {
    status,
    isConnected,
    isConnecting,
    hasError,
    lastMessage,
    messageCount,
    startService,
    stopService,
    getStatus
  } = useCentrifugo();

  return (
    <div className="p-6 bg-gray-900 text-white min-h-screen">
      <h1 className="text-2xl font-bold mb-6">Centrifugo Service Test</h1>
      
      {/* Connection Status */}
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold mb-3">Connection Status</h2>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="text-gray-400">State:</span>
            <span className={`ml-2 px-2 py-1 rounded text-sm ${
              isConnected ? 'bg-green-600' : 
              isConnecting ? 'bg-yellow-600' : 
              hasError ? 'bg-red-600' : 'bg-gray-600'
            }`}>
              {status.state}
            </span>
          </div>
          <div>
            <span className="text-gray-400">User ID:</span>
            <span className="ml-2">{status.user_id || 'N/A'}</span>
          </div>
          <div>
            <span className="text-gray-400">Channel:</span>
            <span className="ml-2 text-sm">{status.channel || 'N/A'}</span>
          </div>
          <div>
            <span className="text-gray-400">Messages:</span>
            <span className="ml-2">{messageCount}</span>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold mb-3">Controls</h2>
        <div className="flex gap-3">
          <button
            onClick={startService}
            disabled={isConnecting}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded"
          >
            {isConnecting ? 'Starting...' : 'Start Service'}
          </button>
          <button
            onClick={stopService}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded"
          >
            Stop Service
          </button>
          <button
            onClick={getStatus}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded"
          >
            Refresh Status
          </button>
        </div>
      </div>

      {/* Last Message */}
      {lastMessage && (
        <div className="mb-6 p-4 bg-gray-800 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Last Message</h2>
          <div className="bg-gray-700 p-3 rounded">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-gray-400">Type:</span>
                <span className="ml-2">{lastMessage.message_type}</span>
              </div>
              <div>
                <span className="text-gray-400">Employee:</span>
                <span className="ml-2">{lastMessage.employee_name}</span>
              </div>
              <div>
                <span className="text-gray-400">Status:</span>
                <span className="ml-2">{lastMessage.status}</span>
              </div>
              <div>
                <span className="text-gray-400">Time:</span>
                <span className="ml-2">{new Date(lastMessage.timestamp * 1000).toLocaleTimeString()}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Debug Info */}
      <div className="p-4 bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold mb-3">Debug Info</h2>
        <pre className="text-xs bg-gray-700 p-3 rounded overflow-auto">
          {JSON.stringify(status, null, 2)}
        </pre>
      </div>
    </div>
  );
};
