// ... existing imports ...
    
    import { TimeTrackingApp } from "../apps/time-tracking/TimeTrackingApp";
    import { TimeTrackingReportsPage } from "../apps/time-tracking/TimeTrackingReportsPage";
    import { AIReportsPage } from "../apps/time-tracking/AIReportsPage";
    import { PulseBoardApp } from "../apps/pulse-board/PulseBoardApp";
    import { ProjectApp } from "../apps/project/ProjectApp";
    import { FileApp } from "../apps/file/FileApp";
    import { ChatApp } from "../apps/chat/ChatApp";
    import { MeetApp } from "../apps/meet/MeetApp";
    import { OrganizationalRequestsApp } from "../apps/requests/OrganizationalRequestsApp";
    import { TaskApp } from "../apps/task/TaskApp";
    import { ProjectsListView } from "../apps/project/ProjectsListView";
    import { KanbanPage } from "../apps/project/pages/KanbanPage";
    
    // ... existing route definitions ...
    
    import { RouteObject } from "react-router-dom";
    
    export const routes: RouteObject[] = [
      {
        path: "/time-tracking",
        element: <TimeTrackingApp />,
        children: [
          { path: "reports", element: <TimeTrackingReportsPage /> },
          { path: "ai-reports", element: <AIReportsPage /> },
        ],
      },
      {
        path: "/pulse-board/*",
        element: <PulseBoardApp />,
      },
      {
        path: "/project",
        children: [
          { path: "projects", element: <ProjectsListView /> },
          { path: "boards", element: <KanbanPage /> },
          { path: "*", element: <ProjectsListView /> },
        ],
      },
      {
        path: "/file/*",
        element: <FileApp />,
      },
      {
        path: "/chat/*",
        element: <ChatApp conversation={null} onSelectConversation={()=>{}} />,
      },
      {
        path: "/meet/*",
        element: <MeetApp call={null} onSelectCall={()=>{}} />,
      },
      {
        path: "/requests/*",
        element: <OrganizationalRequestsApp />,
      },
      {
        path: "/task/*",
        element: <TaskApp />,
      },
    // ... remaining route definitions ...
    ];