import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON><PERSON>rovider } from "@heroui/react"
import { <PERSON>hRout<PERSON> } from "react-router-dom";
import App from './App.tsx'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <HeroUIProvider>
      <HashRouter>
        <App />
      </HashRouter>
    </HeroUIProvider>
  </React.StrictMode>,
)
