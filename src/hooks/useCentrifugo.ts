import { useState, useEffect, useCallback, useRef } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { centrifugoMessageRouter } from '../modules/centrifugo/MessageRouter';

// Centrifugo Types (matching Rust types)
export interface CentrifugoMessage {
  message_type: string;
  timestamp: number;
  employee_id: number;
  employee_name: string;
  status: string;
  screen_active?: boolean; // Optional field for employee_update_screen_view messages
}

export enum CentrifugoConnectionState {
  Disconnected = 'Disconnected',
  Connecting = 'Connecting',
  Connected = 'Connected',
}

export interface CentrifugoStatus {
  state: CentrifugoConnectionState;
  user_id: number | null;
  channel: string | null;
}

export interface UseCentrifugoReturn {
  status: CentrifugoStatus;
  isConnected: boolean;
  isConnecting: boolean;
  hasError: boolean;
  lastMessage: CentrifugoMessage | null;
  messageCount: number;
  startService: () => Promise<void>;
  stopService: () => Promise<void>;
  getStatus: () => Promise<void>;
}

export const useCentrifugo = (): UseCentrifugoReturn => {
  const [status, setStatus] = useState<CentrifugoStatus>({
    state: CentrifugoConnectionState.Disconnected,
    user_id: null,
    channel: null,
  });
  
  const [lastMessage, setLastMessage] = useState<CentrifugoMessage | null>(null);
  const [messageCount, setMessageCount] = useState(0);
  const [isStarting, setIsStarting] = useState(false);
  
  // Refs to prevent duplicate listeners
  const statusListenerRef = useRef<(() => void) | null>(null);
  const messageListenerRef = useRef<(() => void) | null>(null);
  const hasStartedRef = useRef(false);

  // Computed states
  const isConnected = status.state === CentrifugoConnectionState.Connected;
  const isConnecting = status.state === CentrifugoConnectionState.Connecting || isStarting;
  const hasError = status.state === CentrifugoConnectionState.Error;

  // Start Centrifugo service
  const startService = useCallback(async () => {
    if (isStarting || hasStartedRef.current) {
      console.log('🔄 [CENTRIFUGO] Service already starting or started');
      return;
    }

    try {
      setIsStarting(true);
      hasStartedRef.current = true;
      
      console.log('🚀 [CENTRIFUGO] Starting service...');
      await invoke<boolean>('start_centrifugo_service');
      console.log('✅ [CENTRIFUGO] Service start command sent');

      // Start message router
      await centrifugoMessageRouter.startListening();
    } catch (error) {
      console.error('❌ [CENTRIFUGO] Failed to start service:', error);
      hasStartedRef.current = false;
    } finally {
      setIsStarting(false);
    }
  }, [isStarting]);

  // Stop Centrifugo service
  const stopService = useCallback(async () => {
    try {
      console.log('🛑 [CENTRIFUGO] Stopping service...');
      await invoke<boolean>('stop_centrifugo_service');
      hasStartedRef.current = false;
      console.log('✅ [CENTRIFUGO] Service stopped');
    } catch (error) {
      console.error('❌ [CENTRIFUGO] Failed to stop service:', error);
    }
  }, []);

  // Get current status
  const getStatus = useCallback(async () => {
    try {
      const statusData = await invoke<{
        is_running: boolean;
        user_id: number | null;
        channel: string | null;
      }>('get_centrifugo_status');

      console.log('📊 [CENTRIFUGO] Current status:', statusData);

      setStatus({
        state: statusData.is_running ? CentrifugoConnectionState.Connected : CentrifugoConnectionState.Disconnected,
        user_id: statusData.user_id,
        channel: statusData.channel
      });
    } catch (error) {
      console.error('❌ [CENTRIFUGO] Failed to get status:', error);
    }
  }, []);

  // Setup event listeners
  useEffect(() => {
    const setupListeners = async () => {
      // Cleanup existing listeners
      if (statusListenerRef.current) {
        statusListenerRef.current();
        statusListenerRef.current = null;
      }
      if (messageListenerRef.current) {
        messageListenerRef.current();
        messageListenerRef.current = null;
      }

      try {
        // Listen for simple connection status
        const statusUnlisten = await listen<boolean>('centrifugo-connected', (event) => {
          console.log('📊 [CENTRIFUGO] React received connection status:', event.payload);
          const newState = event.payload ? CentrifugoConnectionState.Connected : CentrifugoConnectionState.Disconnected;
          console.log('📊 [CENTRIFUGO] Setting state to:', newState);
          setStatus(prev => ({
            ...prev,
            state: newState
          }));
        });
        statusListenerRef.current = statusUnlisten;

        // Listen for employee status messages
        const messageUnlisten = await listen<CentrifugoMessage>('centrifugo-employee-status', (event) => {
          console.log('📨 [CENTRIFUGO] Employee status message:', event.payload);
          setLastMessage(event.payload);
          setMessageCount(prev => prev + 1);
          
          // Emit custom event for other components to handle
          window.dispatchEvent(new CustomEvent('centrifugo-employee-status', {
            detail: event.payload
          }));
        });
        messageListenerRef.current = messageUnlisten;

        console.log('👂 [CENTRIFUGO] Event listeners setup complete');
      } catch (error) {
        console.error('❌ [CENTRIFUGO] Failed to setup listeners:', error);
      }
    };

    setupListeners();

    // Cleanup on unmount
    return () => {
      if (statusListenerRef.current) {
        statusListenerRef.current();
      }
      if (messageListenerRef.current) {
        messageListenerRef.current();
      }
    };
  }, []);

  // Auto-start service on mount
  useEffect(() => {
    const autoStart = async () => {
      if (!hasStartedRef.current && !isStarting) {
        console.log('🔄 [CENTRIFUGO] Auto-starting service on mount...');
        await startService();
      }
    };

    // Delay to ensure user profile is loaded first
    const timer = setTimeout(autoStart, 2000);
    return () => clearTimeout(timer);
  }, []);

  // Periodic status check
  useEffect(() => {
    const interval = setInterval(() => {
      if (hasStartedRef.current) {
        getStatus();
      }
    }, 10000); // Check every 10 seconds

    return () => clearInterval(interval);
  }, [getStatus]);

  return {
    status,
    isConnected,
    isConnecting,
    hasError,
    lastMessage,
    messageCount,
    startService,
    stopService,
    getStatus,
  };
};
