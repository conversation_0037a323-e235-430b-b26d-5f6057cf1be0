import { useNavigate, useLocation } from "react-router-dom";
import { useApp } from "../context/AppContext";

export const useNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { setActiveApp } = useApp();

  const navigateToApp = (appId: string, subPath?: string) => {
    const basePath = getAppBasePath(appId);
    const fullPath = subPath ? `${basePath}/${subPath}` : basePath;
    
    setActiveApp(appId);
    navigate(fullPath);
  };

  const getAppBasePath = (appId: string): string => {
    const pathMap: Record<string, string> = {
      time: "/time-tracking",
      chat: "/chat",
      project: "/project",
      file: "/file",
      meet: "/meet",
      requests: "/requests",
      pulse: "/pulse-board",
      task: "/task",
    };
    return pathMap[appId] || "/";
  };

  const getCurrentApp = (): string => {
    const path = location.pathname;
    
    if (path.startsWith("/time-tracking")) return "time";
    if (path.startsWith("/chat")) return "chat";
    if (path.startsWith("/project")) return "project";
    if (path.startsWith("/file")) return "file";
    if (path.startsWith("/meet")) return "meet";
    if (path.startsWith("/requests")) return "requests";
    if (path.startsWith("/pulse-board")) return "pulse";
    if (path.startsWith("/task")) return "task";
    
    return "time"; // Default to time tracking
  };

  const getCurrentSection = (): string => {
    const path = location.pathname;
    const segments = path.split("/").filter(Boolean);
    
    if (segments.length > 1) {
      return segments[1]; // Return the section after the app
    }
    
    return "home"; // Default section
  };

  const isCurrentPath = (path: string): boolean => {
    return location.pathname === path;
  };

  const isCurrentApp = (appId: string): boolean => {
    return getCurrentApp() === appId;
  };

  return {
    navigateToApp,
    getCurrentApp,
    getCurrentSection,
    isCurrentPath,
    isCurrentApp,
    navigate,
    location,
  };
};
