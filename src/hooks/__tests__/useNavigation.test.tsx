import { describe, it, expect, vi } from 'vitest'
import { renderHook } from '@testing-library/react'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { useNavigation } from '../useNavigation'
import { AppProvider } from '../../context/AppContext'

// Mock react-router-dom
const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({ pathname: '/time-tracking' }),
  }
})

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>
    <AppProvider>
      {children}
    </AppProvider>
  </BrowserRouter>
)

describe('useNavigation', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('provides navigation functions', () => {
    const { result } = renderHook(() => useNavigation(), { wrapper })
    
    expect(result.current.navigateToApp).toBeDefined()
    expect(result.current.getCurrentApp).toBeDefined()
    expect(result.current.getCurrentSection).toBeDefined()
    expect(result.current.isCurrentPath).toBeDefined()
    expect(result.current.isCurrentApp).toBeDefined()
    expect(result.current.navigate).toBeDefined()
    expect(result.current.location).toBeDefined()
  })

  it('navigates to app correctly', () => {
    const { result } = renderHook(() => useNavigation(), { wrapper })
    
    result.current.navigateToApp('chat')
    
    expect(mockNavigate).toHaveBeenCalledWith('/chat')
  })

  it('navigates to app with subpath', () => {
    const { result } = renderHook(() => useNavigation(), { wrapper })
    
    result.current.navigateToApp('time', 'reports')
    
    expect(mockNavigate).toHaveBeenCalledWith('/time-tracking/reports')
  })

  it('gets correct app base path', () => {
    const { result } = renderHook(() => useNavigation(), { wrapper })
    
    // Test different app IDs
    expect(result.current.navigateToApp).toBeDefined()
    
    // We can't directly test getAppBasePath as it's internal,
    // but we can test it through navigateToApp
    result.current.navigateToApp('project')
    expect(mockNavigate).toHaveBeenCalledWith('/project')
    
    result.current.navigateToApp('pulse')
    expect(mockNavigate).toHaveBeenCalledWith('/pulse-board')
  })

  it('gets current app from path', () => {
    const { result } = renderHook(() => useNavigation(), { wrapper })
    
    // Mock location is set to '/time-tracking'
    expect(result.current.getCurrentApp()).toBe('time')
  })

  it('returns default app for unknown paths', () => {
    // Mock a different location
    vi.mocked(vi.importActual('react-router-dom')).useLocation = () => ({ pathname: '/unknown' })
    
    const { result } = renderHook(() => useNavigation(), { wrapper })
    
    expect(result.current.getCurrentApp()).toBe('time')
  })

  it('gets current section from path', () => {
    // Mock location with section
    vi.mocked(vi.importActual('react-router-dom')).useLocation = () => ({ pathname: '/time-tracking/reports' })
    
    const { result } = renderHook(() => useNavigation(), { wrapper })
    
    expect(result.current.getCurrentSection()).toBe('reports')
  })

  it('returns default section for paths without section', () => {
    const { result } = renderHook(() => useNavigation(), { wrapper })
    
    expect(result.current.getCurrentSection()).toBe('home')
  })

  it('checks if current path matches', () => {
    const { result } = renderHook(() => useNavigation(), { wrapper })
    
    expect(result.current.isCurrentPath('/time-tracking')).toBe(true)
    expect(result.current.isCurrentPath('/chat')).toBe(false)
  })

  it('checks if current app matches', () => {
    const { result } = renderHook(() => useNavigation(), { wrapper })
    
    expect(result.current.isCurrentApp('time')).toBe(true)
    expect(result.current.isCurrentApp('chat')).toBe(false)
  })
})
