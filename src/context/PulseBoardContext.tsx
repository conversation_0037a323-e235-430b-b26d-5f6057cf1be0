import React, { createContext, useContext, useState } from "react";

// Types for PulseBoard
export interface Challenge {
  id: string;
  title: string;
  description: string;
  points: number;
  category: string;
  difficulty: "easy" | "medium" | "hard";
  completed: boolean;
  completedAt?: string;
  progress: number;
  maxProgress: number;
}

export interface Kudos {
  id: string;
  from: string;
  to: string;
  count: number;
  month: string;
  date: string;
  message?: string;
}

export interface Recommendation {
  id: string;
  from: string;
  to: string;
  type: "skill" | "project" | "general";
  title: string;
  description: string;
  date: string;
}

export interface Month {
  id: string;
  name: string;
  year: number;
  isActive: boolean;
}

export interface User {
  id: string;
  name: string;
  avatar: string;
  role: string;
  department: string;
  email: string;
}

interface PulseBoardContextType {
  // Data
  challenges: Challenge[];
  kudos: Kudos[];
  recommendations: Recommendation[];
  months: Month[];
  users: User[];
  currentUser: User;
  
  // Current state
  currentMonth: string;
  remainingKudos: number;
  
  // Actions
  giveKudos: (to: string, count: number, message?: string) => void;
  addRecommendation: (recommendation: Omit<Recommendation, 'id' | 'date'>) => void;
  completeChallenge: (challengeId: string) => void;
  
  // Modals
  isKudosModalOpen: boolean;
  setKudosModalOpen: (open: boolean) => void;
  isRecommendationModalOpen: boolean;
  setRecommendationModalOpen: (open: boolean) => void;
}

// Mock data
const mockUsers: User[] = [
  {
    id: 'user1',
    name: 'TeamBy User',
    avatar: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=1',
    role: 'UI/UX Designer',
    department: 'Design',
    email: '<EMAIL>'
  },
  {
    id: 'user2',
    name: 'John Doe',
    avatar: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=2',
    role: 'Frontend Developer',
    department: 'Engineering',
    email: '<EMAIL>'
  },
  {
    id: 'user3',
    name: 'Jane Smith',
    avatar: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=3',
    role: 'Backend Developer',
    department: 'Engineering',
    email: '<EMAIL>'
  }
];

const mockChallenges: Challenge[] = [
  {
    id: 'challenge1',
    title: 'Complete 5 Tasks',
    description: 'Complete 5 tasks this week to earn points',
    points: 100,
    category: 'productivity',
    difficulty: 'easy',
    completed: false,
    progress: 3,
    maxProgress: 5
  },
  {
    id: 'challenge2',
    title: 'Help a Colleague',
    description: 'Provide assistance to a team member',
    points: 150,
    category: 'collaboration',
    difficulty: 'medium',
    completed: true,
    completedAt: new Date().toISOString(),
    progress: 1,
    maxProgress: 1
  }
];

const mockKudos: Kudos[] = [
  {
    id: 'kudos1',
    from: 'user2',
    to: 'user1',
    count: 2,
    month: '2023-11',
    date: new Date().toISOString(),
    message: 'Great work on the UI design!'
  }
];

const mockRecommendations: Recommendation[] = [
  {
    id: 'rec1',
    from: 'user1',
    to: 'user2',
    type: 'skill',
    title: 'React Development',
    description: 'John has excellent React skills and would be great for frontend projects',
    date: new Date().toISOString()
  }
];

const mockMonths: Month[] = [
  { id: 'nov2023', name: 'November', year: 2023, isActive: true },
  { id: 'oct2023', name: 'October', year: 2023, isActive: false },
  { id: 'sep2023', name: 'September', year: 2023, isActive: false }
];

const PulseBoardContext = createContext<PulseBoardContextType | undefined>(undefined);

export const PulseBoardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [challenges, setChallenges] = useState<Challenge[]>(mockChallenges);
  const [kudos, setKudos] = useState<Kudos[]>(mockKudos);
  const [recommendations, setRecommendations] = useState<Recommendation[]>(mockRecommendations);
  const [months, setMonths] = useState<Month[]>(mockMonths);
  const [users, setUsers] = useState<User[]>(mockUsers);
  
  // Current user (in a real app, this would come from auth)
  const currentUser = users.find(u => u.id === 'user1') || users[0];
  
  // Current month in "YYYY-MM" format
  const currentMonth = "2023-11";
  
  // Calculate remaining kudos for current user in current month (max 5 per month)
  const remainingKudos = 5 - kudos
    .filter(k => k.from === currentUser.id && k.month === currentMonth)
    .reduce((total, k) => total + k.count, 0);
  
  // Modal states
  const [isKudosModalOpen, setKudosModalOpen] = useState(false);
  const [isRecommendationModalOpen, setRecommendationModalOpen] = useState(false);
  
  // Give kudos to a user
  const giveKudos = (to: string, count: number, message?: string) => {
    if (count <= 0 || count > remainingKudos) return;
    
    const newKudos: Kudos = {
      id: `kudos-${Date.now()}`,
      from: currentUser.id,
      to,
      count,
      month: currentMonth,
      date: new Date().toISOString(),
      message
    };
    
    setKudos([...kudos, newKudos]);
  };
  
  // Add a recommendation
  const addRecommendation = (recommendation: Omit<Recommendation, 'id' | 'date'>) => {
    const newRecommendation: Recommendation = {
      id: `rec-${Date.now()}`,
      ...recommendation,
      date: new Date().toISOString(),
    };
    
    setRecommendations([...recommendations, newRecommendation]);
  };

  // Complete a challenge
  const completeChallenge = (challengeId: string) => {
    setChallenges(challenges.map(challenge => 
      challenge.id === challengeId 
        ? { ...challenge, completed: true, completedAt: new Date().toISOString() }
        : challenge
    ));
  };

  return (
    <PulseBoardContext.Provider
      value={{
        challenges,
        kudos,
        recommendations,
        months,
        users,
        currentUser,
        currentMonth,
        remainingKudos,
        giveKudos,
        addRecommendation,
        completeChallenge,
        isKudosModalOpen,
        setKudosModalOpen,
        isRecommendationModalOpen,
        setRecommendationModalOpen
      }}
    >
      {children}
    </PulseBoardContext.Provider>
  );
};

export const usePulseBoard = () => {
  const context = useContext(PulseBoardContext);
  if (context === undefined) {
    throw new Error("usePulseBoard must be used within a PulseBoardProvider");
  }
  return context;
};
