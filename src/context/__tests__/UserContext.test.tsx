import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor, act } from '../../test/test-utils';
import { UserProvider, useUser } from '../UserContext';
import { ToastProvider } from '../ToastContext';
import React from 'react';

// Mock <PERSON> invoke
const mockInvoke = vi.fn();
vi.mock('@tauri-apps/api/core', () => ({
  invoke: mockInvoke,
}));

// Mock toast helpers
const mockShowError = vi.fn();
vi.mock('../ToastContext', () => ({
  ToastProvider: ({ children }: { children: React.ReactNode }) => children,
  useToastHelpers: () => ({
    showError: mockShowError,
    showSuccess: vi.fn(),
    showWarning: vi.fn(),
    showInfo: vi.fn(),
  }),
}));

// Test component that uses UserContext
const TestComponent = () => {
  const { 
    user, 
    isLoading, 
    error, 
    isFromCache, 
    fetchUserProfile, 
    loadCachedProfile,
    clearUser 
  } = useUser();

  return (
    <div>
      <div data-testid="user-email">{user?.email || 'No user'}</div>
      <div data-testid="is-loading">{isLoading.toString()}</div>
      <div data-testid="error">{error || 'No error'}</div>
      <div data-testid="is-from-cache">{isFromCache.toString()}</div>
      <button onClick={() => fetchUserProfile()} data-testid="fetch-profile">
        Fetch Profile
      </button>
      <button onClick={() => loadCachedProfile()} data-testid="load-cached">
        Load Cached
      </button>
      <button onClick={() => clearUser()} data-testid="clear-user">
        Clear User
      </button>
    </div>
  );
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ToastProvider>
      <UserProvider>
        {component}
      </UserProvider>
    </ToastProvider>
  );
};

describe('UserContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should load cached profile successfully', async () => {
    const cachedProfile = {
      full_name: 'Cached User',
      email: '<EMAIL>',
      avatar: null,
      position: 'Developer',
      company: 'Test Company',
      is_admin: false,
      screen_active: false,
    };

    mockInvoke.mockResolvedValueOnce(cachedProfile);

    renderWithProviders(<TestComponent />);

    const loadCachedButton = screen.getByTestId('load-cached');
    
    await act(async () => {
      loadCachedButton.click();
    });

    await waitFor(() => {
      expect(screen.getByTestId('user-email')).toHaveTextContent('<EMAIL>');
      expect(screen.getByTestId('is-from-cache')).toHaveTextContent('true');
    });

    expect(mockInvoke).toHaveBeenCalledWith('get_cached_user_profile');
  });

  it('should handle 403 error by clearing stores', async () => {
    mockInvoke.mockRejectedValueOnce('AUTH_EXPIRED');

    renderWithProviders(<TestComponent />);

    const fetchButton = screen.getByTestId('fetch-profile');
    
    await act(async () => {
      fetchButton.click();
    });

    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent('Authentication expired');
      expect(screen.getByTestId('user-email')).toHaveTextContent('No user');
    });

    expect(mockInvoke).toHaveBeenCalledWith('clear_user_stores');
  });

  it('should retry on non-403 errors', async () => {
    // First call fails with network error
    mockInvoke.mockRejectedValueOnce('Network error');
    // Second call (retry) succeeds
    const profile = {
      full_name: 'Test User',
      email: '<EMAIL>',
      avatar: null,
      position: 'Developer',
      company: 'Test Company',
      is_admin: false,
      screen_active: true,
    };
    mockInvoke.mockResolvedValueOnce(profile);

    renderWithProviders(<TestComponent />);

    const fetchButton = screen.getByTestId('fetch-profile');
    
    await act(async () => {
      fetchButton.click();
    });

    // Should show error and toast
    await waitFor(() => {
      expect(mockShowError).toHaveBeenCalledWith(
        'Profile Loading Failed',
        'Retrying automatically...',
        3000
      );
    });

    // Fast-forward 2 seconds to trigger retry
    await act(async () => {
      vi.advanceTimersByTime(2000);
    });

    // Should succeed on retry
    await waitFor(() => {
      expect(screen.getByTestId('user-email')).toHaveTextContent('<EMAIL>');
      expect(screen.getByTestId('is-from-cache')).toHaveTextContent('false');
    });

    expect(mockInvoke).toHaveBeenCalledTimes(2);
  });

  it('should clear user data and cancel retries', async () => {
    renderWithProviders(<TestComponent />);

    const clearButton = screen.getByTestId('clear-user');
    
    await act(async () => {
      clearButton.click();
    });

    expect(screen.getByTestId('user-email')).toHaveTextContent('No user');
    expect(screen.getByTestId('is-from-cache')).toHaveTextContent('false');
    expect(screen.getByTestId('error')).toHaveTextContent('No error');
  });

  it('should handle cached profile not found', async () => {
    mockInvoke.mockResolvedValueOnce(null);

    renderWithProviders(<TestComponent />);

    const loadCachedButton = screen.getByTestId('load-cached');
    
    await act(async () => {
      loadCachedButton.click();
    });

    expect(screen.getByTestId('user-email')).toHaveTextContent('No user');
    expect(screen.getByTestId('is-from-cache')).toHaveTextContent('false');
  });
});
