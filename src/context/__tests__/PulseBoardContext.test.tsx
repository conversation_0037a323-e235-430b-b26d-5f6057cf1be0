import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '../../test/test-utils'
import { PulseBoardProvider, usePulseBoard } from '../PulseBoardContext'

// Test component to use the context
const TestComponent = () => {
  const { 
    challenges,
    kudos,
    recommendations,
    users,
    currentUser,
    currentMonth,
    remainingKudos,
    giveKudos,
    addRecommendation,
    completeChallenge,
    isKudosModalOpen,
    setKudosModalOpen,
    isRecommendationModalOpen,
    setRecommendationModalOpen
  } = usePulseBoard()

  return (
    <div>
      <div data-testid="challenges-count">{challenges.length}</div>
      <div data-testid="kudos-count">{kudos.length}</div>
      <div data-testid="recommendations-count">{recommendations.length}</div>
      <div data-testid="users-count">{users.length}</div>
      <div data-testid="current-user">{currentUser.name}</div>
      <div data-testid="current-month">{currentMonth}</div>
      <div data-testid="remaining-kudos">{remainingKudos}</div>
      <div data-testid="kudos-modal-open">{isKudosModalOpen.toString()}</div>
      <div data-testid="recommendation-modal-open">{isRecommendationModalOpen.toString()}</div>
      
      <button onClick={() => giveKudos('user2', 1, 'Great work!')}>Give Kudos</button>
      <button onClick={() => addRecommendation({
        from: currentUser.id,
        to: 'user2',
        type: 'skill',
        title: 'Test Skill',
        description: 'Test description'
      })}>Add Recommendation</button>
      <button onClick={() => completeChallenge('challenge1')}>Complete Challenge</button>
      <button onClick={() => setKudosModalOpen(true)}>Open Kudos Modal</button>
      <button onClick={() => setRecommendationModalOpen(true)}>Open Recommendation Modal</button>
    </div>
  )
}

describe('PulseBoardContext', () => {
  it('provides default values', () => {
    render(
      <PulseBoardProvider>
        <TestComponent />
      </PulseBoardProvider>
    )
    
    expect(screen.getByTestId('challenges-count')).toHaveTextContent('2')
    expect(screen.getByTestId('kudos-count')).toHaveTextContent('1')
    expect(screen.getByTestId('recommendations-count')).toHaveTextContent('1')
    expect(screen.getByTestId('users-count')).toHaveTextContent('3')
    expect(screen.getByTestId('current-user')).toHaveTextContent('TeamBy User')
    expect(screen.getByTestId('current-month')).toHaveTextContent('2023-11')
    expect(screen.getByTestId('remaining-kudos')).toHaveTextContent('5') // 5 - 0 = 5 (no kudos given by current user yet)
    expect(screen.getByTestId('kudos-modal-open')).toHaveTextContent('false')
    expect(screen.getByTestId('recommendation-modal-open')).toHaveTextContent('false')
  })

  it('gives kudos correctly', () => {
    render(
      <PulseBoardProvider>
        <TestComponent />
      </PulseBoardProvider>
    )
    
    const giveKudosButton = screen.getByText('Give Kudos')
    fireEvent.click(giveKudosButton)
    
    // Kudos count should increase
    expect(screen.getByTestId('kudos-count')).toHaveTextContent('2')
    
    // Remaining kudos should decrease
    expect(screen.getByTestId('remaining-kudos')).toHaveTextContent('4')
  })

  it('adds recommendations correctly', () => {
    render(
      <PulseBoardProvider>
        <TestComponent />
      </PulseBoardProvider>
    )
    
    const addRecommendationButton = screen.getByText('Add Recommendation')
    fireEvent.click(addRecommendationButton)
    
    // Recommendations count should increase
    expect(screen.getByTestId('recommendations-count')).toHaveTextContent('2')
  })

  it('completes challenges correctly', () => {
    render(
      <PulseBoardProvider>
        <TestComponent />
      </PulseBoardProvider>
    )
    
    const completeChallengeButton = screen.getByText('Complete Challenge')
    fireEvent.click(completeChallengeButton)
    
    // We can't directly test the challenge completion without exposing more state,
    // but we can verify the function was called without errors
    expect(screen.getByTestId('challenges-count')).toHaveTextContent('2')
  })

  it('manages modal states correctly', () => {
    render(
      <PulseBoardProvider>
        <TestComponent />
      </PulseBoardProvider>
    )
    
    const openKudosModalButton = screen.getByText('Open Kudos Modal')
    fireEvent.click(openKudosModalButton)
    
    expect(screen.getByTestId('kudos-modal-open')).toHaveTextContent('true')
    
    const openRecommendationModalButton = screen.getByText('Open Recommendation Modal')
    fireEvent.click(openRecommendationModalButton)
    
    expect(screen.getByTestId('recommendation-modal-open')).toHaveTextContent('true')
  })

  it('prevents giving more kudos than remaining', () => {
    render(
      <PulseBoardProvider>
        <TestComponent />
      </PulseBoardProvider>
    )
    
    const giveKudosButton = screen.getByText('Give Kudos')
    
    // Give kudos 5 times (should use up all remaining kudos)
    for (let i = 0; i < 5; i++) {
      fireEvent.click(giveKudosButton)
    }
    
    expect(screen.getByTestId('remaining-kudos')).toHaveTextContent('0')
    expect(screen.getByTestId('kudos-count')).toHaveTextContent('6') // 1 initial + 5 new
    
    // Try to give one more (should be prevented)
    fireEvent.click(giveKudosButton)
    
    expect(screen.getByTestId('remaining-kudos')).toHaveTextContent('0')
    expect(screen.getByTestId('kudos-count')).toHaveTextContent('6') // Should not increase
  })

  it('throws error when used outside provider', () => {
    // Mock console.error to avoid noise in test output
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    expect(() => {
      render(<TestComponent />)
    }).toThrow('usePulseBoard must be used within a PulseBoardProvider')
    
    consoleSpy.mockRestore()
  })
})
