import { describe, it, expect } from 'vitest'
import { render, screen, fireEvent } from '../../test/test-utils'
import { AppProvider, useApp } from '../AppContext'

// Test component to use the context
const TestComponent = () => {
  const { 
    activeApp, 
    setActiveApp, 
    hoveredApp, 
    setHoveredApp,
    showEmployeeList,
    setShowEmployeeList,
    sidebarExpanded,
    setSidebarExpanded
  } = useApp()

  return (
    <div>
      <div data-testid="active-app">{activeApp}</div>
      <div data-testid="hovered-app">{hoveredApp || 'none'}</div>
      <div data-testid="show-employee-list">{showEmployeeList.toString()}</div>
      <div data-testid="sidebar-expanded">{sidebarExpanded.toString()}</div>
      
      <button onClick={() => setActiveApp('chat')}>Set Chat</button>
      <button onClick={() => setHoveredApp('project')}>Hover Project</button>
      <button onClick={() => setShowEmployeeList(true)}>Show Employees</button>
      <button onClick={() => setSidebarExpanded(true)}>Expand Sidebar</button>
    </div>
  )
}

describe('AppContext', () => {
  it('provides default values', () => {
    render(
      <AppProvider>
        <TestComponent />
      </AppProvider>
    )
    
    expect(screen.getByTestId('active-app')).toHaveTextContent('time')
    expect(screen.getByTestId('hovered-app')).toHaveTextContent('none')
    expect(screen.getByTestId('show-employee-list')).toHaveTextContent('false')
    expect(screen.getByTestId('sidebar-expanded')).toHaveTextContent('false')
  })

  it('updates activeApp when setActiveApp is called', () => {
    render(
      <AppProvider>
        <TestComponent />
      </AppProvider>
    )
    
    fireEvent.click(screen.getByText('Set Chat'))
    expect(screen.getByTestId('active-app')).toHaveTextContent('chat')
  })

  it('updates hoveredApp when setHoveredApp is called', () => {
    render(
      <AppProvider>
        <TestComponent />
      </AppProvider>
    )
    
    fireEvent.click(screen.getByText('Hover Project'))
    expect(screen.getByTestId('hovered-app')).toHaveTextContent('project')
  })

  it('updates showEmployeeList when setShowEmployeeList is called', () => {
    render(
      <AppProvider>
        <TestComponent />
      </AppProvider>
    )
    
    fireEvent.click(screen.getByText('Show Employees'))
    expect(screen.getByTestId('show-employee-list')).toHaveTextContent('true')
  })

  it('updates sidebarExpanded when setSidebarExpanded is called', () => {
    render(
      <AppProvider>
        <TestComponent />
      </AppProvider>
    )
    
    fireEvent.click(screen.getByText('Expand Sidebar'))
    expect(screen.getByTestId('sidebar-expanded')).toHaveTextContent('true')
  })

  it('throws error when used outside provider', () => {
    // Mock console.error to avoid noise in test output
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    expect(() => {
      render(<TestComponent />)
    }).toThrow('useApp must be used within an AppProvider')
    
    consoleSpy.mockRestore()
  })
})
