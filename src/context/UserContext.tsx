import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback, useRef } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { employeeStatusService } from '../modules/centrifugo/EmployeeStatusService';
import { useToastHelpers } from './ToastContext';
import type { CentrifugoMessage } from '../hooks/useCentrifugo';

// User Profile Types
export interface IncompleteActivity {
  activity_id: number;
  duration: string;
}

export interface UserProfile {
  full_name: string;
  email: string;
  avatar: string | null;
  position: string | null;
  company: string | null;
  is_admin: boolean;
  screen_active: boolean;
  incomplete_activity: IncompleteActivity | null;
}

// Employee Types
export interface Employee {
  id: number;
  full_name: string;
  position_name: string | null;
  isonline: boolean;
  screan_active: boolean;
  avatar: string;
}

// Employee Detail Types for profile modal
export interface EmployeeDetail {
  id: number;
  full_name: string;
  email: string;
  phone_number: string;
  bio: string | null;
  skills: string[];
  typical_working_weekday_start: string | null;
  typical_working_weekday_end: string | null;
  typical_working_weekend: string | null;
  score: number;
  slogan: string | null;
  projects: {
    name: string;
    role: string;
  }[];
  position: string | null;
  created_at: string;
  city: string | null;
  country: string | null;
  contacts: {
    email: string;
    phone: string;
    github?: string;
    linkedin?: string;
    telegram?: string;
    instagram?: string;
  };
  isonline: boolean;
  last_seen: string;
}

// Activity Stats Types
export interface ActivityStat {
  title: string;
  slug: string;
  icon: string;
  value: number;
}

interface UserContextType {
  user: UserProfile | null;
  isLoading: boolean;
  error: string | null;
  isFromCache: boolean;
  isOfflineMode: boolean;
  hasError: boolean; // New flag for error state
  incompleteActivity: IncompleteActivity | null; // New field for incomplete activity
  employees: Employee[]; // New field for employees list
  isLoadingEmployees: boolean; // Loading state for employees
  employeesError: string | null; // Error state for employees
  activityStats: ActivityStat[]; // New field for activity stats
  fetchUserProfile: () => Promise<void>;
  refreshUserProfile: () => Promise<void>;
  clearUser: () => void;
  toggleScreenActive: () => Promise<void>;
  updateScreenActive: (active: boolean) => Promise<void>;
  loadCachedProfile: () => Promise<void>;
  createOfflineProfile: (email: string) => Promise<void>;
  clearIncompleteActivity: () => void; // New function to clear incomplete activity
  fetchEmployees: () => Promise<void>; // New function to fetch employees
  fetchActivityStats: () => Promise<void>; // New function to fetch activity stats
  loadActivityStatsFromStore: () => Promise<boolean>; // New function to load activity stats from store
  updateEmployeeOnlineStatus: (employeeId: number, isOnline: boolean) => void; // New function to update employee online status
  updateEmployeeScreenActive: (employeeId: number, screenActive: boolean) => void; // New function to update employee screen active status
}

export const UserContext = createContext<UserContextType | undefined>(undefined);

// Custom hook for accessing user context
export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}

interface UserProviderProps {
  children: ReactNode;
}

const UserProviderComponent: React.FC<UserProviderProps> = ({ children }) => {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isFromCache, setIsFromCache] = useState(false);
  const [isOfflineMode, setIsOfflineMode] = useState(false);
  const [hasFetched, setHasFetched] = useState(false);
  const [hasCacheLoaded, setHasCacheLoaded] = useState(false);
  const [hasError, setHasError] = useState(false); // New error flag
  const [incompleteActivity, setIncompleteActivity] = useState<IncompleteActivity | null>(null);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoadingEmployees, setIsLoadingEmployees] = useState(false);
  const [employeesError, setEmployeesError] = useState<string | null>(null);
  const [activityStats, setActivityStats] = useState<ActivityStat[]>([]);

  // Refs for preventing duplicate calls
  const retryTimeoutRef = useRef<number | null>(null);
  const isFetchingRef = useRef(false);
  
  const { showError, showSuccess } = useToastHelpers();

  // Load cached profile from Tauri store
  const loadCachedProfile = useCallback(async () => {
    if (hasCacheLoaded) {
      console.log('⏭️ [USER] Cache already loaded, skipping...');
      return;
    }

    try {
      console.log('💾 [USER] Loading cached profile...');
      setHasCacheLoaded(true);
      const cachedProfile = await invoke<UserProfile | null>('get_cached_user_profile');

      if (cachedProfile) {
        console.log('✅ [USER] Cached profile loaded:', cachedProfile.email);
        console.log('🖼️ [USER] Cached profile avatar:', cachedProfile.avatar);
        console.log('🖼️ [USER] Cached profile avatar type:', typeof cachedProfile.avatar);
        setUser(cachedProfile);
        setIsFromCache(true);
        setError(null);
      } else {
        console.log('ℹ️ [USER] No cached profile found');
        setIsFromCache(false);
      }
    } catch (err) {
      console.error('❌ [USER] Failed to load cached profile:', err);
      setIsFromCache(false);
    }
  }, [hasCacheLoaded]);

  // Load activity stats from Tauri store
  const loadActivityStatsFromStore = useCallback(async () => {
    try {
      console.log('💾 [ACTIVITY_STATS] Loading cached activity stats...');
      const cachedStats = await invoke<ActivityStat[] | null>('get_cached_activity_stats');

      if (cachedStats) {
        console.log('✅ [ACTIVITY_STATS] Cached stats loaded:', cachedStats.length, 'stats');
        setActivityStats(cachedStats);
        return true;
      } else {
        console.log('ℹ️ [ACTIVITY_STATS] No cached stats found');
        return false;
      }
    } catch (err) {
      console.error('❌ [ACTIVITY_STATS] Failed to load cached stats:', err);
      return false;
    }
  }, []);

  // Create offline profile
  const createOfflineProfile = useCallback(async (email: string) => {
    try {
      console.log('📴 [USER] Creating offline profile for:', email);
      const offlineProfile = await invoke<UserProfile>('create_offline_profile', { email });
      console.log('✅ [USER] Offline profile created:', offlineProfile);

      setUser(offlineProfile);
      setIsFromCache(true);
      setIsOfflineMode(true);
      setError(null);
    } catch (err) {
      console.error('❌ [USER] Failed to create offline profile:', err);
      setError('Failed to create offline profile');
    }
  }, []);

  // Get user-friendly error message
  const getErrorMessage = (error: string): { title: string; message: string } => {
    switch (error) {
      case 'NETWORK_TIMEOUT':
        return { title: 'Connection Timeout', message: 'Request timed out' };
      case 'NETWORK_CONNECTION_FAILED':
        return { title: 'Connection Failed', message: 'Cannot connect to server' };
      case 'NETWORK_REQUEST_FAILED':
        return { title: 'Request Failed', message: 'Network request failed' };
      case 'NETWORK_ERROR':
        return { title: 'Network Error', message: 'Network error occurred' };
      default:
        return { title: 'Profile Loading Failed', message: 'Failed to load profile' };
    }
  };

  // Simple retry function
  const retryFetch = useCallback((errorMessage: string) => {
    console.log('🔄 [USER] Setting up retry for error:', errorMessage);
    
    // Show toast immediately
    const { title, message } = getErrorMessage(errorMessage);
    showError(title, message, 2000);

    // Clear any existing timer
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }

    // Set up retry after 10 seconds
    retryTimeoutRef.current = setTimeout(async () => {
      console.log('🔄 [REACT] Retrying fetch after 10 seconds...');
      
      try {
        console.log('🔄 [REACT] Calling invoke(get_user_profile)...');
        const profile = await invoke<UserProfile>('get_user_profile');
        console.log('✅ [REACT] Retry successful:', profile.email);
        
        // Success - update state
        setUser(profile);
        setIsFromCache(false);
        setIsOfflineMode(false);
        setError(null);
        setHasError(false); // Reset error flag on retry success
        
      } catch (retryErr) {
        console.log('❌ [REACT] Retry failed:', retryErr);
        const retryErrorMessage = typeof retryErr === 'string' ? retryErr : 'Retry failed';
        
        // Handle auth errors - stop retrying
        if (retryErrorMessage === 'AUTH_EXPIRED') {
          console.log('🔐 [USER] Auth expired, stopping retry');
          setUser(null);
          setIsFromCache(false);
          setError('Authentication expired');
          try {
            await invoke('clear_user_stores');
          } catch (clearErr) {
            console.error('Failed to clear user stores:', clearErr);
          }
          return;
        }
        
        // Continue retrying for other errors
        retryFetch(retryErrorMessage);
      }
    }, 3000);
  }, [showError]);

  // Main fetch function - simple and clean
  const fetchUserProfile = useCallback(async () => {
    console.log('👤 [REACT] fetchUserProfile called');
    
    // Prevent duplicate calls
    if (isFetchingRef.current) {
      console.log('🛡️ [REACT] Already fetching, ignoring call');
      return;
    }

    // Set flags
    isFetchingRef.current = true;
    setIsLoading(true);
    setHasFetched(true);
    setIsFromCache(false);
    setError(null);
    setHasError(false); // Reset error flag

    try {
      console.log('🔄 [REACT] Calling invoke(get_user_profile)...');
      const profile = await invoke<UserProfile>('get_user_profile');
      console.log('✅ [REACT] Profile fetched successfully:', profile.email);
      
      // Success
      console.log('🔄 [REACT] Setting user profile in state:', profile);
      console.log('🖼️ [REACT] Profile avatar value:', profile.avatar);
      console.log('🖼️ [REACT] Profile avatar type:', typeof profile.avatar);
      setUser(profile);
      setIsFromCache(false);
      setIsOfflineMode(false);
      setError(null);
      setHasError(false); // Reset error flag on success

      // Handle incomplete activity
      setIncompleteActivity(profile.incomplete_activity);
      if (profile.incomplete_activity) {
        console.log('⚠️ [REACT] Found incomplete activity ID:', profile.incomplete_activity.activity_id, 'with duration:', profile.incomplete_activity.duration);
      }

    } catch (err) {
      console.log('❌ [REACT] Fetch failed:', err);
      const errorMessage = typeof err === 'string' ? err : 'Failed to fetch user profile';
      
      // Handle auth errors
      if (errorMessage === 'AUTH_EXPIRED') {
        console.log('🔐 [USER] Auth expired, clearing data');
        setUser(null);
        setIsFromCache(false);
        setError('Authentication expired');
        setHasFetched(false);
        
        try {
          await invoke('clear_user_stores');
        } catch (clearErr) {
          console.error('Failed to clear user stores:', clearErr);
        }
      } else {
        // For other errors, start retry
        setError(errorMessage);
        setHasError(true); // Set error flag for UI
        retryFetch(errorMessage);
      }
    } finally {
      setIsLoading(false);
      isFetchingRef.current = false;
    }
  }, [retryFetch]);

  // Force refresh
  const refreshUserProfile = useCallback(async () => {
    console.log('🔄 [USER] Force refreshing...');
    setHasFetched(false);
    setIsFromCache(false);
    await fetchUserProfile();
  }, [fetchUserProfile]);

  // Clear user data
  const clearUser = useCallback(() => {
    console.log('🗑️ [USER] Clearing user data...');
    setUser(null);
    setError(null);
    setIsFromCache(false);
    setIsOfflineMode(false);
    setHasFetched(false);
    setHasCacheLoaded(false);
    setHasError(false); // Reset error flag
    isFetchingRef.current = false;

    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
  }, []);

  // Toggle screen active
  const toggleScreenActive = useCallback(async () => {
    if (!user) return;
    const newScreenActive = !user.screen_active;
    console.log(`👁️ [USER] Toggling screen_active: ${user.screen_active} -> ${newScreenActive}`);

    try {
      // Call Tauri backend to update screen active status via API
      console.log('📡 [USER] Calling update_screen_active_status API...');
      const updatedProfile = await invoke<UserProfile>('update_screen_active_status', {
        screenActive: newScreenActive
      });

      console.log('✅ [USER] Screen active status updated successfully via API');
      console.log('📋 [USER] Updated profile:', updatedProfile);

      // Update user state with the response from API
      setUser(updatedProfile);

      // Show success notification
      showSuccess(`Screen sharing ${newScreenActive ? 'enabled' : 'disabled'} successfully`);

    } catch (err) {
      console.error('❌ [USER] Failed to toggle screen_active:', err);

      // Handle specific error types
      if (typeof err === 'string') {
        if (err === 'AUTH_EXPIRED') {
          showError('Session expired. Please log in again.');
          // Don't update state, let auth handler deal with it
          return;
        } else if (err === 'NETWORK_CONNECTION_FAILED') {
          showError('Network connection failed. Please check your internet connection.');
        } else if (err === 'Invalid data provided') {
          showError('Invalid request. Please try again.');
        } else {
          showError('Failed to update screen sharing status. Please try again.');
        }
      } else {
        showError('Failed to update screen sharing status. Please try again.');
      }

      // Don't change the user state on error - keep current state
    }
  }, [user, showSuccess, showError]);

  // Update screen active
  const updateScreenActive = useCallback(async (active: boolean) => {
    if (!user || user.screen_active === active) return;
    console.log(`👁️ [USER] Updating screen_active to: ${active}`);

    try {
      setUser(prev => prev ? { ...prev, screen_active: active } : null);
    } catch (err) {
      console.error('❌ [USER] Failed to update screen_active:', err);
      setUser(prev => prev ? { ...prev, screen_active: !active } : null);
    }
  }, [user]);

  // Clear incomplete activity
  const clearIncompleteActivity = useCallback(() => {
    console.log('🗑️ [USER] Clearing incomplete activity');
    setIncompleteActivity(null);
    // Also update user profile to remove incomplete activity
    setUser(prev => prev ? { ...prev, incomplete_activity: null } : null);
  }, []);

  // Fetch employees list
  const fetchEmployees = useCallback(async () => {
    if (isLoadingEmployees) return; // Prevent duplicate calls

    setIsLoadingEmployees(true);
    setEmployeesError(null);

    try {
      console.log('👥 [USER] Fetching employees list...');
      const employeesData = await invoke<Employee[]>('fetch_employees_list');
      console.log('✅ [USER] Employees fetched successfully:', employeesData.length, 'employees');
      setEmployees(employeesData);
    } catch (err) {
      console.error('❌ [USER] Failed to fetch employees:', err);
      const errorMessage = typeof err === 'string' ? err : 'Failed to fetch employees';
      setEmployeesError(errorMessage);

      // Handle auth errors
      if (errorMessage === 'AUTH_EXPIRED') {
        console.log('🔐 [USER] Auth expired while fetching employees');
      }
    } finally {
      setIsLoadingEmployees(false);
    }
  }, [isLoadingEmployees]);

  // Update employee online status (for Centrifugo updates)
  const updateEmployeeOnlineStatus = useCallback((employeeId: number, isOnline: boolean) => {
    console.log(`🔄 [USER] Updating employee ${employeeId} online status to:`, isOnline);

    setEmployees(prevEmployees => {
      const updatedEmployees = prevEmployees.map(employee =>
        employee.id === employeeId
          ? { ...employee, isonline: isOnline }
          : employee
      );

      // Sort employees: online first, then offline
      const sortedEmployees = updatedEmployees.sort((a, b) => {
        if (a.isonline && !b.isonline) return -1;
        if (!a.isonline && b.isonline) return 1;
        return 0;
      });

      console.log(`✅ [USER] Employee ${employeeId} status updated and list re-sorted`);
      return sortedEmployees;
    });
  }, []);

  // Update employee screen active status (for Centrifugo updates)
  const updateEmployeeScreenActive = useCallback((employeeId: number, screenActive: boolean) => {
    console.log(`👁️ [USER] Updating employee ${employeeId} screen_active status to:`, screenActive);

    setEmployees(prevEmployees => {
      const updatedEmployees = prevEmployees.map(employee =>
        employee.id === employeeId
          ? { ...employee, screan_active: screenActive }
          : employee
      );

      console.log(`✅ [USER] Employee ${employeeId} screen_active updated to:`, screenActive);
      return updatedEmployees;
    });
  }, []);

  // Initialize Centrifugo Employee Status Service
  useEffect(() => {
    console.log('🔧 [USER] Initializing Centrifugo Employee Status Service...');

    // Debug log to Tauri terminal
    invoke('debug_log', {
      message: 'Initializing Centrifugo Employee Status Service...',
      tag: 'USER-CONTEXT'
    });

    // Initialize service with setEmployees updater function
    employeeStatusService.init(setEmployees);

    console.log('✅ [USER] Centrifugo Employee Status Service initialized');

    // Debug log to Tauri terminal
    invoke('debug_log', {
      message: 'Centrifugo Employee Status Service initialized successfully',
      tag: 'USER-CONTEXT'
    });

    // Cleanup on unmount
    return () => {
      employeeStatusService.cleanup();
    };
  }, []);

  // Fetch activity stats
  const fetchActivityStats = useCallback(async () => {
    try {
      console.log('📊 [USER] Fetching activity stats...');
      const statsData = await invoke<ActivityStat[]>('fetch_activity_stats');
      console.log('✅ [USER] Activity stats fetched successfully:', statsData);

      // Update state immediately
      setActivityStats(statsData);

      // Store in Tauri store for offline use
      try {
        await invoke('store_activity_stats', { stats: statsData });
        console.log('💾 [USER] Activity stats stored in Tauri store');
      } catch (storageErr) {
        console.error('❌ [USER] Failed to store activity stats in Tauri store:', storageErr);
      }
    } catch (err) {
      console.error('❌ [USER] Failed to fetch activity stats:', err);

      // Try to load from Tauri store if not already loaded
      if (activityStats.length === 0) {
        console.log('🔄 [USER] Trying to load activity stats from Tauri store');
        await loadActivityStatsFromStore();
      } else {
        console.log('🔄 [USER] Using cached activity stats from state');
      }
    }
  }, [loadActivityStatsFromStore, activityStats.length]);

  // Fetch employees after successful profile fetch
  useEffect(() => {
    if (user && !hasError && !isLoading && employees.length === 0 && !isLoadingEmployees && !employeesError) {
      console.log('👥 [REACT] Profile loaded successfully, now fetching employees...');
      fetchEmployees();
    }
  }, [user, hasError, isLoading, employees.length, isLoadingEmployees, employeesError, fetchEmployees]);

  // Fetch activity stats after successful profile fetch
  useEffect(() => {
    if (user && !hasError && !isLoading) {
      console.log('📊 [REACT] Profile loaded successfully, now fetching activity stats...');
      fetchActivityStats();
    }
  }, [user, hasError, isLoading, fetchActivityStats]);

  // Listen for Centrifugo employee status updates
  useEffect(() => {
    const handleCentrifugoMessage = (event: CustomEvent<CentrifugoMessage>) => {
      const message = event.detail;
      console.log('📨 [USER] Received Centrifugo message:', message);

      if (message.message_type === 'employee_online') {
        updateEmployeeOnlineStatus(message.employee_id, true);
      } else if (message.message_type === 'employee_offline') {
        updateEmployeeOnlineStatus(message.employee_id, false);
      } else if (message.message_type === 'employee_update_screen_view') {
        console.log('👁️ [USER] Received screen view update for employee:', message.employee_id, 'screen_active:', message.screen_active);
        if (message.screen_active !== undefined) {
          updateEmployeeScreenActive(message.employee_id, message.screen_active);
        } else {
          console.warn('⚠️ [USER] screen_active field is missing in employee_update_screen_view message');
        }
      }
    };

    // Listen for custom Centrifugo events
    window.addEventListener('centrifugo-employee-status', handleCentrifugoMessage as EventListener);

    return () => {
      window.removeEventListener('centrifugo-employee-status', handleCentrifugoMessage as EventListener);
    };
  }, [updateEmployeeOnlineStatus, updateEmployeeScreenActive]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  const value: UserContextType = {
    user,
    isLoading,
    error,
    isFromCache,
    isOfflineMode,
    hasError,
    incompleteActivity,
    employees,
    isLoadingEmployees,
    employeesError,
    activityStats,
    fetchUserProfile,
    refreshUserProfile,
    clearUser,
    toggleScreenActive,
    updateScreenActive,
    loadCachedProfile,
    createOfflineProfile,
    clearIncompleteActivity,
    fetchEmployees,
    fetchActivityStats,
    loadActivityStatsFromStore,
    updateEmployeeOnlineStatus,
    updateEmployeeScreenActive,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

// Export the memoized component
export const UserProvider = React.memo(UserProviderComponent);
UserProvider.displayName = 'UserProvider';

// Export types
export type { UserContextType };
