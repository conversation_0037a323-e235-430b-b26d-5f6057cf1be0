import React, { createContext, useContext, useState } from "react";

export interface AppContextType {
  activeApp: string;
  setActiveApp: (appId: string) => void;
  hoveredApp: string | null;
  setHoveredApp: (appId: string | null) => void;
  showEmployeeList: boolean;
  setShowEmployeeList: (show: boolean) => void;
  sidebarExpanded: boolean;
  setSidebarExpanded: (expanded: boolean) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

interface AppProviderProps {
  children: React.ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [activeApp, setActiveApp] = useState("time");
  const [hoveredApp, setHoveredApp] = useState<string | null>(null);
  const [showEmployeeList, setShowEmployeeList] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);

  return (
    <AppContext.Provider 
      value={{ 
        activeApp,
        setActiveApp,
        hoveredApp,
        setHoveredApp,
        showEmployeeList,
        setShowEmployeeList,
        sidebarExpanded,
        setSidebarExpanded
      }}
    >
      {children}
    </AppContext.Provider>
  );
};
