import React, { createContext, useContext, useState, useEffect } from "react";

type Status = "available" | "meeting" | "offline";

interface TimeTrackingContextType {
  isTracking: boolean;
  toggleTracking: () => void;
  userStatus: Status;
  setUserStatus: (status: Status) => void;
  currentTimeEntry: TimeEntry | null;
  setCurrentTimeEntry: (entry: TimeEntry | null) => void;
  showOverviewModal: boolean;
  setShowOverviewModal: (show: boolean) => void;
  timeLogs: TimeEntry[];
  addTimeLog: (log: TimeEntry) => void;
  updateTimeLog: (id: string, log: Partial<TimeEntry>) => void;
}

export interface TimeEntry {
  id: string;
  startTime: Date;
  endTime: Date;
  duration: string;
  project: string;
  task: string;
  notes: string;
  /**
   * Optional user information associated with this time entry.  
   * Keeping it optional ensures existing sample data remains valid while
   * allowing richer objects (avatar, hourlyRate, etc.) to be attached when
   * available – for example in the TimeTrackingReportsPage.
   */
  user?: {
    id: string;
    name: string;
    avatar: string;
    role?: string;
    hourlyRate?: number;
  };
}

const TimeTrackingContext = createContext<TimeTrackingContextType | undefined>(undefined);

export const useTimeTracking = () => {
  const context = useContext(TimeTrackingContext);
  if (context === undefined) {
    throw new Error('useTimeTracking must be used within a TimeTrackingProvider');
  }
  return context;
};

interface TimeTrackingProviderProps {
  children: React.ReactNode;
}

export const TimeTrackingProvider: React.FC<TimeTrackingProviderProps> = ({ children }) => {
  const [isTracking, setIsTracking] = useState(false);
  const [userStatus, setUserStatus] = useState<Status>("offline"); // Default to offline when not tracking
  const [currentTimeEntry, setCurrentTimeEntry] = useState<TimeEntry | null>(null);
  const [showOverviewModal, setShowOverviewModal] = useState(false);
  const [timeLogs, setTimeLogs] = useState<TimeEntry[]>([]);

  // Load saved state from localStorage on mount
  useEffect(() => {
    const savedStatus = localStorage.getItem('userStatus') as Status;
    if (savedStatus) {
      setUserStatus(savedStatus);
    }
    
    const savedTimeLogs = localStorage.getItem('timeLogs');
    if (savedTimeLogs) {
      try {
        const parsedLogs = JSON.parse(savedTimeLogs).map((log: any) => ({
          ...log,
          startTime: new Date(log.startTime),
          endTime: new Date(log.endTime)
        }));
        setTimeLogs(parsedLogs);
      } catch (error) {
        console.error('Error parsing saved time logs:', error);
      }
    }
  }, []);

  // Save status to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('userStatus', userStatus);
  }, [userStatus]);

  // Save time logs to localStorage when they change
  useEffect(() => {
    localStorage.setItem('timeLogs', JSON.stringify(timeLogs));
  }, [timeLogs]);

  // Auto-manage user status based on tracking state
  useEffect(() => {
    if (!isTracking && userStatus !== "offline") {
      // When not tracking, automatically set to offline unless user explicitly chose another status
      const savedStatus = localStorage.getItem('userStatus') as Status;
      if (!savedStatus || savedStatus === "available") {
        setUserStatus("offline");
      }
    }
  }, [isTracking, userStatus]);
  
  const toggleTracking = () => {
    if (isTracking) {
      // Stop tracking
      if (currentTimeEntry) {
        const endTime = new Date();
        const updatedEntry = {
          ...currentTimeEntry,
          endTime,
          duration: formatDuration(currentTimeEntry.startTime, endTime)
        };

        setTimeLogs(prev => [...prev, updatedEntry]);
        setShowOverviewModal(true);
      }
      setIsTracking(false);
      // When stopping tracking, automatically set to offline
      setUserStatus("offline");
    } else {
      // Start tracking
      const newEntry: TimeEntry = {
        id: Date.now().toString(),
        startTime: new Date(),
        endTime: new Date(),
        duration: "00:00:00",
        project: "",
        task: "",
        notes: ""
      };
      setCurrentTimeEntry(newEntry);
      setIsTracking(true);

      // When starting tracking, change from offline to available
      if (userStatus === "offline") {
        setUserStatus("available");
      }
    }
  };

  const addTimeLog = (log: TimeEntry) => {
    setTimeLogs(prev => [...prev, log]);
  };

  const updateTimeLog = (id: string, updatedLog: Partial<TimeEntry>) => {
    setTimeLogs(prev => prev.map(log => 
      log.id === id ? { ...log, ...updatedLog } : log
    ));
  };
  
  const formatDuration = (start: Date, end: Date) => {
    const diff = Math.floor((end.getTime() - start.getTime()) / 1000);
    const hours = Math.floor(diff / 3600).toString().padStart(2, '0');
    const minutes = Math.floor((diff % 3600) / 60).toString().padStart(2, '0');
    const seconds = (diff % 60).toString().padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
  };
  
  return (
    <TimeTrackingContext.Provider 
      value={{ 
        isTracking, 
        toggleTracking,
        userStatus,
        setUserStatus,
        currentTimeEntry,
        setCurrentTimeEntry,
        showOverviewModal,
        setShowOverviewModal,
        timeLogs,
        addTimeLog,
        updateTimeLog
      }}
    >
      {children}
    </TimeTrackingContext.Provider>
  );
};
