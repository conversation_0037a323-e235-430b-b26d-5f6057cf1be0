// Shared type definitions for the entire application
// This file unifies all duplicate type definitions and provides a single source of truth

/**
 * Unified User interface that consolidates all user-related types across the application
 * Replaces duplicate User interfaces in project/types.ts, pulse-board/types.ts, etc.
 */
export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  role: string;
  department?: string;
  status?: 'active' | 'inactive' | 'busy' | 'offline';
  isOnline?: boolean;
  lastSeen?: Date;
  hourlyRate?: number;
}

/**
 * Common entity base interface for all domain entities
 */
export interface BaseEntity {
  id: string;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  createdBy?: User;
}

/**
 * Common attachment interface used across multiple domains
 */
export interface Attachment {
  id: string;
  name: string;
  url?: string;
  type: 'image' | 'document' | 'video' | 'audio' | 'other';
  size: string;
  thumbnailUrl?: string;
  uploadedBy?: User;
  uploadedAt?: string; // ISO date string
}

/**
 * Common comment interface used across multiple domains
 */
export interface Comment extends BaseEntity {
  user: User;
  content: string;
  timestamp: string; // ISO date string
  attachments?: Attachment[];
  replyTo?: Comment;
  isEdited?: boolean;
  editedAt?: string; // ISO date string
}

/**
 * Priority levels used across different domains
 */
export type Priority = 'Low' | 'Medium' | 'High' | 'Critical';

/**
 * Common status types for various entities
 */
export type EntityStatus = 'active' | 'inactive' | 'archived' | 'deleted';

/**
 * Message status for communication systems
 */
export type MessageStatus = 'sent' | 'delivered' | 'read' | 'failed';

/**
 * Visibility modes for sharing and permissions
 */
export type VisibilityMode = 'public' | 'private' | 'team' | 'all-except' | 'none-except';

/**
 * Permission levels for access control
 */
export type PermissionLevel = 'read' | 'write' | 'admin' | 'owner';

/**
 * Common date range interface
 */
export interface DateRange {
  from: string; // ISO date string
  to: string; // ISO date string
}

/**
 * Common pagination interface
 */
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Common API response wrapper
 */
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
  pagination?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

/**
 * Common error interface
 */
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
}

/**
 * Loading state interface
 */
export interface LoadingState {
  isLoading: boolean;
  error?: AppError;
  lastUpdated?: string;
}
