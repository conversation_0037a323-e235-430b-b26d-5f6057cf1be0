// Domain-specific type definitions that extend the base types

import { BaseEntity, User, Attachment, Comment, Priority, DateRange } from './index';

/**
 * Time Tracking Domain Types
 */
export interface TimeEntry extends BaseEntity {
  startTime: Date;
  endTime: Date;
  duration: string;
  project: string;
  task: string;
  notes: string;
  user?: User;
}

export type TimeTrackingStatus = "available" | "meeting" | "offline";

/**
 * Project Management Domain Types
 */
export interface Project extends BaseEntity {
  name: string;
  color: string;
  icon?: string;
  description?: string;
  deadline?: string; // ISO date string
  status: 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled';
  teamMembers: User[];
  progress: number; // 0-100
}

export interface Task extends BaseEntity {
  title: string;
  description?: string;
  status: 'todo' | 'inprogress' | 'review' | 'done';
  priority: Priority;
  dueDate?: string; // ISO date string
  assignees: User[];
  tag: string;
  project: Project;
  attachments: Attachment[];
  comments: Comment[];
  timeLogs?: TimeEntry[];
  estimatedHours?: number;
  actualHours?: number;
}

export interface ProjectTodo extends BaseEntity {
  title: string;
  completed: boolean;
  icon?: string;
  dueDate?: string; // ISO date string
  startDate?: string; // ISO date string
  assignees?: User[];
  projectId: string;
}

/**
 * Communication Domain Types
 */
export type ChatType = 'private' | 'group' | 'channel';

export interface ChatMessage extends BaseEntity {
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  content: string;
  timestamp: Date;
  status: 'sent' | 'delivered' | 'read';
  isOutgoing: boolean;
  replyTo?: {
    id: string;
    content: string;
    senderName: string;
  };
  attachments?: Attachment[];
}

export interface ChatConversation extends BaseEntity {
  title: string;
  avatar?: string;
  type: ChatType;
  lastMessage?: {
    content: string;
    timestamp: Date;
    senderId: string;
    senderName?: string;
    status: 'sent' | 'delivered' | 'read';
  };
  unreadCount: number;
  isOnline?: boolean;
  lastSeen?: Date;
  isTyping?: boolean;
  isPinned?: boolean;
  isMuted?: boolean;
  members?: User[];
  description?: string;
  icon?: string;
}

export interface Call extends BaseEntity {
  title: string;
  participants: User[];
  status: 'scheduled' | 'active' | 'ended' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  duration?: number; // in minutes
  type: 'audio' | 'video' | 'screen-share';
  recordingUrl?: string;
}

/**
 * File Management Domain Types
 */
export type FileSection = "my-files" | "shared" | "recent" | "categories";

export interface FileItem extends BaseEntity {
  name: string;
  type: string;
  path: string;
  section: FileSection;
  size: string;
  owner: string;
  modifiedAt: string;
  visibilityMode?: 'all-except' | 'none-except';
  allowedUsers?: string[];
  shareLink?: string;
  tags?: string[];
  version?: number;
}

export interface FolderItem extends BaseEntity {
  name: string;
  type: "folder";
  path: string;
  section: FileSection;
  items: number;
  owner: string;
  modifiedAt: string;
  visibilityMode?: 'all-except' | 'none-except';
  allowedUsers?: string[];
  shareLink?: string;
  parentId?: string;
}

/**
 * Employee Engagement Domain Types
 */
export interface Challenge extends BaseEntity {
  month: string; // In format "YYYY-MM"
  title: string;
  description: string;
  icon: string;
  winners?: string[]; // User IDs of winners
  badge: string;
  category: 'performance' | 'collaboration' | 'learning' | 'social';
  points: number;
  isActive: boolean;
}

export interface Kudos extends BaseEntity {
  from: string; // User ID who gave kudos
  to: string;   // User ID who received kudos
  count: number;
  month: string; // In format "YYYY-MM"
  date: string;  // ISO date string
  message?: string;
  category?: string;
}

export interface Recommendation extends BaseEntity {
  from: User;
  to: User;
  title: string;
  description: string;
  category: 'skill' | 'performance' | 'leadership' | 'collaboration';
  isPublic: boolean;
  endorsements?: User[];
}

/**
 * Organizational Requests Domain Types
 */
export type RequestType = 'leave' | 'loan' | 'overtime' | 'other';
export type RequestStatus = 'pending' | 'approved' | 'rejected' | 'cancelled';

export interface OrganizationalRequest extends BaseEntity {
  type: RequestType;
  title: string;
  description: string;
  status: RequestStatus;
  requestedBy: User;
  approvedBy?: User;
  dateRange?: DateRange;
  amount?: number;
  hours?: number;
  attachments?: Attachment[];
  comments?: Comment[];
  approvalDate?: string; // ISO date string
  rejectionReason?: string;
}
