/**
 * Storage utilities for localStorage and sessionStorage
 */

/**
 * Safe localStorage wrapper with error handling
 */
export class LocalStorage {
  private static isAvailable(): boolean {
    try {
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  static get<T>(key: string, defaultValue?: T): T | null {
    if (!this.isAvailable()) return defaultValue || null;
    
    try {
      const item = localStorage.getItem(key);
      if (item === null) return defaultValue || null;
      return JSON.parse(item);
    } catch (error) {
      console.warn(`Error reading from localStorage key "${key}":`, error);
      return defaultValue || null;
    }
  }

  static set<T>(key: string, value: T): boolean {
    if (!this.isAvailable()) return false;
    
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.warn(`<PERSON>rror writing to localStorage key "${key}":`, error);
      return false;
    }
  }

  static remove(key: string): boolean {
    if (!this.isAvailable()) return false;
    
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error);
      return false;
    }
  }

  static clear(): boolean {
    if (!this.isAvailable()) return false;
    
    try {
      localStorage.clear();
      return true;
    } catch (error) {
      console.warn('Error clearing localStorage:', error);
      return false;
    }
  }

  static keys(): string[] {
    if (!this.isAvailable()) return [];
    
    try {
      return Object.keys(localStorage);
    } catch (error) {
      console.warn('Error getting localStorage keys:', error);
      return [];
    }
  }
}

/**
 * Safe sessionStorage wrapper with error handling
 */
export class SessionStorage {
  private static isAvailable(): boolean {
    try {
      const test = '__storage_test__';
      sessionStorage.setItem(test, test);
      sessionStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  static get<T>(key: string, defaultValue?: T): T | null {
    if (!this.isAvailable()) return defaultValue || null;
    
    try {
      const item = sessionStorage.getItem(key);
      if (item === null) return defaultValue || null;
      return JSON.parse(item);
    } catch (error) {
      console.warn(`Error reading from sessionStorage key "${key}":`, error);
      return defaultValue || null;
    }
  }

  static set<T>(key: string, value: T): boolean {
    if (!this.isAvailable()) return false;
    
    try {
      sessionStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.warn(`Error writing to sessionStorage key "${key}":`, error);
      return false;
    }
  }

  static remove(key: string): boolean {
    if (!this.isAvailable()) return false;
    
    try {
      sessionStorage.removeItem(key);
      return true;
    } catch (error) {
      console.warn(`Error removing sessionStorage key "${key}":`, error);
      return false;
    }
  }

  static clear(): boolean {
    if (!this.isAvailable()) return false;
    
    try {
      sessionStorage.clear();
      return true;
    } catch (error) {
      console.warn('Error clearing sessionStorage:', error);
      return false;
    }
  }

  static keys(): string[] {
    if (!this.isAvailable()) return [];
    
    try {
      return Object.keys(sessionStorage);
    } catch (error) {
      console.warn('Error getting sessionStorage keys:', error);
      return [];
    }
  }
}

/**
 * Storage with expiration support
 */
interface StorageItem<T> {
  value: T;
  expiry?: number;
}

export class ExpiringStorage {
  private storage: typeof localStorage | typeof sessionStorage;

  constructor(storage: 'local' | 'session' = 'local') {
    this.storage = storage === 'local' ? localStorage : sessionStorage;
  }

  set<T>(key: string, value: T, expiryMinutes?: number): boolean {
    try {
      const item: StorageItem<T> = {
        value,
        expiry: expiryMinutes ? Date.now() + (expiryMinutes * 60 * 1000) : undefined
      };
      
      this.storage.setItem(key, JSON.stringify(item));
      return true;
    } catch (error) {
      console.warn(`Error setting expiring storage key "${key}":`, error);
      return false;
    }
  }

  get<T>(key: string, defaultValue?: T): T | null {
    try {
      const itemStr = this.storage.getItem(key);
      if (!itemStr) return defaultValue || null;

      const item: StorageItem<T> = JSON.parse(itemStr);
      
      // Check if item has expired
      if (item.expiry && Date.now() > item.expiry) {
        this.storage.removeItem(key);
        return defaultValue || null;
      }

      return item.value;
    } catch (error) {
      console.warn(`Error getting expiring storage key "${key}":`, error);
      return defaultValue || null;
    }
  }

  remove(key: string): boolean {
    try {
      this.storage.removeItem(key);
      return true;
    } catch (error) {
      console.warn(`Error removing expiring storage key "${key}":`, error);
      return false;
    }
  }

  clear(): boolean {
    try {
      this.storage.clear();
      return true;
    } catch (error) {
      console.warn('Error clearing expiring storage:', error);
      return false;
    }
  }

  /**
   * Clean up expired items
   */
  cleanup(): number {
    let cleanedCount = 0;
    
    try {
      const keys = Object.keys(this.storage);
      
      for (const key of keys) {
        const itemStr = this.storage.getItem(key);
        if (!itemStr) continue;

        try {
          const item: StorageItem<any> = JSON.parse(itemStr);
          if (item.expiry && Date.now() > item.expiry) {
            this.storage.removeItem(key);
            cleanedCount++;
          }
        } catch {
          // Invalid JSON, skip this item
        }
      }
    } catch (error) {
      console.warn('Error during storage cleanup:', error);
    }
    
    return cleanedCount;
  }
}

/**
 * Cookie utilities
 */
export class CookieStorage {
  static get(name: string): string | null {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      return parts.pop()?.split(';').shift() || null;
    }
    return null;
  }

  static set(name: string, value: string, days?: number, path: string = '/'): void {
    let expires = '';
    if (days) {
      const date = new Date();
      date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
      expires = `; expires=${date.toUTCString()}`;
    }
    document.cookie = `${name}=${value}${expires}; path=${path}`;
  }

  static remove(name: string, path: string = '/'): void {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}`;
  }

  static exists(name: string): boolean {
    return this.get(name) !== null;
  }
}
