/**
 * ID generation utilities
 * Provides consistent ID generation across the application
 */

/**
 * Generate a unique ID with optional prefix
 * Replaces scattered Date.now() and Math.random() usage
 */
export function generateId(prefix?: string): string {
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substring(2, 8);
  const id = `${timestamp}${randomPart}`;
  
  return prefix ? `${prefix}-${id}` : id;
}

/**
 * Generate UUID v4 (more robust for production use)
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Generate short ID for UI display
 */
export function generateShortId(length: number = 6): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Validate ID format
 */
export function isValidId(id: string): boolean {
  return typeof id === 'string' && id.length > 0 && id.trim() === id;
}

/**
 * Extract prefix from prefixed ID
 */
export function extractPrefix(id: string): string | null {
  const parts = id.split('-');
  return parts.length > 1 ? parts[0] : null;
}

/**
 * Domain-specific ID generators
 */
export const IdGenerators = {
  user: () => generateId('user'),
  project: () => generateId('proj'),
  task: () => generateId('task'),
  timeEntry: () => generateId('time'),
  chat: () => generateId('chat'),
  message: () => generateId('msg'),
  file: () => generateId('file'),
  folder: () => generateId('folder'),
  kudos: () => generateId('kudos'),
  challenge: () => generateId('challenge'),
  recommendation: () => generateId('rec'),
  request: () => generateId('req'),
  comment: () => generateId('comment'),
  attachment: () => generateId('attach'),
} as const;
