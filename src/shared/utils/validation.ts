/**
 * Validation utilities
 * Provides consistent validation logic across the application
 */

/**
 * Email validation
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Required field validation
 */
export function isRequired(value: any): boolean {
  if (value === null || value === undefined) return false;
  if (typeof value === 'string') return value.trim().length > 0;
  if (Array.isArray(value)) return value.length > 0;
  return true;
}

/**
 * String length validation
 */
export function hasValidLength(value: string, min: number = 0, max: number = Infinity): boolean {
  if (!value) return min === 0;
  const length = value.trim().length;
  return length >= min && length <= max;
}

/**
 * Number range validation
 */
export function isInRange(value: number, min: number = -Infinity, max: number = Infinity): boolean {
  return typeof value === 'number' && !isNaN(value) && value >= min && value <= max;
}

/**
 * URL validation
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Phone number validation (basic)
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  return phoneRegex.test(phone);
}

/**
 * Password strength validation
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  score: number;
  feedback: string[];
} {
  const feedback: string[] = [];
  let score = 0;
  
  if (password.length < 8) {
    feedback.push('Password must be at least 8 characters long');
  } else {
    score += 1;
  }
  
  if (!/[a-z]/.test(password)) {
    feedback.push('Password must contain at least one lowercase letter');
  } else {
    score += 1;
  }
  
  if (!/[A-Z]/.test(password)) {
    feedback.push('Password must contain at least one uppercase letter');
  } else {
    score += 1;
  }
  
  if (!/\d/.test(password)) {
    feedback.push('Password must contain at least one number');
  } else {
    score += 1;
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    feedback.push('Password must contain at least one special character');
  } else {
    score += 1;
  }
  
  return {
    isValid: score >= 4,
    score,
    feedback
  };
}

/**
 * File validation
 */
export function validateFile(file: File, options: {
  maxSize?: number; // in bytes
  allowedTypes?: string[];
  allowedExtensions?: string[];
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (options.maxSize && file.size > options.maxSize) {
    errors.push(`File size must be less than ${formatFileSize(options.maxSize)}`);
  }
  
  if (options.allowedTypes && !options.allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed`);
  }
  
  if (options.allowedExtensions) {
    const extension = file.name.split('.').pop()?.toLowerCase();
    if (!extension || !options.allowedExtensions.includes(extension)) {
      errors.push(`File extension must be one of: ${options.allowedExtensions.join(', ')}`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Format file size for display
 */
function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

/**
 * Generic form validation
 */
export interface ValidationRule<T = any> {
  validator: (value: T) => boolean;
  message: string;
}

export function validateField<T>(value: T, rules: ValidationRule<T>[]): string[] {
  const errors: string[] = [];
  
  for (const rule of rules) {
    if (!rule.validator(value)) {
      errors.push(rule.message);
    }
  }
  
  return errors;
}

/**
 * Common validation rules
 */
export const ValidationRules = {
  required: (message: string = 'This field is required'): ValidationRule => ({
    validator: isRequired,
    message
  }),
  
  email: (message: string = 'Please enter a valid email address'): ValidationRule<string> => ({
    validator: isValidEmail,
    message
  }),
  
  minLength: (min: number, message?: string): ValidationRule<string> => ({
    validator: (value: string) => hasValidLength(value, min),
    message: message || `Must be at least ${min} characters long`
  }),
  
  maxLength: (max: number, message?: string): ValidationRule<string> => ({
    validator: (value: string) => hasValidLength(value, 0, max),
    message: message || `Must be no more than ${max} characters long`
  }),
  
  range: (min: number, max: number, message?: string): ValidationRule<number> => ({
    validator: (value: number) => isInRange(value, min, max),
    message: message || `Must be between ${min} and ${max}`
  }),
  
  url: (message: string = 'Please enter a valid URL'): ValidationRule<string> => ({
    validator: isValidUrl,
    message
  })
} as const;

/**
 * Validate entire form object
 */
export function validateForm<T extends Record<string, any>>(
  data: T,
  rules: Partial<Record<keyof T, ValidationRule[]>>
): { isValid: boolean; errors: Partial<Record<keyof T, string[]>> } {
  const errors: Partial<Record<keyof T, string[]>> = {};
  let isValid = true;
  
  for (const [field, fieldRules] of Object.entries(rules)) {
    if (fieldRules && Array.isArray(fieldRules)) {
      const fieldErrors = validateField(data[field], fieldRules);
      if (fieldErrors.length > 0) {
        errors[field as keyof T] = fieldErrors;
        isValid = false;
      }
    }
  }
  
  return { isValid, errors };
}
