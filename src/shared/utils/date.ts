/**
 * Date formatting and manipulation utilities
 * Centralizes all date-related operations
 */

import { format, parseISO, isValid, differenceInMinutes, differenceInHours, differenceInDays } from 'date-fns';

/**
 * Enhanced date formatting function that replaces the existing formatDate utility
 */
export function formatDate(dateInput: string | number | Date, dateFormat: string = 'MMM dd, yyyy'): string {
  if (!dateInput) return '';
  
  try {
    const date = typeof dateInput === 'string' || typeof dateInput === 'number' 
      ? new Date(dateInput) 
      : dateInput;
    
    if (!isValid(date)) {
      console.warn('Invalid date provided to formatDate:', dateInput);
      return '';
    }
    
    return format(date, dateFormat);
  } catch (err) {
    console.error('formatDate error:', err);
    return '';
  }
}

/**
 * Format date for display in different contexts
 */
export const DateFormatters = {
  short: (date: string | Date) => formatDate(date, 'MMM dd'),
  medium: (date: string | Date) => formatDate(date, 'MMM dd, yyyy'),
  long: (date: string | Date) => formatDate(date, 'MMMM dd, yyyy'),
  time: (date: string | Date) => formatDate(date, 'HH:mm'),
  dateTime: (date: string | Date) => formatDate(date, 'MMM dd, yyyy HH:mm'),
  iso: (date: string | Date) => {
    const d = typeof date === 'string' ? parseISO(date) : date;
    return d.toISOString();
  },
  relative: (date: string | Date) => {
    const d = typeof date === 'string' ? parseISO(date) : date;
    const now = new Date();
    const diffInMinutes = differenceInMinutes(now, d);
    const diffInHours = differenceInHours(now, d);
    const diffInDays = differenceInDays(now, d);
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return formatDate(date, 'MMM dd');
  }
} as const;

/**
 * Duration formatting for time tracking
 */
export function formatDuration(start: Date, end: Date): string {
  const diff = Math.floor((end.getTime() - start.getTime()) / 1000);
  const hours = Math.floor(diff / 3600).toString().padStart(2, '0');
  const minutes = Math.floor((diff % 3600) / 60).toString().padStart(2, '0');
  const seconds = (diff % 60).toString().padStart(2, '0');
  return `${hours}:${minutes}:${seconds}`;
}

/**
 * Parse duration string to minutes
 */
export function parseDurationToMinutes(duration: string): number {
  const parts = duration.split(':');
  if (parts.length !== 3) return 0;
  
  const hours = parseInt(parts[0], 10) || 0;
  const minutes = parseInt(parts[1], 10) || 0;
  const seconds = parseInt(parts[2], 10) || 0;
  
  return hours * 60 + minutes + Math.round(seconds / 60);
}

/**
 * Format minutes to human readable duration
 */
export function formatMinutesToDuration(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  
  if (hours === 0) return `${mins}m`;
  if (mins === 0) return `${hours}h`;
  return `${hours}h ${mins}m`;
}

/**
 * Check if date is today
 */
export function isToday(date: string | Date): boolean {
  const d = typeof date === 'string' ? parseISO(date) : date;
  const today = new Date();
  return d.toDateString() === today.toDateString();
}

/**
 * Check if date is overdue
 */
export function isOverdue(date: string | Date): boolean {
  const d = typeof date === 'string' ? parseISO(date) : date;
  return d < new Date();
}

/**
 * Get date range for common periods
 */
export function getDateRange(period: 'today' | 'week' | 'month' | 'year'): { start: Date; end: Date } {
  const now = new Date();
  const start = new Date(now);
  const end = new Date(now);
  
  switch (period) {
    case 'today':
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
      break;
    case 'week':
      const dayOfWeek = now.getDay();
      start.setDate(now.getDate() - dayOfWeek);
      start.setHours(0, 0, 0, 0);
      end.setDate(start.getDate() + 6);
      end.setHours(23, 59, 59, 999);
      break;
    case 'month':
      start.setDate(1);
      start.setHours(0, 0, 0, 0);
      end.setMonth(start.getMonth() + 1, 0);
      end.setHours(23, 59, 59, 999);
      break;
    case 'year':
      start.setMonth(0, 1);
      start.setHours(0, 0, 0, 0);
      end.setMonth(11, 31);
      end.setHours(23, 59, 59, 999);
      break;
  }
  
  return { start, end };
}

/**
 * Validate date string
 */
export function isValidDateString(dateString: string): boolean {
  if (!dateString) return false;
  const date = parseISO(dateString);
  return isValid(date);
}

/**
 * Get current timestamp in ISO format
 */
export function getCurrentTimestamp(): string {
  return new Date().toISOString();
}
