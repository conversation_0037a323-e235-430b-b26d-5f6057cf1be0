/**
 * Service registry for dependency injection and service management
 * Implements dependency inversion principle
 */

/**
 * Service registry interface
 */
export interface ServiceRegistry {
  register<T>(key: string, service: T): void;
  get<T>(key: string): T;
  has(key: string): boolean;
  remove(key: string): boolean;
  clear(): void;
}

/**
 * Simple service registry implementation
 */
export class SimpleServiceRegistry implements ServiceRegistry {
  private services = new Map<string, any>();

  /**
   * Register a service
   */
  register<T>(key: string, service: T): void {
    this.services.set(key, service);
  }

  /**
   * Get a service
   */
  get<T>(key: string): T {
    const service = this.services.get(key);
    if (!service) {
      throw new Error(`Service '${key}' not found in registry`);
    }
    return service;
  }

  /**
   * Check if service exists
   */
  has(key: string): boolean {
    return this.services.has(key);
  }

  /**
   * Remove a service
   */
  remove(key: string): boolean {
    return this.services.delete(key);
  }

  /**
   * Clear all services
   */
  clear(): void {
    this.services.clear();
  }

  /**
   * Get all registered service keys
   */
  getKeys(): string[] {
    return Array.from(this.services.keys());
  }
}

/**
 * Global service registry instance
 */
export const serviceRegistry = new SimpleServiceRegistry();

/**
 * Service registration decorator
 */
export function Service(key: string) {
  return function <T extends new (...args: any[]) => any>(constructor: T) {
    // Register the service class
    serviceRegistry.register(key, constructor);
    return constructor;
  };
}

/**
 * Inject service decorator
 */
export function Inject(key: string) {
  return function (target: any, propertyKey: string) {
    // Define getter that retrieves service from registry
    Object.defineProperty(target, propertyKey, {
      get() {
        return serviceRegistry.get(key);
      },
      enumerable: true,
      configurable: true
    });
  };
}

/**
 * Service factory interface
 */
export interface ServiceFactory<T> {
  create(): T;
}

/**
 * Lazy service registry that creates services on demand
 */
export class LazyServiceRegistry implements ServiceRegistry {
  private services = new Map<string, any>();
  private factories = new Map<string, ServiceFactory<any>>();

  /**
   * Register a service instance
   */
  register<T>(key: string, service: T): void {
    this.services.set(key, service);
  }

  /**
   * Register a service factory
   */
  registerFactory<T>(key: string, factory: ServiceFactory<T>): void {
    this.factories.set(key, factory);
  }

  /**
   * Get a service (create if needed)
   */
  get<T>(key: string): T {
    // Check if service instance exists
    if (this.services.has(key)) {
      return this.services.get(key);
    }

    // Check if factory exists
    if (this.factories.has(key)) {
      const factory = this.factories.get(key);
      const service = factory.create();
      this.services.set(key, service);
      return service;
    }

    throw new Error(`Service '${key}' not found in registry`);
  }

  /**
   * Check if service or factory exists
   */
  has(key: string): boolean {
    return this.services.has(key) || this.factories.has(key);
  }

  /**
   * Remove a service and its factory
   */
  remove(key: string): boolean {
    const serviceRemoved = this.services.delete(key);
    const factoryRemoved = this.factories.delete(key);
    return serviceRemoved || factoryRemoved;
  }

  /**
   * Clear all services and factories
   */
  clear(): void {
    this.services.clear();
    this.factories.clear();
  }

  /**
   * Get all registered keys
   */
  getKeys(): string[] {
    const serviceKeys = Array.from(this.services.keys());
    const factoryKeys = Array.from(this.factories.keys());
    return [...new Set([...serviceKeys, ...factoryKeys])];
  }
}

/**
 * Service configuration interface
 */
export interface ServiceConfig {
  singleton?: boolean;
  lazy?: boolean;
  dependencies?: string[];
}

/**
 * Advanced service registry with configuration support
 */
export class ConfigurableServiceRegistry implements ServiceRegistry {
  private services = new Map<string, any>();
  private factories = new Map<string, ServiceFactory<any>>();
  private configs = new Map<string, ServiceConfig>();
  private singletons = new Map<string, any>();

  /**
   * Register a service with configuration
   */
  registerWithConfig<T>(key: string, service: T | ServiceFactory<T>, config: ServiceConfig = {}): void {
    this.configs.set(key, config);
    
    if (typeof service === 'object' && 'create' in service) {
      this.factories.set(key, service as ServiceFactory<T>);
    } else {
      this.services.set(key, service);
    }
  }

  /**
   * Register a service (default implementation)
   */
  register<T>(key: string, service: T): void {
    this.registerWithConfig(key, service);
  }

  /**
   * Get a service with configuration support
   */
  get<T>(key: string): T {
    const config = this.configs.get(key) || {};

    // Check for singleton
    if (config.singleton && this.singletons.has(key)) {
      return this.singletons.get(key);
    }

    let service: T;

    // Get from service instance
    if (this.services.has(key)) {
      service = this.services.get(key);
    }
    // Create from factory
    else if (this.factories.has(key)) {
      const factory = this.factories.get(key);
      service = factory.create();
    }
    else {
      throw new Error(`Service '${key}' not found in registry`);
    }

    // Store singleton
    if (config.singleton) {
      this.singletons.set(key, service);
    }

    return service;
  }

  /**
   * Check if service exists
   */
  has(key: string): boolean {
    return this.services.has(key) || this.factories.has(key);
  }

  /**
   * Remove a service
   */
  remove(key: string): boolean {
    const serviceRemoved = this.services.delete(key);
    const factoryRemoved = this.factories.delete(key);
    const configRemoved = this.configs.delete(key);
    const singletonRemoved = this.singletons.delete(key);
    
    return serviceRemoved || factoryRemoved || configRemoved || singletonRemoved;
  }

  /**
   * Clear all services
   */
  clear(): void {
    this.services.clear();
    this.factories.clear();
    this.configs.clear();
    this.singletons.clear();
  }
}
