/**
 * Mock repository implementation for development and testing
 * Provides in-memory data storage with full CRUD operations
 */

import { Repository } from './BaseService';
import { ApiResponse, PaginationParams, BaseEntity } from '../../types';
import { generateId } from '../../utils/id';
import { getCurrentTimestamp } from '../../utils/date';

/**
 * Generic mock repository that stores data in memory
 */
export class MockRepository<T extends BaseEntity, CreateInput = Partial<T>, UpdateInput = Partial<T>> 
  implements Repository<T, CreateInput, UpdateInput> {
  
  protected data: Map<string, T> = new Map();
  protected entityName: string;

  constructor(entityName: string, initialData: T[] = []) {
    this.entityName = entityName;
    initialData.forEach(item => this.data.set(item.id, item));
  }

  /**
   * Find all entities with pagination
   */
  async findAll(params?: PaginationParams): Promise<ApiResponse<T[]>> {
    try {
      let items = Array.from(this.data.values());

      // Apply sorting
      if (params?.sortBy) {
        items = this.sortItems(items, params.sortBy, params.sortOrder || 'asc');
      }

      // Apply pagination
      const total = items.length;
      if (params?.page && params?.limit) {
        const startIndex = (params.page - 1) * params.limit;
        const endIndex = startIndex + params.limit;
        items = items.slice(startIndex, endIndex);
      }

      return {
        data: items,
        success: true,
        pagination: params?.page && params?.limit ? {
          total,
          page: params.page,
          limit: params.limit,
          totalPages: Math.ceil(total / params.limit)
        } : undefined
      };
    } catch (error) {
      return this.createErrorResponse(`Failed to fetch ${this.entityName}s`, error);
    }
  }

  /**
   * Find entity by ID
   */
  async findById(id: string): Promise<ApiResponse<T | null>> {
    try {
      const item = this.data.get(id) || null;
      return {
        data: item,
        success: true,
        message: item ? undefined : `${this.entityName} not found`
      };
    } catch (error) {
      return this.createErrorResponse(`Failed to fetch ${this.entityName}`, error);
    }
  }

  /**
   * Create new entity
   */
  async create(data: CreateInput): Promise<ApiResponse<T>> {
    try {
      const now = getCurrentTimestamp();
      const newItem: T = {
        ...data,
        id: generateId(),
        createdAt: now,
        updatedAt: now
      } as T;

      this.data.set(newItem.id, newItem);

      return {
        data: newItem,
        success: true,
        message: `${this.entityName} created successfully`
      };
    } catch (error) {
      return this.createErrorResponse(`Failed to create ${this.entityName}`, error);
    }
  }

  /**
   * Update existing entity
   */
  async update(id: string, data: UpdateInput): Promise<ApiResponse<T>> {
    try {
      const existingItem = this.data.get(id);
      if (!existingItem) {
        return {
          data: null,
          success: false,
          message: `${this.entityName} not found`,
          errors: [`${this.entityName} with ID ${id} does not exist`]
        };
      }

      const updatedItem: T = {
        ...existingItem,
        ...data,
        id, // Ensure ID doesn't change
        updatedAt: getCurrentTimestamp()
      } as T;

      this.data.set(id, updatedItem);

      return {
        data: updatedItem,
        success: true,
        message: `${this.entityName} updated successfully`
      };
    } catch (error) {
      return this.createErrorResponse(`Failed to update ${this.entityName}`, error);
    }
  }

  /**
   * Delete entity
   */
  async delete(id: string): Promise<ApiResponse<boolean>> {
    try {
      const existed = this.data.has(id);
      if (!existed) {
        return {
          data: false,
          success: false,
          message: `${this.entityName} not found`,
          errors: [`${this.entityName} with ID ${id} does not exist`]
        };
      }

      this.data.delete(id);

      return {
        data: true,
        success: true,
        message: `${this.entityName} deleted successfully`
      };
    } catch (error) {
      return this.createErrorResponse(`Failed to delete ${this.entityName}`, error);
    }
  }

  /**
   * Search entities by query
   */
  async search(query: string, params?: PaginationParams): Promise<ApiResponse<T[]>> {
    try {
      const searchTerm = query.toLowerCase();
      let items = Array.from(this.data.values()).filter(item => 
        this.matchesSearchQuery(item, searchTerm)
      );

      // Apply sorting
      if (params?.sortBy) {
        items = this.sortItems(items, params.sortBy, params.sortOrder || 'asc');
      }

      // Apply pagination
      const total = items.length;
      if (params?.page && params?.limit) {
        const startIndex = (params.page - 1) * params.limit;
        const endIndex = startIndex + params.limit;
        items = items.slice(startIndex, endIndex);
      }

      return {
        data: items,
        success: true,
        pagination: params?.page && params?.limit ? {
          total,
          page: params.page,
          limit: params.limit,
          totalPages: Math.ceil(total / params.limit)
        } : undefined
      };
    } catch (error) {
      return this.createErrorResponse(`Failed to search ${this.entityName}s`, error);
    }
  }

  /**
   * Get all data (for testing/debugging)
   */
  getAllData(): T[] {
    return Array.from(this.data.values());
  }

  /**
   * Clear all data
   */
  clear(): void {
    this.data.clear();
  }

  /**
   * Seed data
   */
  seed(items: T[]): void {
    this.clear();
    items.forEach(item => this.data.set(item.id, item));
  }

  /**
   * Check if entity matches search query
   * Override in subclasses for custom search logic
   */
  protected matchesSearchQuery(item: T, searchTerm: string): boolean {
    // Default implementation: search in string properties
    const searchableFields = this.getSearchableFields(item);
    return searchableFields.some(field => 
      field && field.toLowerCase().includes(searchTerm)
    );
  }

  /**
   * Get searchable fields from entity
   * Override in subclasses for custom searchable fields
   */
  protected getSearchableFields(item: T): string[] {
    const fields: string[] = [];
    
    // Add common searchable fields
    if ('name' in item && typeof item.name === 'string') {
      fields.push(item.name);
    }
    if ('title' in item && typeof item.title === 'string') {
      fields.push(item.title);
    }
    if ('description' in item && typeof item.description === 'string') {
      fields.push(item.description);
    }
    if ('content' in item && typeof item.content === 'string') {
      fields.push(item.content);
    }
    
    return fields;
  }

  /**
   * Sort items by field
   */
  protected sortItems(items: T[], sortBy: string, sortOrder: 'asc' | 'desc'): T[] {
    return [...items].sort((a, b) => {
      const aVal = (a as any)[sortBy];
      const bVal = (b as any)[sortBy];
      
      if (aVal < bVal) return sortOrder === 'asc' ? -1 : 1;
      if (aVal > bVal) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
  }

  /**
   * Create error response
   */
  protected createErrorResponse(message: string, error: any): ApiResponse<any> {
    console.error(message, error);
    return {
      data: null,
      success: false,
      message,
      errors: [error.message || message]
    };
  }
}
