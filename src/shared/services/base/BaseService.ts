/**
 * Base service class that provides common functionality for all services
 * Implements dependency inversion principle by depending on abstractions
 */

import { ApiResponse, AppError, PaginationParams } from '../../types';

/**
 * Generic repository interface for data operations
 */
export interface Repository<T, CreateInput = Partial<T>, UpdateInput = Partial<T>> {
  findAll(params?: PaginationParams): Promise<ApiResponse<T[]>>;
  findById(id: string): Promise<ApiResponse<T | null>>;
  create(data: CreateInput): Promise<ApiResponse<T>>;
  update(id: string, data: UpdateInput): Promise<ApiResponse<T>>;
  delete(id: string): Promise<ApiResponse<boolean>>;
  search(query: string, params?: PaginationParams): Promise<ApiResponse<T[]>>;
}

/**
 * Base service class with common CRUD operations
 */
export abstract class BaseService<T, CreateInput = Partial<T>, UpdateInput = Partial<T>> {
  protected repository: Repository<T, CreateInput, UpdateInput>;

  constructor(repository: Repository<T, CreateInput, UpdateInput>) {
    this.repository = repository;
  }

  /**
   * Get all entities with optional pagination
   */
  async getAll(params?: PaginationParams): Promise<ApiResponse<T[]>> {
    try {
      return await this.repository.findAll(params);
    } catch (error) {
      return this.handleError(error, 'Failed to fetch entities');
    }
  }

  /**
   * Get entity by ID
   */
  async getById(id: string): Promise<ApiResponse<T | null>> {
    try {
      if (!id) {
        return this.createErrorResponse('ID is required', 'INVALID_INPUT');
      }
      return await this.repository.findById(id);
    } catch (error) {
      return this.handleError(error, `Failed to fetch entity with ID: ${id}`);
    }
  }

  /**
   * Create new entity
   */
  async create(data: CreateInput): Promise<ApiResponse<T>> {
    try {
      const validationResult = await this.validateCreate(data);
      if (!validationResult.isValid) {
        return this.createErrorResponse('Validation failed', 'VALIDATION_ERROR', validationResult.errors);
      }

      const processedData = await this.preprocessCreate(data);
      const result = await this.repository.create(processedData);
      
      if (result.success && result.data) {
        await this.postprocessCreate(result.data);
      }
      
      return result;
    } catch (error) {
      return this.handleError(error, 'Failed to create entity');
    }
  }

  /**
   * Update existing entity
   */
  async update(id: string, data: UpdateInput): Promise<ApiResponse<T>> {
    try {
      if (!id) {
        return this.createErrorResponse('ID is required', 'INVALID_INPUT');
      }

      const validationResult = await this.validateUpdate(id, data);
      if (!validationResult.isValid) {
        return this.createErrorResponse('Validation failed', 'VALIDATION_ERROR', validationResult.errors);
      }

      const processedData = await this.preprocessUpdate(id, data);
      const result = await this.repository.update(id, processedData);
      
      if (result.success && result.data) {
        await this.postprocessUpdate(result.data);
      }
      
      return result;
    } catch (error) {
      return this.handleError(error, `Failed to update entity with ID: ${id}`);
    }
  }

  /**
   * Delete entity
   */
  async delete(id: string): Promise<ApiResponse<boolean>> {
    try {
      if (!id) {
        return this.createErrorResponse('ID is required', 'INVALID_INPUT');
      }

      const canDelete = await this.canDelete(id);
      if (!canDelete.allowed) {
        return this.createErrorResponse(canDelete.reason || 'Cannot delete entity', 'DELETE_NOT_ALLOWED');
      }

      await this.preprocessDelete(id);
      const result = await this.repository.delete(id);
      
      if (result.success) {
        await this.postprocessDelete(id);
      }
      
      return result;
    } catch (error) {
      return this.handleError(error, `Failed to delete entity with ID: ${id}`);
    }
  }

  /**
   * Search entities
   */
  async search(query: string, params?: PaginationParams): Promise<ApiResponse<T[]>> {
    try {
      if (!query || query.trim().length === 0) {
        return this.createErrorResponse('Search query is required', 'INVALID_INPUT');
      }
      return await this.repository.search(query, params);
    } catch (error) {
      return this.handleError(error, 'Failed to search entities');
    }
  }

  // Protected methods for subclasses to override

  /**
   * Validate data before creating entity
   */
  protected async validateCreate(data: CreateInput): Promise<{ isValid: boolean; errors?: string[] }> {
    return { isValid: true };
  }

  /**
   * Validate data before updating entity
   */
  protected async validateUpdate(id: string, data: UpdateInput): Promise<{ isValid: boolean; errors?: string[] }> {
    return { isValid: true };
  }

  /**
   * Check if entity can be deleted
   */
  protected async canDelete(id: string): Promise<{ allowed: boolean; reason?: string }> {
    return { allowed: true };
  }

  /**
   * Preprocess data before creating
   */
  protected async preprocessCreate(data: CreateInput): Promise<CreateInput> {
    return data;
  }

  /**
   * Preprocess data before updating
   */
  protected async preprocessUpdate(id: string, data: UpdateInput): Promise<UpdateInput> {
    return data;
  }

  /**
   * Preprocess before deleting
   */
  protected async preprocessDelete(id: string): Promise<void> {
    // Override in subclasses if needed
  }

  /**
   * Postprocess after creating
   */
  protected async postprocessCreate(entity: T): Promise<void> {
    // Override in subclasses if needed
  }

  /**
   * Postprocess after updating
   */
  protected async postprocessUpdate(entity: T): Promise<void> {
    // Override in subclasses if needed
  }

  /**
   * Postprocess after deleting
   */
  protected async postprocessDelete(id: string): Promise<void> {
    // Override in subclasses if needed
  }

  /**
   * Handle errors consistently
   */
  protected handleError(error: any, message: string): ApiResponse<any> {
    console.error(message, error);
    
    const appError: AppError = {
      code: error.code || 'INTERNAL_ERROR',
      message: error.message || message,
      details: error.details || {},
      timestamp: new Date().toISOString()
    };

    return {
      data: null,
      success: false,
      message,
      errors: [appError.message]
    };
  }

  /**
   * Create error response
   */
  protected createErrorResponse(message: string, code: string, errors?: string[]): ApiResponse<any> {
    return {
      data: null,
      success: false,
      message,
      errors: errors || [message]
    };
  }

  /**
   * Create success response
   */
  protected createSuccessResponse<U>(data: U, message?: string): ApiResponse<U> {
    return {
      data,
      success: true,
      message
    };
  }
}
