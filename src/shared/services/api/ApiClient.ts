/**
 * API client for HTTP requests
 * Provides consistent interface for API communication
 */

import { ApiResponse, AppError } from '../../types';

/**
 * HTTP methods
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

/**
 * Request configuration
 */
export interface RequestConfig {
  method?: HttpMethod;
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

/**
 * API client interface
 */
export interface ApiClient {
  get<T>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>;
  post<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>;
  put<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>;
  patch<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>;
  delete<T>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>;
  request<T>(url: string, config: RequestConfig): Promise<ApiResponse<T>>;
}

/**
 * Fetch-based API client implementation
 */
export class FetchApiClient implements ApiClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;
  private defaultTimeout: number;

  constructor(
    baseUrl: string = '',
    defaultHeaders: Record<string, string> = {},
    defaultTimeout: number = 10000
  ) {
    this.baseUrl = baseUrl;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...defaultHeaders
    };
    this.defaultTimeout = defaultTimeout;
  }

  /**
   * GET request
   */
  async get<T>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(url, { ...config, method: 'GET' });
  }

  /**
   * POST request
   */
  async post<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(url, { ...config, method: 'POST', body: data });
  }

  /**
   * PUT request
   */
  async put<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(url, { ...config, method: 'PUT', body: data });
  }

  /**
   * PATCH request
   */
  async patch<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(url, { ...config, method: 'PATCH', body: data });
  }

  /**
   * DELETE request
   */
  async delete<T>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(url, { ...config, method: 'DELETE' });
  }

  /**
   * Generic request method
   */
  async request<T>(url: string, config: RequestConfig): Promise<ApiResponse<T>> {
    const fullUrl = this.buildUrl(url);
    const requestConfig = this.buildRequestConfig(config);
    
    try {
      const response = await this.executeRequest(fullUrl, requestConfig);
      return await this.handleResponse<T>(response);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Build full URL
   */
  private buildUrl(url: string): string {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }
    return `${this.baseUrl}${url.startsWith('/') ? url : `/${url}`}`;
  }

  /**
   * Build request configuration
   */
  private buildRequestConfig(config: RequestConfig): RequestInit {
    const headers = {
      ...this.defaultHeaders,
      ...config.headers
    };

    const requestInit: RequestInit = {
      method: config.method || 'GET',
      headers
    };

    // Add body for non-GET requests
    if (config.body && config.method !== 'GET') {
      if (typeof config.body === 'object') {
        requestInit.body = JSON.stringify(config.body);
      } else {
        requestInit.body = config.body;
      }
    }

    return requestInit;
  }

  /**
   * Execute request with timeout and retries
   */
  private async executeRequest(url: string, config: RequestInit): Promise<Response> {
    const timeout = config.timeout || this.defaultTimeout;
    const retries = config.retries || 0;
    const retryDelay = config.retryDelay || 1000;

    let lastError: Error;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url, {
          ...config,
          signal: controller.signal
        });

        clearTimeout(timeoutId);
        return response;
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < retries) {
          await this.delay(retryDelay * Math.pow(2, attempt)); // Exponential backoff
        }
      }
    }

    throw lastError!;
  }

  /**
   * Handle response
   */
  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    try {
      const data = await response.json();

      if (response.ok) {
        return {
          data,
          success: true,
          message: response.statusText
        };
      } else {
        return {
          data: null,
          success: false,
          message: data.message || response.statusText,
          errors: data.errors || [data.message || response.statusText]
        };
      }
    } catch (error) {
      if (response.ok) {
        // Response is OK but not JSON (e.g., empty response)
        return {
          data: null as T,
          success: true,
          message: response.statusText
        };
      } else {
        return {
          data: null,
          success: false,
          message: `HTTP ${response.status}: ${response.statusText}`,
          errors: [`HTTP ${response.status}: ${response.statusText}`]
        };
      }
    }
  }

  /**
   * Handle errors
   */
  private handleError(error: any): ApiResponse<any> {
    const appError: AppError = {
      code: error.name || 'NETWORK_ERROR',
      message: error.message || 'Network request failed',
      details: { error },
      timestamp: new Date().toISOString()
    };

    return {
      data: null,
      success: false,
      message: appError.message,
      errors: [appError.message]
    };
  }

  /**
   * Delay utility for retries
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Set default header
   */
  setDefaultHeader(key: string, value: string): void {
    this.defaultHeaders[key] = value;
  }

  /**
   * Remove default header
   */
  removeDefaultHeader(key: string): void {
    delete this.defaultHeaders[key];
  }

  /**
   * Set authorization header
   */
  setAuthToken(token: string): void {
    this.setDefaultHeader('Authorization', `Bearer ${token}`);
  }

  /**
   * Remove authorization header
   */
  removeAuthToken(): void {
    this.removeDefaultHeader('Authorization');
  }
}

/**
 * Default API client instance
 */
export const apiClient = new FetchApiClient();
