/**
 * Icon Component
 * Unified icon system with consistent sizing and styling
 */

import React from 'react';
import { Icon as IconifyIcon } from '@iconify/react';

/**
 * Icon size presets
 */
export type IconSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

/**
 * Icon props
 */
export interface IconProps {
  icon: string;
  size?: IconSize | number;
  className?: string;
  color?: string;
  rotate?: number;
  flip?: 'horizontal' | 'vertical' | 'both';
  onClick?: () => void;
  'aria-label'?: string;
}

/**
 * Size mapping
 */
const sizeMap: Record<IconSize, string> = {
  xs: 'w-3 h-3',
  sm: 'w-4 h-4',
  md: 'w-5 h-5',
  lg: 'w-6 h-6',
  xl: 'w-8 h-8',
  '2xl': 'w-10 h-10'
};

/**
 * Icon component with consistent sizing and behavior
 */
export const Icon: React.FC<IconProps> = ({
  icon,
  size = 'md',
  className = '',
  color,
  rotate,
  flip,
  onClick,
  'aria-label': ariaLabel,
  ...props
}) => {
  const sizeClass = typeof size === 'string' ? sizeMap[size] : '';
  const clickableClass = onClick ? 'cursor-pointer' : '';
  
  const style: React.CSSProperties = {
    color,
    transform: [
      rotate ? `rotate(${rotate}deg)` : '',
      flip === 'horizontal' ? 'scaleX(-1)' : '',
      flip === 'vertical' ? 'scaleY(-1)' : '',
      flip === 'both' ? 'scale(-1)' : ''
    ].filter(Boolean).join(' ') || undefined,
    width: typeof size === 'number' ? size : undefined,
    height: typeof size === 'number' ? size : undefined
  };

  return (
    <IconifyIcon
      icon={icon}
      className={`${sizeClass} ${clickableClass} ${className}`}
      style={style}
      onClick={onClick}
      aria-label={ariaLabel}
      {...props}
    />
  );
};

/**
 * Animated icon wrapper
 */
interface AnimatedIconProps extends IconProps {
  animation?: 'spin' | 'pulse' | 'bounce' | 'ping';
}

export const AnimatedIcon: React.FC<AnimatedIconProps> = ({
  animation = 'spin',
  className = '',
  ...props
}) => {
  const animationClasses = {
    spin: 'animate-spin',
    pulse: 'animate-pulse',
    bounce: 'animate-bounce',
    ping: 'animate-ping'
  };

  return (
    <Icon
      className={`${animationClasses[animation]} ${className}`}
      {...props}
    />
  );
};

/**
 * Icon with badge
 */
interface BadgedIconProps extends IconProps {
  badge?: {
    content: string | number;
    color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
    size?: 'sm' | 'md';
  };
}

export const BadgedIcon: React.FC<BadgedIconProps> = ({
  badge,
  className = '',
  ...props
}) => {
  if (!badge) {
    return <Icon className={className} {...props} />;
  }

  const badgeColors = {
    primary: 'bg-blue-500',
    secondary: 'bg-gray-500',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    danger: 'bg-red-500'
  };

  const badgeSizes = {
    sm: 'w-4 h-4 text-xs',
    md: 'w-5 h-5 text-sm'
  };

  return (
    <div className={`relative inline-block ${className}`}>
      <Icon {...props} />
      <span
        className={`absolute -top-1 -right-1 ${badgeColors[badge.color || 'primary']} ${
          badgeSizes[badge.size || 'sm']
        } rounded-full flex items-center justify-center text-white font-medium`}
      >
        {badge.content}
      </span>
    </div>
  );
};

/**
 * Icon button (clickable icon)
 */
interface IconButtonProps extends IconProps {
  variant?: 'ghost' | 'solid' | 'outline';
  disabled?: boolean;
  loading?: boolean;
}

export const IconButton: React.FC<IconButtonProps> = ({
  variant = 'ghost',
  disabled = false,
  loading = false,
  className = '',
  onClick,
  ...props
}) => {
  const variantClasses = {
    ghost: 'hover:bg-gray-700 active:bg-gray-600',
    solid: 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800 text-white',
    outline: 'border border-gray-600 hover:bg-gray-700 active:bg-gray-600'
  };

  const baseClasses = 'p-2 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500';
  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';

  const handleClick = () => {
    if (!disabled && !loading && onClick) {
      onClick();
    }
  };

  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${disabledClasses} ${className}`}
      onClick={handleClick}
      disabled={disabled || loading}
      aria-label={props['aria-label']}
    >
      {loading ? (
        <AnimatedIcon icon="lucide:loader-2" {...props} />
      ) : (
        <Icon {...props} />
      )}
    </button>
  );
};

/**
 * Common icon presets
 */
export const Icons = {
  // Navigation
  menu: 'lucide:menu',
  close: 'lucide:x',
  back: 'lucide:arrow-left',
  forward: 'lucide:arrow-right',
  up: 'lucide:arrow-up',
  down: 'lucide:arrow-down',
  
  // Actions
  add: 'lucide:plus',
  edit: 'lucide:edit',
  delete: 'lucide:trash-2',
  save: 'lucide:save',
  copy: 'lucide:copy',
  share: 'lucide:share',
  download: 'lucide:download',
  upload: 'lucide:upload',
  
  // Status
  success: 'lucide:check',
  error: 'lucide:x',
  warning: 'lucide:alert-triangle',
  info: 'lucide:info',
  loading: 'lucide:loader-2',
  
  // Common
  search: 'lucide:search',
  filter: 'lucide:filter',
  sort: 'lucide:arrow-up-down',
  settings: 'lucide:settings',
  user: 'lucide:user',
  users: 'lucide:users',
  
  // Time tracking
  play: 'lucide:play',
  pause: 'lucide:pause',
  stop: 'lucide:square',
  clock: 'lucide:clock',
  timer: 'lucide:timer',
  
  // Communication
  message: 'lucide:message-circle',
  phone: 'lucide:phone',
  video: 'lucide:video',
  mail: 'lucide:mail',
  
  // Files
  file: 'lucide:file',
  folder: 'lucide:folder',
  image: 'lucide:image',
  document: 'lucide:file-text',
  
  // Project management
  kanban: 'lucide:kanban',
  calendar: 'lucide:calendar',
  chart: 'lucide:bar-chart-2',
  task: 'lucide:check-square'
} as const;
