/**
 * Card Component
 * Pure UI component for content containers
 */

import React, { ReactNode } from 'react';
import { Card as HeroCard, CardBody, CardHeader, CardFooter } from '@heroui/react';

/**
 * Card props
 */
export interface CardProps {
  children: ReactNode;
  header?: ReactNode;
  footer?: ReactNode;
  className?: string;
  headerClassName?: string;
  bodyClassName?: string;
  footerClassName?: string;
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  radius?: 'none' | 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  isPressable?: boolean;
  isHoverable?: boolean;
  isBlurred?: boolean;
  onClick?: () => void;
}

/**
 * Card component with consistent styling
 */
export const Card: React.FC<CardProps> = ({
  children,
  header,
  footer,
  className = '',
  headerClassName = '',
  bodyClassName = '',
  footerClassName = '',
  shadow = 'none',
  radius = 'md',
  fullWidth = false,
  isPressable = false,
  isHoverable = false,
  isBlurred = false,
  onClick
}) => {
  const defaultClasses = `bg-custom-card border-custom-border ${className}`;

  return (
    <HeroCard
      className={defaultClasses}
      shadow={shadow}
      radius={radius}
      fullWidth={fullWidth}
      isPressable={isPressable}
      isHoverable={isHoverable}
      isBlurred={isBlurred}
      onPress={onClick}
    >
      {header && (
        <CardHeader className={`border-b border-custom-border ${headerClassName}`}>
          {header}
        </CardHeader>
      )}
      
      <CardBody className={bodyClassName}>
        {children}
      </CardBody>
      
      {footer && (
        <CardFooter className={`border-t border-custom-border ${footerClassName}`}>
          {footer}
        </CardFooter>
      )}
    </HeroCard>
  );
};

/**
 * Simple card without header/footer structure
 */
export const SimpleCard: React.FC<Omit<CardProps, 'header' | 'footer'>> = ({
  children,
  className = '',
  ...props
}) => {
  return (
    <HeroCard
      className={`bg-custom-card border-custom-border ${className}`}
      {...props}
    >
      <CardBody>
        {children}
      </CardBody>
    </HeroCard>
  );
};

/**
 * Stats card for displaying metrics
 */
interface StatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  className = ''
}) => {
  return (
    <Card className={className}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="text-sm text-gray-400 mb-1">{title}</p>
          <p className="text-2xl font-semibold text-white mb-1">{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500">{subtitle}</p>
          )}
        </div>
        
        {icon && (
          <div className="text-gray-400">
            {icon}
          </div>
        )}
      </div>
      
      {trend && (
        <div className="mt-3 pt-3 border-t border-custom-border">
          <div className="flex items-center">
            <span className={`text-sm ${trend.isPositive ? 'text-green-500' : 'text-red-500'}`}>
              {trend.isPositive ? '↗' : '↘'} {Math.abs(trend.value)}%
            </span>
            <span className="text-sm text-gray-500 ml-2">vs last period</span>
          </div>
        </div>
      )}
    </Card>
  );
};

/**
 * Action card with clickable content
 */
interface ActionCardProps extends Omit<CardProps, 'isPressable' | 'onClick'> {
  title: string;
  description?: string;
  action: () => void;
  actionLabel?: string;
  icon?: ReactNode;
}

export const ActionCard: React.FC<ActionCardProps> = ({
  title,
  description,
  action,
  actionLabel = 'View',
  icon,
  className = '',
  ...props
}) => {
  return (
    <Card
      className={`cursor-pointer hover:bg-gray-800 transition-colors ${className}`}
      isPressable
      onClick={action}
      {...props}
    >
      <div className="flex items-start gap-3">
        {icon && (
          <div className="text-blue-500 mt-1">
            {icon}
          </div>
        )}
        
        <div className="flex-1">
          <h3 className="text-white font-medium mb-1">{title}</h3>
          {description && (
            <p className="text-gray-400 text-sm">{description}</p>
          )}
        </div>
        
        <div className="text-gray-500 text-sm">
          {actionLabel}
        </div>
      </div>
    </Card>
  );
};

/**
 * Loading card placeholder
 */
interface LoadingCardProps {
  className?: string;
  lines?: number;
}

export const LoadingCard: React.FC<LoadingCardProps> = ({
  className = '',
  lines = 3
}) => {
  return (
    <Card className={className}>
      <div className="animate-pulse space-y-3">
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={`h-4 bg-gray-700 rounded ${
              index === lines - 1 ? 'w-3/4' : 'w-full'
            }`}
          />
        ))}
      </div>
    </Card>
  );
};
