/**
 * <PERSON>ton Component
 * Pure UI component following single responsibility principle
 */

import React, { forwardRef, ReactNode } from 'react';
import { <PERSON>ton as HeroButton, ButtonProps as HeroButtonProps } from '@heroui/react';
import { Icon } from '@iconify/react';

/**
 * Extended button props
 */
export interface ButtonProps extends Omit<HeroButtonProps, 'children'> {
  children?: ReactNode;
  icon?: string;
  iconPosition?: 'left' | 'right';
  loading?: boolean;
  loadingText?: string;
  tooltip?: string;
}

/**
 * Button component with consistent styling and behavior
 */
export const Button = forwardRef<HTMLButtonElement, ButtonProps>(({
  children,
  icon,
  iconPosition = 'left',
  loading = false,
  loadingText = 'Loading...',
  disabled,
  className = '',
  ...props
}, ref) => {
  const isDisabled = disabled || loading;

  const renderIcon = (iconName: string) => (
    <Icon icon={iconName} className="w-4 h-4" />
  );

  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex items-center gap-2">
          <Icon icon="lucide:loader-2" className="w-4 h-4 animate-spin" />
          {loadingText}
        </div>
      );
    }

    if (icon && children) {
      return (
        <div className="flex items-center gap-2">
          {iconPosition === 'left' && renderIcon(icon)}
          {children}
          {iconPosition === 'right' && renderIcon(icon)}
        </div>
      );
    }

    if (icon && !children) {
      return renderIcon(icon);
    }

    return children;
  };

  return (
    <HeroButton
      ref={ref}
      disabled={isDisabled}
      className={className}
      {...props}
    >
      {renderContent()}
    </HeroButton>
  );
});

Button.displayName = 'Button';

/**
 * Icon Button (specialized button for icons only)
 */
export interface IconButtonProps extends Omit<ButtonProps, 'children' | 'icon'> {
  icon: string;
  'aria-label': string;
}

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(({
  icon,
  size = 'sm',
  variant = 'light',
  isIconOnly = true,
  ...props
}, ref) => {
  return (
    <Button
      ref={ref}
      icon={icon}
      size={size}
      variant={variant}
      isIconOnly={isIconOnly}
      {...props}
    />
  );
});

IconButton.displayName = 'IconButton';

/**
 * Button Group for related actions
 */
interface ButtonGroupProps {
  children: ReactNode;
  orientation?: 'horizontal' | 'vertical';
  spacing?: 'none' | 'sm' | 'md' | 'lg';
  className?: string;
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  orientation = 'horizontal',
  spacing = 'sm',
  className = ''
}) => {
  const spacingClasses = {
    none: '',
    sm: orientation === 'horizontal' ? 'gap-1' : 'gap-1',
    md: orientation === 'horizontal' ? 'gap-2' : 'gap-2',
    lg: orientation === 'horizontal' ? 'gap-4' : 'gap-4'
  };

  const orientationClasses = {
    horizontal: 'flex-row',
    vertical: 'flex-col'
  };

  return (
    <div className={`flex ${orientationClasses[orientation]} ${spacingClasses[spacing]} ${className}`}>
      {children}
    </div>
  );
};

/**
 * Predefined button variants for common use cases
 */
export const PrimaryButton = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => (
  <Button ref={ref} color="primary" variant="solid" {...props} />
));

export const SecondaryButton = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => (
  <Button ref={ref} color="default" variant="bordered" {...props} />
));

export const DangerButton = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => (
  <Button ref={ref} color="danger" variant="solid" {...props} />
));

export const SuccessButton = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => (
  <Button ref={ref} color="success" variant="solid" {...props} />
));

export const GhostButton = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => (
  <Button ref={ref} variant="light" {...props} />
));

PrimaryButton.displayName = 'PrimaryButton';
SecondaryButton.displayName = 'SecondaryButton';
DangerButton.displayName = 'DangerButton';
SuccessButton.displayName = 'SuccessButton';
GhostButton.displayName = 'GhostButton';
