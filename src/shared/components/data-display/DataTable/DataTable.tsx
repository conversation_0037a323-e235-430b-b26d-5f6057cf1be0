/**
 * Data Table Component
 * Reusable table with sorting, filtering, and pagination
 */

import React, { ReactNode } from 'react';
import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell, Pagination, Spinner } from '@heroui/react';
import { Icon } from '../../ui/Icon';
import { Button } from '../../ui/Button';
import { Inline, Stack } from '../../layout/Grid';

/**
 * Column definition
 */
export interface ColumnDef<T = any> {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  render?: (value: any, row: T, index: number) => ReactNode;
  headerRender?: () => ReactNode;
}

/**
 * Sort configuration
 */
export interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
}

/**
 * Pagination configuration
 */
export interface PaginationConfig {
  page: number;
  pageSize: number;
  total: number;
  showSizeChanger?: boolean;
  pageSizeOptions?: number[];
}

/**
 * Data table props
 */
export interface DataTableProps<T = any> {
  columns: ColumnDef<T>[];
  data: T[];
  loading?: boolean;
  emptyMessage?: string;
  
  // Selection
  selectable?: boolean;
  selectedKeys?: Set<string>;
  onSelectionChange?: (keys: Set<string>) => void;
  
  // Sorting
  sortConfig?: SortConfig;
  onSort?: (config: SortConfig) => void;
  
  // Pagination
  pagination?: PaginationConfig;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  
  // Styling
  className?: string;
  rowClassName?: (row: T, index: number) => string;
  
  // Row actions
  onRowClick?: (row: T, index: number) => void;
  rowKey?: keyof T | ((row: T) => string);
}

/**
 * Data Table component
 */
export const DataTable = <T extends Record<string, any>>({
  columns,
  data,
  loading = false,
  emptyMessage = 'No data available',
  selectable = false,
  selectedKeys,
  onSelectionChange,
  sortConfig,
  onSort,
  pagination,
  onPageChange,
  onPageSizeChange,
  className = '',
  rowClassName,
  onRowClick,
  rowKey = 'id'
}: DataTableProps<T>) => {
  const getRowKey = (row: T): string => {
    if (typeof rowKey === 'function') {
      return rowKey(row);
    }
    return String(row[rowKey]);
  };

  const handleSort = (columnKey: string) => {
    if (!onSort) return;
    
    const newDirection = 
      sortConfig?.key === columnKey && sortConfig.direction === 'asc' 
        ? 'desc' 
        : 'asc';
    
    onSort({ key: columnKey, direction: newDirection });
  };

  const renderSortIcon = (columnKey: string) => {
    if (!sortConfig || sortConfig.key !== columnKey) {
      return <Icon icon="lucide:arrow-up-down" size="sm" className="text-gray-400" />;
    }
    
    return (
      <Icon 
        icon={sortConfig.direction === 'asc' ? 'lucide:arrow-up' : 'lucide:arrow-down'} 
        size="sm" 
        className="text-blue-400" 
      />
    );
  };

  const renderCell = (row: T, column: ColumnDef<T>, index: number) => {
    const value = row[column.key];
    
    if (column.render) {
      return column.render(value, row, index);
    }
    
    return value;
  };

  const topContent = pagination && (
    <div className="flex justify-between items-center">
      <span className="text-custom-muted text-sm">
        Total {pagination.total} items
      </span>
      
      {pagination.showSizeChanger && onPageSizeChange && (
        <div className="flex items-center gap-2">
          <span className="text-custom-muted text-sm">Rows per page:</span>
          <select
            value={pagination.pageSize}
            onChange={(e) => onPageSizeChange(Number(e.target.value))}
            className="bg-custom-sidebar border border-custom-border rounded px-2 py-1 text-custom-text text-sm"
          >
            {(pagination.pageSizeOptions || [10, 20, 50, 100]).map(size => (
              <option key={size} value={size}>{size}</option>
            ))}
          </select>
        </div>
      )}
    </div>
  );

  const bottomContent = pagination && onPageChange && (
    <div className="flex justify-center">
      <Pagination
        page={pagination.page}
        total={Math.ceil(pagination.total / pagination.pageSize)}
        onChange={onPageChange}
        showControls
        classNames={{
          wrapper: 'gap-0 overflow-visible',
          item: 'w-8 h-8 text-small rounded-none bg-transparent',
          cursor: 'bg-primary text-white font-bold'
        }}
      />
    </div>
  );

  return (
    <div className={`space-y-4 ${className}`}>
      <Table
        aria-label="Data table"
        selectionMode={selectable ? 'multiple' : 'none'}
        selectedKeys={selectedKeys}
        onSelectionChange={onSelectionChange}
        topContent={topContent}
        bottomContent={bottomContent}
        classNames={{
          wrapper: 'bg-custom-card border border-custom-border',
          th: 'bg-custom-sidebar text-custom-text border-b border-custom-border',
          td: 'border-b border-custom-border text-custom-text'
        }}
      >
        <TableHeader>
          {columns.map((column) => (
            <TableColumn
              key={column.key}
              width={column.width}
              align={column.align || 'left'}
              allowsSorting={column.sortable}
            >
              <div className="flex items-center gap-2">
                {column.headerRender ? column.headerRender() : column.label}
                {column.sortable && (
                  <Button
                    variant="light"
                    size="sm"
                    isIconOnly
                    onClick={() => handleSort(column.key)}
                    className="min-w-unit-6 w-unit-6 h-unit-6"
                  >
                    {renderSortIcon(column.key)}
                  </Button>
                )}
              </div>
            </TableColumn>
          ))}
        </TableHeader>
        
        <TableBody
          items={data}
          isLoading={loading}
          loadingContent={<Spinner label="Loading..." />}
          emptyContent={emptyMessage}
        >
          {(row) => (
            <TableRow
              key={getRowKey(row)}
              className={`
                ${onRowClick ? 'cursor-pointer hover:bg-custom-hover' : ''}
                ${rowClassName ? rowClassName(row, data.indexOf(row)) : ''}
              `}
              onClick={onRowClick ? () => onRowClick(row, data.indexOf(row)) : undefined}
            >
              {columns.map((column) => (
                <TableCell key={column.key}>
                  {renderCell(row, column, data.indexOf(row))}
                </TableCell>
              ))}
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};

/**
 * Simple data list for basic display
 */
interface DataListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => ReactNode;
  loading?: boolean;
  emptyMessage?: string;
  className?: string;
}

export const DataList = <T,>({
  items,
  renderItem,
  loading = false,
  emptyMessage = 'No items found',
  className = ''
}: DataListProps<T>) => {
  if (loading) {
    return (
      <div className={`flex justify-center py-8 ${className}`}>
        <Spinner label="Loading..." />
      </div>
    );
  }

  if (items.length === 0) {
    return (
      <div className={`text-center py-8 text-custom-muted ${className}`}>
        {emptyMessage}
      </div>
    );
  }

  return (
    <Stack spacing="sm" className={className}>
      {items.map((item, index) => (
        <div key={index}>
          {renderItem(item, index)}
        </div>
      ))}
    </Stack>
  );
};
