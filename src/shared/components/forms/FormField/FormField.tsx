/**
 * Form Field Components
 * Reusable form inputs with consistent validation and styling
 */

import React, { ReactNode } from 'react';
import { Input, Textarea, Select, SelectItem, Checkbox, Switch } from '@heroui/react';
import { Icon } from '../../ui/Icon';

/**
 * Base form field props
 */
export interface BaseFormFieldProps {
  name: string;
  label?: string;
  placeholder?: string;
  value: any;
  onChange: (value: any) => void;
  onBlur?: () => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  description?: string;
}

/**
 * Text input field
 */
export interface TextFieldProps extends BaseFormFieldProps {
  type?: 'text' | 'email' | 'password' | 'url' | 'tel';
  startContent?: ReactNode;
  endContent?: ReactNode;
  maxLength?: number;
}

export const TextField: React.FC<TextFieldProps> = ({
  name,
  label,
  placeholder,
  value,
  onChange,
  onBlur,
  error,
  disabled = false,
  required = false,
  type = 'text',
  startContent,
  endContent,
  maxLength,
  className = '',
  description
}) => {
  return (
    <div className={className}>
      <Input
        name={name}
        label={label}
        placeholder={placeholder}
        type={type}
        value={value || ''}
        onValueChange={onChange}
        onBlur={onBlur}
        isInvalid={!!error}
        errorMessage={error}
        isDisabled={disabled}
        isRequired={required}
        startContent={startContent}
        endContent={endContent}
        maxLength={maxLength}
        description={description}
        classNames={{
          inputWrapper: 'bg-custom-sidebar border-custom-border',
          input: 'text-custom-text',
          label: 'text-custom-muted',
          description: 'text-custom-muted text-sm',
          errorMessage: 'text-red-400 text-sm'
        }}
      />
    </div>
  );
};

/**
 * Textarea field
 */
export interface TextAreaFieldProps extends BaseFormFieldProps {
  rows?: number;
  maxLength?: number;
  resize?: boolean;
}

export const TextAreaField: React.FC<TextAreaFieldProps> = ({
  name,
  label,
  placeholder,
  value,
  onChange,
  onBlur,
  error,
  disabled = false,
  required = false,
  rows = 4,
  maxLength,
  className = '',
  description
}) => {
  return (
    <div className={className}>
      <Textarea
        name={name}
        label={label}
        placeholder={placeholder}
        value={value || ''}
        onValueChange={onChange}
        onBlur={onBlur}
        isInvalid={!!error}
        errorMessage={error}
        isDisabled={disabled}
        isRequired={required}
        minRows={rows}
        maxLength={maxLength}
        description={description}
        classNames={{
          inputWrapper: 'bg-custom-sidebar border-custom-border',
          input: 'text-custom-text',
          label: 'text-custom-muted',
          description: 'text-custom-muted text-sm',
          errorMessage: 'text-red-400 text-sm'
        }}
      />
    </div>
  );
};

/**
 * Select field
 */
export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SelectFieldProps extends BaseFormFieldProps {
  options: SelectOption[];
  multiple?: boolean;
  searchable?: boolean;
}

export const SelectField: React.FC<SelectFieldProps> = ({
  name,
  label,
  placeholder,
  value,
  onChange,
  onBlur,
  error,
  disabled = false,
  required = false,
  options,
  multiple = false,
  className = '',
  description
}) => {
  const selectedKeys = multiple 
    ? new Set(Array.isArray(value) ? value : [])
    : new Set(value ? [value] : []);

  const handleSelectionChange = (keys: any) => {
    if (multiple) {
      onChange(Array.from(keys));
    } else {
      const selectedValue = Array.from(keys)[0];
      onChange(selectedValue || '');
    }
  };

  return (
    <div className={className}>
      <Select
        name={name}
        label={label}
        placeholder={placeholder}
        selectedKeys={selectedKeys}
        onSelectionChange={handleSelectionChange}
        onBlur={onBlur}
        isInvalid={!!error}
        errorMessage={error}
        isDisabled={disabled}
        isRequired={required}
        selectionMode={multiple ? 'multiple' : 'single'}
        description={description}
        classNames={{
          trigger: 'bg-custom-sidebar border-custom-border',
          value: 'text-custom-text',
          label: 'text-custom-muted',
          description: 'text-custom-muted text-sm',
          errorMessage: 'text-red-400 text-sm',
          popoverContent: 'bg-custom-card border-custom-border'
        }}
      >
        {options.map((option) => (
          <SelectItem
            key={option.value}
            value={option.value}
            isDisabled={option.disabled}
            classNames={{
              base: 'text-custom-text hover:bg-custom-hover'
            }}
          >
            {option.label}
          </SelectItem>
        ))}
      </Select>
    </div>
  );
};

/**
 * Checkbox field
 */
export interface CheckboxFieldProps extends Omit<BaseFormFieldProps, 'value' | 'onChange'> {
  checked: boolean;
  onChange: (checked: boolean) => void;
  size?: 'sm' | 'md' | 'lg';
}

export const CheckboxField: React.FC<CheckboxFieldProps> = ({
  name,
  label,
  checked,
  onChange,
  onBlur,
  error,
  disabled = false,
  required = false,
  size = 'md',
  className = '',
  description
}) => {
  return (
    <div className={className}>
      <Checkbox
        name={name}
        isSelected={checked}
        onValueChange={onChange}
        onBlur={onBlur}
        isInvalid={!!error}
        isDisabled={disabled}
        isRequired={required}
        size={size}
        classNames={{
          base: 'text-custom-text',
          label: 'text-custom-text',
          wrapper: 'border-custom-border'
        }}
      >
        {label}
      </Checkbox>
      {description && (
        <p className="text-custom-muted text-sm mt-1">{description}</p>
      )}
      {error && (
        <p className="text-red-400 text-sm mt-1">{error}</p>
      )}
    </div>
  );
};

/**
 * Switch field
 */
export interface SwitchFieldProps extends Omit<BaseFormFieldProps, 'value' | 'onChange'> {
  checked: boolean;
  onChange: (checked: boolean) => void;
  size?: 'sm' | 'md' | 'lg';
}

export const SwitchField: React.FC<SwitchFieldProps> = ({
  name,
  label,
  checked,
  onChange,
  onBlur,
  error,
  disabled = false,
  required = false,
  size = 'md',
  className = '',
  description
}) => {
  return (
    <div className={`flex items-center justify-between ${className}`}>
      <div className="flex-1">
        {label && (
          <label className="text-custom-text font-medium">
            {label}
            {required && <span className="text-red-400 ml-1">*</span>}
          </label>
        )}
        {description && (
          <p className="text-custom-muted text-sm mt-1">{description}</p>
        )}
        {error && (
          <p className="text-red-400 text-sm mt-1">{error}</p>
        )}
      </div>
      
      <Switch
        name={name}
        isSelected={checked}
        onValueChange={onChange}
        onBlur={onBlur}
        isDisabled={disabled}
        size={size}
        classNames={{
          wrapper: 'bg-custom-sidebar border-custom-border'
        }}
      />
    </div>
  );
};

/**
 * Password field with visibility toggle
 */
export const PasswordField: React.FC<Omit<TextFieldProps, 'type'>> = (props) => {
  const [isVisible, setIsVisible] = React.useState(false);

  const toggleVisibility = () => setIsVisible(!isVisible);

  return (
    <TextField
      {...props}
      type={isVisible ? 'text' : 'password'}
      endContent={
        <button
          type="button"
          onClick={toggleVisibility}
          className="text-custom-muted hover:text-custom-text transition-colors"
        >
          <Icon
            icon={isVisible ? 'lucide:eye-off' : 'lucide:eye'}
            size="sm"
          />
        </button>
      }
    />
  );
};
