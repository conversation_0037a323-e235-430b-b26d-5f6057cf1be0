/**
 * TopBar Component (Refactored)
 * Separated UI from business logic and window management
 */

import React from 'react';
import { Avatar, Badge, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from '@heroui/react';
import { Icon } from '../../ui/Icon';
import { Button, IconButton } from '../../ui/Button';
import { Inline } from '../../layout/Grid';
import { User } from '../../../types';
import { useUser } from '../../../../context/UserContext';

/**
 * Window control actions (injected dependency)
 */
export interface WindowControls {
  onMinimize: () => void;
  onMaximize: () => void;
  onClose: () => void;
}

/**
 * TopBar props (pure UI props)
 */
export interface TopBarProps {
  // User data
  user?: User;
  
  // Navigation state
  activeApp: string;
  
  // UI state
  showEmployeeList: boolean;
  isTracking?: boolean;
  
  // Actions (dependency injection)
  onToggleEmployeeList: () => void;
  onUserMenuAction: (action: string) => void;
  windowControls?: WindowControls;
  
  // Styling
  className?: string;
}

/**
 * TopBar component with separated concerns
 */
export const TopBar: React.FC<TopBarProps> = ({
  user,
  activeApp,
  showEmployeeList,
  isTracking = false,
  onToggleEmployeeList,
  onUserMenuAction,
  windowControls,
  className = ''
}) => {
  const getAppTitle = (app: string): string => {
    const titles: Record<string, string> = {
      time: 'Time Tracking',
      chat: 'Chat',
      project: 'Project Management',
      file: 'File Manager',
      meet: 'Meet',
      requests: 'Requests',
      pulse: 'Pulse Board',
      task: 'Task Management'
    };
    return titles[app] || 'TeamBy';
  };

  return (
    <div 
      className={`h-[60px] bg-custom-topbar border-b border-custom-border flex items-center justify-between px-4 ${className}`}
      data-tauri-drag-region
    >
      {/* Left section - App title and tracking status */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-3">
          <h1 className="text-white text-lg font-medium">
            {getAppTitle(activeApp)}
          </h1>
          
          {isTracking && (
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
              <span className="text-red-400 text-sm">Recording</span>
            </div>
          )}
        </div>
      </div>

      {/* Center section - App-specific content can be injected here */}
      <div className="flex-1 flex justify-center">
        <div className="bg-black/20 backdrop-blur-sm rounded-lg px-4 py-1">
          <span className="text-white/80 text-sm">
            {getAppTitle(activeApp)}
          </span>
        </div>
      </div>

      {/* Right section - User controls and window controls */}
      <Inline spacing="sm" align="center">
        {/* Employee list toggle */}
        <IconButton
          icon={showEmployeeList ? 'lucide:users' : 'lucide:users'}
          aria-label="Toggle employee list"
          variant={showEmployeeList ? 'solid' : 'ghost'}
          onClick={onToggleEmployeeList}
        />

        {/* User menu */}
        {user && (
          <Dropdown placement="bottom-end">
            <DropdownTrigger>
              <div className="cursor-pointer">
                <Badge
                  content=""
                  color={user.isOnline ? 'success' : 'default'}
                  shape="circle"
                  placement="bottom-right"
                >
                  <Avatar
                    src={user.avatar}
                    name={user.name}
                    size="sm"
                    className="ring-2 ring-custom-border"
                    showFallback
                    onError={(e) => {
                      console.error('🖼️ [TOPBAR] Avatar failed to load:', user.avatar, e);
                    }}
                  />
                </Badge>
              </div>
            </DropdownTrigger>
            
            <DropdownMenu
              aria-label="User menu"
              onAction={(key) => onUserMenuAction(key as string)}
              classNames={{
                base: "bg-custom-card border border-custom-border",
                list: "bg-custom-card"
              }}
            >
              <DropdownItem key="profile" startContent={<Icon icon="lucide:user" size="sm" />}>
                Profile
              </DropdownItem>
              <DropdownItem key="settings" startContent={<Icon icon="lucide:settings" size="sm" />}>
                Settings
              </DropdownItem>
              <DropdownItem key="logout" startContent={<Icon icon="lucide:log-out" size="sm" />}>
                Logout
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        )}

        {/* Window controls (Tauri specific) */}
        {windowControls && (
          <Inline spacing="none">
            <IconButton
              icon="lucide:minus"
              aria-label="Minimize window"
              size="sm"
              variant="ghost"
              onClick={windowControls.onMinimize}
              className="hover:bg-gray-700"
            />
            <IconButton
              icon="lucide:square"
              aria-label="Maximize window"
              size="sm"
              variant="ghost"
              onClick={windowControls.onMaximize}
              className="hover:bg-gray-700"
            />
            <IconButton
              icon="lucide:x"
              aria-label="Close window"
              size="sm"
              variant="ghost"
              onClick={windowControls.onClose}
              className="hover:bg-red-600 text-red-400 hover:text-white"
            />
          </Inline>
        )}
      </Inline>
    </div>
  );
};

/**
 * TopBar container that handles business logic
 */
interface TopBarContainerProps {
  activeApp: string;
  showEmployeeList: boolean;
  onToggleEmployeeList: () => void;
}

export const TopBarContainer: React.FC<TopBarContainerProps> = ({
  activeApp,
  showEmployeeList,
  onToggleEmployeeList
}) => {
  // Get real user data from UserContext
  const { user: userProfile, isLoading, hasError } = useUser();

  // Convert UserProfile to User type for compatibility
  const user: User = userProfile ? {
    id: userProfile.email,
    name: userProfile.full_name,
    email: userProfile.email,
    avatar: userProfile.avatar || '/avatars/default.jpg',
    role: userProfile.position || 'Employee',
    status: 'active',
    isOnline: userProfile.screen_active && !hasError
  } : {
    id: 'loading',
    name: isLoading ? 'Loading...' : 'Unknown User',
    email: '<EMAIL>',
    avatar: '/avatars/default.jpg',
    role: 'Employee',
    status: 'inactive',
    isOnline: false
  };

  // Debug logging for avatar
  React.useEffect(() => {
    console.log('🖼️ [TOPBAR] Debug info:');
    console.log('🖼️ [TOPBAR] - userProfile:', userProfile);
    console.log('🖼️ [TOPBAR] - userProfile?.avatar:', userProfile?.avatar);
    console.log('🖼️ [TOPBAR] - typeof userProfile?.avatar:', typeof userProfile?.avatar);
    console.log('🖼️ [TOPBAR] - user.avatar:', user.avatar);
    console.log('🖼️ [TOPBAR] - isLoading:', isLoading);
    console.log('🖼️ [TOPBAR] - hasError:', hasError);
  }, [userProfile, user.avatar, isLoading, hasError]);

  const [isTracking, setIsTracking] = React.useState(false);

  // Window controls for Tauri
  const windowControls: WindowControls = {
    onMinimize: async () => {
      try {
        const { getCurrentWindow } = await import('@tauri-apps/api/window');
        await getCurrentWindow().minimize();
      } catch (error) {
        console.error('Failed to minimize window:', error);
      }
    },
    onMaximize: async () => {
      try {
        const { getCurrentWindow } = await import('@tauri-apps/api/window');
        const window = getCurrentWindow();
        const isMaximized = await window.isMaximized();
        if (isMaximized) {
          await window.unmaximize();
        } else {
          await window.maximize();
        }
      } catch (error) {
        console.error('Failed to toggle maximize window:', error);
      }
    },
    onClose: async () => {
      try {
        const { getCurrentWindow } = await import('@tauri-apps/api/window');
        await getCurrentWindow().close();
      } catch (error) {
        console.error('Failed to close window:', error);
      }
    }
  };

  const handleUserMenuAction = (action: string) => {
    switch (action) {
      case 'profile':
        // Handle profile action
        console.log('Open profile');
        break;
      case 'settings':
        // Handle settings action
        console.log('Open settings');
        break;
      case 'logout':
        // Handle logout action
        console.log('Logout');
        break;
      default:
        break;
    }
  };

  // Listen for tracking status changes
  React.useEffect(() => {
    const handleTrackingChange = (event: CustomEvent) => {
      setIsTracking(event.detail.isTracking);
    };

    window.addEventListener('tracking-status-change', handleTrackingChange as EventListener);
    
    return () => {
      window.removeEventListener('tracking-status-change', handleTrackingChange as EventListener);
    };
  }, []);

  return (
    <TopBar
      user={user}
      activeApp={activeApp}
      showEmployeeList={showEmployeeList}
      isTracking={isTracking}
      onToggleEmployeeList={onToggleEmployeeList}
      onUserMenuAction={handleUserMenuAction}
      windowControls={windowControls}
    />
  );
};
