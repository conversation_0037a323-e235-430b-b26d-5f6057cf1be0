/**
 * Base Sidebar Components
 * Reusable sidebar patterns with consistent behavior
 */

import React, { ReactNode } from 'react';
import { Tooltip } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { Icon } from '../../ui/Icon';
import { Stack } from '../../layout/Grid';

/**
 * Sidebar item interface
 */
export interface SidebarItem {
  id: string;
  label: string;
  icon: string;
  badge?: string | number;
  disabled?: boolean;
  tooltip?: string;
}

/**
 * Base sidebar props
 */
export interface BaseSidebarProps {
  items: SidebarItem[];
  activeItem?: string;
  onItemClick: (itemId: string) => void;
  className?: string;
  width?: number;
  collapsed?: boolean;
}

/**
 * Sidebar item component
 */
interface SidebarItemComponentProps {
  item: SidebarItem;
  isActive: boolean;
  collapsed: boolean;
  onClick: () => void;
}

const SidebarItemComponent: React.FC<SidebarItemComponentProps> = ({
  item,
  isActive,
  collapsed,
  onClick
}) => {
  const itemContent = (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className={`
        flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors
        ${isActive 
          ? 'bg-primary text-white' 
          : 'text-custom-text hover:bg-custom-hover hover:text-white'
        }
        ${item.disabled ? 'opacity-50 cursor-not-allowed' : ''}
      `}
      onClick={item.disabled ? undefined : onClick}
    >
      <Icon 
        icon={item.icon} 
        size="lg"
        className={isActive ? 'text-white' : 'text-custom-muted'}
      />
      
      <AnimatePresence>
        {!collapsed && (
          <motion.div
            initial={{ opacity: 0, width: 0 }}
            animate={{ opacity: 1, width: 'auto' }}
            exit={{ opacity: 0, width: 0 }}
            className="flex items-center justify-between flex-1 overflow-hidden"
          >
            <span className="font-medium truncate">{item.label}</span>
            {item.badge && (
              <span className="bg-primary/20 text-primary text-xs px-2 py-1 rounded-full ml-2">
                {item.badge}
              </span>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );

  if (collapsed && item.tooltip) {
    return (
      <Tooltip content={item.tooltip} placement="right">
        {itemContent}
      </Tooltip>
    );
  }

  return itemContent;
};

/**
 * Base sidebar component
 */
export const BaseSidebar: React.FC<BaseSidebarProps> = ({
  items,
  activeItem,
  onItemClick,
  className = '',
  width = 240,
  collapsed = false
}) => {
  const sidebarWidth = collapsed ? 64 : width;

  return (
    <motion.div
      animate={{ width: sidebarWidth }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className={`bg-custom-sidebar border-r border-custom-border h-full ${className}`}
    >
      <div className="p-4">
        <Stack spacing="sm">
          {items.map((item) => (
            <SidebarItemComponent
              key={item.id}
              item={item}
              isActive={activeItem === item.id}
              collapsed={collapsed}
              onClick={() => onItemClick(item.id)}
            />
          ))}
        </Stack>
      </div>
    </motion.div>
  );
};

/**
 * Mini sidebar (always collapsed)
 */
export interface MiniSidebarProps {
  items: SidebarItem[];
  activeItem?: string;
  onItemClick: (itemId: string) => void;
  className?: string;
}

export const MiniSidebar: React.FC<MiniSidebarProps> = ({
  items,
  activeItem,
  onItemClick,
  className = ''
}) => {
  return (
    <BaseSidebar
      items={items}
      activeItem={activeItem}
      onItemClick={onItemClick}
      collapsed={true}
      width={64}
      className={className}
    />
  );
};

/**
 * Expandable sidebar
 */
export interface ExpandableSidebarProps extends BaseSidebarProps {
  expanded: boolean;
  onToggle: () => void;
  header?: ReactNode;
  footer?: ReactNode;
}

export const ExpandableSidebar: React.FC<ExpandableSidebarProps> = ({
  items,
  activeItem,
  onItemClick,
  expanded,
  onToggle,
  header,
  footer,
  className = '',
  width = 240
}) => {
  return (
    <motion.div
      animate={{ width: expanded ? width : 64 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className={`bg-custom-sidebar border-r border-custom-border h-full flex flex-col ${className}`}
    >
      {/* Header */}
      {header && (
        <div className="p-4 border-b border-custom-border">
          <AnimatePresence>
            {expanded ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                {header}
              </motion.div>
            ) : (
              <div className="flex justify-center">
                <Icon icon="lucide:menu" size="lg" className="text-custom-muted" />
              </div>
            )}
          </AnimatePresence>
        </div>
      )}

      {/* Navigation items */}
      <div className="flex-1 p-4">
        <Stack spacing="sm">
          {items.map((item) => (
            <SidebarItemComponent
              key={item.id}
              item={item}
              isActive={activeItem === item.id}
              collapsed={!expanded}
              onClick={() => onItemClick(item.id)}
            />
          ))}
        </Stack>
      </div>

      {/* Footer */}
      {footer && (
        <div className="p-4 border-t border-custom-border">
          <AnimatePresence>
            {expanded ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                {footer}
              </motion.div>
            ) : (
              <div className="flex justify-center">
                <Icon icon="lucide:settings" size="lg" className="text-custom-muted" />
              </div>
            )}
          </AnimatePresence>
        </div>
      )}

      {/* Toggle button */}
      <div className="absolute -right-3 top-6">
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={onToggle}
          className="w-6 h-6 bg-custom-card border border-custom-border rounded-full flex items-center justify-center text-custom-muted hover:text-white transition-colors"
        >
          <Icon 
            icon={expanded ? 'lucide:chevron-left' : 'lucide:chevron-right'} 
            size="sm" 
          />
        </motion.button>
      </div>
    </motion.div>
  );
};
