/**
 * Error Provider for global error state management
 * Provides centralized error handling across the application
 */

import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { AppError } from '../../types';
import { getCurrentTimestamp } from '../../utils/date';

/**
 * Error state interface
 */
interface ErrorState {
  errors: AppError[];
  globalError: AppError | null;
  isErrorModalOpen: boolean;
}

/**
 * Error actions
 */
type ErrorAction =
  | { type: 'ADD_ERROR'; payload: AppError }
  | { type: 'REMOVE_ERROR'; payload: string }
  | { type: 'CLEAR_ERRORS' }
  | { type: 'SET_GLOBAL_ERROR'; payload: AppError | null }
  | { type: 'OPEN_ERROR_MODAL' }
  | { type: 'CLOSE_ERROR_MODAL' };

/**
 * Error context interface
 */
interface ErrorContextType {
  state: ErrorState;
  addError: (error: Partial<AppError>) => void;
  removeError: (code: string) => void;
  clearErrors: () => void;
  setGlobalError: (error: AppError | null) => void;
  openErrorModal: () => void;
  closeErrorModal: () => void;
  reportError: (error: Error, context?: string) => void;
}

/**
 * Initial error state
 */
const initialState: ErrorState = {
  errors: [],
  globalError: null,
  isErrorModalOpen: false
};

/**
 * Error reducer
 */
function errorReducer(state: ErrorState, action: ErrorAction): ErrorState {
  switch (action.type) {
    case 'ADD_ERROR':
      return {
        ...state,
        errors: [...state.errors, action.payload]
      };
    
    case 'REMOVE_ERROR':
      return {
        ...state,
        errors: state.errors.filter(error => error.code !== action.payload)
      };
    
    case 'CLEAR_ERRORS':
      return {
        ...state,
        errors: []
      };
    
    case 'SET_GLOBAL_ERROR':
      return {
        ...state,
        globalError: action.payload
      };
    
    case 'OPEN_ERROR_MODAL':
      return {
        ...state,
        isErrorModalOpen: true
      };
    
    case 'CLOSE_ERROR_MODAL':
      return {
        ...state,
        isErrorModalOpen: false
      };
    
    default:
      return state;
  }
}

/**
 * Error context
 */
const ErrorContext = createContext<ErrorContextType | undefined>(undefined);

/**
 * Error provider props
 */
interface ErrorProviderProps {
  children: ReactNode;
  onError?: (error: AppError) => void;
}

/**
 * Error Provider component
 */
export const ErrorProvider: React.FC<ErrorProviderProps> = ({
  children,
  onError
}) => {
  const [state, dispatch] = useReducer(errorReducer, initialState);

  /**
   * Add error to state
   */
  const addError = (error: Partial<AppError>) => {
    const appError: AppError = {
      code: error.code || 'UNKNOWN_ERROR',
      message: error.message || 'An unknown error occurred',
      details: error.details || {},
      timestamp: error.timestamp || getCurrentTimestamp()
    };

    dispatch({ type: 'ADD_ERROR', payload: appError });

    // Call external error handler
    if (onError) {
      onError(appError);
    }

    // Auto-remove error after 5 seconds (except for critical errors)
    if (!appError.code.includes('CRITICAL')) {
      setTimeout(() => {
        removeError(appError.code);
      }, 5000);
    }
  };

  /**
   * Remove error from state
   */
  const removeError = (code: string) => {
    dispatch({ type: 'REMOVE_ERROR', payload: code });
  };

  /**
   * Clear all errors
   */
  const clearErrors = () => {
    dispatch({ type: 'CLEAR_ERRORS' });
  };

  /**
   * Set global error
   */
  const setGlobalError = (error: AppError | null) => {
    dispatch({ type: 'SET_GLOBAL_ERROR', payload: error });
  };

  /**
   * Open error modal
   */
  const openErrorModal = () => {
    dispatch({ type: 'OPEN_ERROR_MODAL' });
  };

  /**
   * Close error modal
   */
  const closeErrorModal = () => {
    dispatch({ type: 'CLOSE_ERROR_MODAL' });
  };

  /**
   * Report error (convert JS Error to AppError)
   */
  const reportError = (error: Error, context?: string) => {
    const appError: AppError = {
      code: error.name || 'JAVASCRIPT_ERROR',
      message: error.message,
      details: {
        stack: error.stack,
        context: context || 'Unknown context'
      },
      timestamp: getCurrentTimestamp()
    };

    addError(appError);
  };

  const contextValue: ErrorContextType = {
    state,
    addError,
    removeError,
    clearErrors,
    setGlobalError,
    openErrorModal,
    closeErrorModal,
    reportError
  };

  return (
    <ErrorContext.Provider value={contextValue}>
      {children}
    </ErrorContext.Provider>
  );
};

/**
 * Hook to use error context
 */
export const useError = (): ErrorContextType => {
  const context = useContext(ErrorContext);
  if (context === undefined) {
    throw new Error('useError must be used within an ErrorProvider');
  }
  return context;
};

/**
 * Hook for error notifications
 */
export const useErrorNotification = () => {
  const { addError } = useError();

  const notifyError = (message: string, code?: string) => {
    addError({
      code: code || 'USER_NOTIFICATION',
      message,
      timestamp: getCurrentTimestamp()
    });
  };

  const notifySuccess = (message: string) => {
    addError({
      code: 'SUCCESS_NOTIFICATION',
      message,
      timestamp: getCurrentTimestamp()
    });
  };

  const notifyWarning = (message: string) => {
    addError({
      code: 'WARNING_NOTIFICATION',
      message,
      timestamp: getCurrentTimestamp()
    });
  };

  const notifyInfo = (message: string) => {
    addError({
      code: 'INFO_NOTIFICATION',
      message,
      timestamp: getCurrentTimestamp()
    });
  };

  return {
    notifyError,
    notifySuccess,
    notifyWarning,
    notifyInfo
  };
};

/**
 * Hook for handling async operations with error reporting
 */
export const useAsyncOperation = () => {
  const { reportError } = useError();

  const executeAsync = async <T,>(
    operation: () => Promise<T>,
    context?: string
  ): Promise<T | null> => {
    try {
      return await operation();
    } catch (error) {
      if (error instanceof Error) {
        reportError(error, context);
      } else {
        reportError(new Error(String(error)), context);
      }
      return null;
    }
  };

  return { executeAsync };
};
