/**
 * Async Error Boundary for handling async errors and promise rejections
 * Extends the basic ErrorBoundary to handle async operations
 */

import React, { ReactNode, useEffect, useState } from 'react';
import { ErrorBoundary } from './ErrorBoundary';

/**
 * Async error boundary props
 */
interface AsyncErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error) => void;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
}

/**
 * Hook for handling async errors
 */
export function useAsyncError() {
  const [, setError] = useState();
  
  return (error: Error) => {
    setError(() => {
      throw error;
    });
  };
}

/**
 * Async Error Boundary component
 */
export const AsyncErrorBoundary: React.FC<AsyncErrorBoundaryProps> = ({
  children,
  fallback,
  onError,
  resetOnPropsChange,
  resetKeys
}) => {
  const [asyncError, setAsyncError] = useState<Error | null>(null);

  // Handle unhandled promise rejections
  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = event.reason instanceof Error 
        ? event.reason 
        : new Error(String(event.reason));
      
      setAsyncError(error);
      
      if (onError) {
        onError(error);
      }
      
      // Prevent the default browser behavior
      event.preventDefault();
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [onError]);

  // Reset async error when reset keys change
  useEffect(() => {
    if (asyncError && resetOnPropsChange && resetKeys) {
      setAsyncError(null);
    }
  }, [asyncError, resetOnPropsChange, resetKeys]);

  // Throw async error to be caught by ErrorBoundary
  if (asyncError) {
    throw asyncError;
  }

  return (
    <ErrorBoundary
      fallback={fallback}
      onError={(error, errorInfo) => {
        if (onError) {
          onError(error);
        }
      }}
      resetOnPropsChange={resetOnPropsChange}
      resetKeys={resetKeys}
    >
      {children}
    </ErrorBoundary>
  );
};

/**
 * Hook for safe async operations with error handling
 */
export function useSafeAsync<T>() {
  const throwAsyncError = useAsyncError();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const execute = async (asyncFunction: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await asyncFunction();
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
      throwAsyncError(error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const reset = () => {
    setError(null);
    setLoading(false);
  };

  return {
    execute,
    loading,
    error,
    reset
  };
}

/**
 * Component for handling async operations with error boundaries
 */
interface AsyncComponentProps<T> {
  asyncFunction: () => Promise<T>;
  children: (data: T) => ReactNode;
  fallback?: ReactNode;
  errorFallback?: ReactNode;
  onError?: (error: Error) => void;
}

export function AsyncComponent<T>({
  asyncFunction,
  children,
  fallback,
  errorFallback,
  onError
}: AsyncComponentProps<T>) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const throwAsyncError = useAsyncError();

  useEffect(() => {
    let cancelled = false;

    const executeAsync = async () => {
      try {
        setLoading(true);
        const result = await asyncFunction();
        
        if (!cancelled) {
          setData(result);
        }
      } catch (err) {
        if (!cancelled) {
          const error = err instanceof Error ? err : new Error(String(err));
          if (onError) {
            onError(error);
          }
          throwAsyncError(error);
        }
      } finally {
        if (!cancelled) {
          setLoading(false);
        }
      }
    };

    executeAsync();

    return () => {
      cancelled = true;
    };
  }, [asyncFunction, onError, throwAsyncError]);

  if (loading) {
    return fallback || <div>Loading...</div>;
  }

  if (data === null) {
    return errorFallback || <div>Error loading data</div>;
  }

  return <>{children(data)}</>;
}

/**
 * Higher-order component for async error boundary
 */
export function withAsyncErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<AsyncErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <AsyncErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </AsyncErrorBoundary>
  );

  WrappedComponent.displayName = `withAsyncErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}
