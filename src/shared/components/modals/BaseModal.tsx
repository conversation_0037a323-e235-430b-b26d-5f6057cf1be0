/**
 * Base Modal component
 * Provides consistent modal structure and behavior across the application
 */

import React, { ReactNode } from 'react';
import { Modal, ModalContent, ModalHeader, ModalBody, ModalFooter } from '@heroui/react';

/**
 * Base modal props
 */
export interface BaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: ReactNode;
  footer?: ReactNode;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | 'full';
  placement?: 'auto' | 'top' | 'center' | 'bottom';
  backdrop?: 'transparent' | 'opaque' | 'blur';
  scrollBehavior?: 'inside' | 'outside';
  closeButton?: boolean;
  isDismissable?: boolean;
  hideCloseButton?: boolean;
  className?: string;
  headerClassName?: string;
  bodyClassName?: string;
  footerClassName?: string;
  onOpenChange?: (open: boolean) => void;
  motionProps?: any;
}

/**
 * Base Modal component
 */
export const BaseModal: React.FC<BaseModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  footer,
  size = 'md',
  placement = 'center',
  backdrop = 'opaque',
  scrollBehavior = 'inside',
  closeButton = true,
  isDismissable = true,
  hideCloseButton = false,
  className = '',
  headerClassName = '',
  bodyClassName = '',
  footerClassName = '',
  onOpenChange,
  motionProps,
  ...props
}) => {
  const handleOpenChange = (open: boolean) => {
    if (onOpenChange) {
      onOpenChange(open);
    }
    if (!open) {
      onClose();
    }
  };

  const defaultClassNames = {
    base: `bg-custom-card text-white ${className}`,
    header: `border-b border-custom-border ${headerClassName}`,
    body: `${bodyClassName}`,
    footer: `border-t border-custom-border ${footerClassName}`
  };

  return (
    <Modal
      isOpen={isOpen}
      onOpenChange={handleOpenChange}
      size={size}
      placement={placement}
      backdrop={backdrop}
      scrollBehavior={scrollBehavior}
      closeButton={closeButton}
      isDismissable={isDismissable}
      hideCloseButton={hideCloseButton}
      classNames={defaultClassNames}
      motionProps={motionProps}
      {...props}
    >
      <ModalContent>
        {title && (
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-lg font-semibold">{title}</h2>
          </ModalHeader>
        )}
        
        <ModalBody className="py-4">
          {children}
        </ModalBody>
        
        {footer && (
          <ModalFooter>
            {footer}
          </ModalFooter>
        )}
      </ModalContent>
    </Modal>
  );
};

/**
 * Modal with custom header
 */
interface CustomHeaderModalProps extends Omit<BaseModalProps, 'title'> {
  header: ReactNode;
}

export const CustomHeaderModal: React.FC<CustomHeaderModalProps> = ({
  header,
  children,
  footer,
  ...props
}) => {
  return (
    <BaseModal {...props}>
      <div className="space-y-4">
        <div className="border-b border-custom-border pb-4">
          {header}
        </div>
        <div>
          {children}
        </div>
      </div>
    </BaseModal>
  );
};

/**
 * Confirmation modal
 */
interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  confirmVariant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  isLoading?: boolean;
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmVariant = 'primary',
  isLoading = false
}) => {
  const handleConfirm = () => {
    onConfirm();
    if (!isLoading) {
      onClose();
    }
  };

  const footer = (
    <div className="flex gap-2 justify-end">
      <button
        onClick={onClose}
        disabled={isLoading}
        className="px-4 py-2 text-sm font-medium text-gray-300 hover:text-white transition-colors disabled:opacity-50"
      >
        {cancelText}
      </button>
      <button
        onClick={handleConfirm}
        disabled={isLoading}
        className={`px-4 py-2 text-sm font-medium rounded-md transition-colors disabled:opacity-50 ${
          confirmVariant === 'danger'
            ? 'bg-red-600 hover:bg-red-700 text-white'
            : confirmVariant === 'warning'
            ? 'bg-yellow-600 hover:bg-yellow-700 text-white'
            : confirmVariant === 'success'
            ? 'bg-green-600 hover:bg-green-700 text-white'
            : 'bg-blue-600 hover:bg-blue-700 text-white'
        }`}
      >
        {isLoading ? 'Loading...' : confirmText}
      </button>
    </div>
  );

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      footer={footer}
      size="sm"
      isDismissable={!isLoading}
    >
      <p className="text-gray-300">{message}</p>
    </BaseModal>
  );
};

/**
 * Loading modal
 */
interface LoadingModalProps {
  isOpen: boolean;
  message?: string;
  progress?: number;
}

export const LoadingModal: React.FC<LoadingModalProps> = ({
  isOpen,
  message = 'Loading...',
  progress
}) => {
  return (
    <BaseModal
      isOpen={isOpen}
      onClose={() => {}} // Cannot be closed
      size="sm"
      isDismissable={false}
      hideCloseButton={true}
    >
      <div className="flex flex-col items-center space-y-4 py-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p className="text-gray-300">{message}</p>
        {progress !== undefined && (
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        )}
      </div>
    </BaseModal>
  );
};

/**
 * Error modal
 */
interface ErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  error: string | Error;
  onRetry?: () => void;
}

export const ErrorModal: React.FC<ErrorModalProps> = ({
  isOpen,
  onClose,
  title = 'Error',
  error,
  onRetry
}) => {
  const errorMessage = error instanceof Error ? error.message : error;

  const footer = (
    <div className="flex gap-2 justify-end">
      {onRetry && (
        <button
          onClick={onRetry}
          className="px-4 py-2 text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
        >
          Retry
        </button>
      )}
      <button
        onClick={onClose}
        className="px-4 py-2 text-sm font-medium bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors"
      >
        Close
      </button>
    </div>
  );

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      footer={footer}
      size="sm"
    >
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <svg
            className="h-6 w-6 text-red-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
        <div>
          <p className="text-gray-300">{errorMessage}</p>
        </div>
      </div>
    </BaseModal>
  );
};
