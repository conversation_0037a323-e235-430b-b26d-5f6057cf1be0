/**
 * Tabbed Modal component
 * Provides modal with tab navigation functionality
 */

import React, { ReactNode, useState } from 'react';
import { Tabs, Tab } from '@heroui/react';
import { BaseModal, BaseModalProps } from './BaseModal';

/**
 * Tab configuration
 */
export interface TabConfig {
  key: string;
  title: string;
  content: ReactNode;
  icon?: ReactNode;
  disabled?: boolean;
  badge?: string | number;
}

/**
 * Tabbed modal props
 */
interface TabbedModalProps extends Omit<BaseModalProps, 'children'> {
  tabs: TabConfig[];
  defaultTab?: string;
  onTabChange?: (key: string) => void;
  tabsClassName?: string;
  tabClassName?: string;
  tabPanelClassName?: string;
}

/**
 * Tabbed Modal component
 */
export const TabbedModal: React.FC<TabbedModalProps> = ({
  tabs,
  defaultTab,
  onTabChange,
  tabsClassName = '',
  tabClassName = '',
  tabPanelClassName = '',
  ...modalProps
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.key || '');

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    if (onTabChange) {
      onTabChange(key);
    }
  };

  const activeTabContent = tabs.find(tab => tab.key === activeTab)?.content;

  return (
    <BaseModal {...modalProps}>
      <div className="space-y-4">
        <Tabs
          selectedKey={activeTab}
          onSelectionChange={(key) => handleTabChange(key as string)}
          className={`w-full ${tabsClassName}`}
          classNames={{
            tabList: "bg-gray-800 rounded-lg p-1",
            tab: `text-gray-400 hover:text-white data-[selected=true]:text-white data-[selected=true]:bg-gray-700 ${tabClassName}`,
            panel: `pt-4 ${tabPanelClassName}`
          }}
        >
          {tabs.map((tab) => (
            <Tab
              key={tab.key}
              title={
                <div className="flex items-center space-x-2">
                  {tab.icon && <span>{tab.icon}</span>}
                  <span>{tab.title}</span>
                  {tab.badge && (
                    <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                      {tab.badge}
                    </span>
                  )}
                </div>
              }
              isDisabled={tab.disabled}
            >
              <div className="min-h-[200px]">
                {tab.content}
              </div>
            </Tab>
          ))}
        </Tabs>
      </div>
    </BaseModal>
  );
};

/**
 * Form Modal with tabs
 */
interface FormTabbedModalProps extends Omit<TabbedModalProps, 'footer'> {
  onSubmit: (data: any) => void;
  onCancel: () => void;
  submitText?: string;
  cancelText?: string;
  isSubmitting?: boolean;
  isValid?: boolean;
}

export const FormTabbedModal: React.FC<FormTabbedModalProps> = ({
  onSubmit,
  onCancel,
  submitText = 'Save',
  cancelText = 'Cancel',
  isSubmitting = false,
  isValid = true,
  ...props
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isValid && !isSubmitting) {
      onSubmit({});
    }
  };

  const footer = (
    <form onSubmit={handleSubmit} className="flex gap-2 justify-end w-full">
      <button
        type="button"
        onClick={onCancel}
        disabled={isSubmitting}
        className="px-4 py-2 text-sm font-medium text-gray-300 hover:text-white transition-colors disabled:opacity-50"
      >
        {cancelText}
      </button>
      <button
        type="submit"
        disabled={!isValid || isSubmitting}
        className="px-4 py-2 text-sm font-medium bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-md transition-colors"
      >
        {isSubmitting ? 'Saving...' : submitText}
      </button>
    </form>
  );

  return (
    <TabbedModal
      {...props}
      footer={footer}
    />
  );
};

/**
 * Wizard Modal (stepped tabs)
 */
interface WizardStep extends Omit<TabConfig, 'key'> {
  key: string;
  isValid?: boolean;
  isOptional?: boolean;
}

interface WizardModalProps extends Omit<BaseModalProps, 'children' | 'footer'> {
  steps: WizardStep[];
  currentStep: number;
  onNext: () => void;
  onPrevious: () => void;
  onFinish: () => void;
  onCancel: () => void;
  nextText?: string;
  previousText?: string;
  finishText?: string;
  cancelText?: string;
  isSubmitting?: boolean;
}

export const WizardModal: React.FC<WizardModalProps> = ({
  steps,
  currentStep,
  onNext,
  onPrevious,
  onFinish,
  onCancel,
  nextText = 'Next',
  previousText = 'Previous',
  finishText = 'Finish',
  cancelText = 'Cancel',
  isSubmitting = false,
  ...modalProps
}) => {
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === steps.length - 1;
  const currentStepData = steps[currentStep];
  const canProceed = currentStepData?.isValid !== false;

  const footer = (
    <div className="flex justify-between w-full">
      <button
        onClick={onCancel}
        disabled={isSubmitting}
        className="px-4 py-2 text-sm font-medium text-gray-300 hover:text-white transition-colors disabled:opacity-50"
      >
        {cancelText}
      </button>
      
      <div className="flex gap-2">
        {!isFirstStep && (
          <button
            onClick={onPrevious}
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors disabled:opacity-50"
          >
            {previousText}
          </button>
        )}
        
        {isLastStep ? (
          <button
            onClick={onFinish}
            disabled={!canProceed || isSubmitting}
            className="px-4 py-2 text-sm font-medium bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-md transition-colors"
          >
            {isSubmitting ? 'Processing...' : finishText}
          </button>
        ) : (
          <button
            onClick={onNext}
            disabled={!canProceed}
            className="px-4 py-2 text-sm font-medium bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-md transition-colors"
          >
            {nextText}
          </button>
        )}
      </div>
    </div>
  );

  return (
    <BaseModal
      {...modalProps}
      footer={footer}
      isDismissable={!isSubmitting}
    >
      <div className="space-y-6">
        {/* Step indicator */}
        <div className="flex items-center justify-center space-x-2">
          {steps.map((step, index) => (
            <div key={step.key} className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  index === currentStep
                    ? 'bg-blue-600 text-white'
                    : index < currentStep
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-600 text-gray-300'
                }`}
              >
                {index < currentStep ? (
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  index + 1
                )}
              </div>
              {index < steps.length - 1 && (
                <div className={`w-8 h-0.5 ${index < currentStep ? 'bg-green-600' : 'bg-gray-600'}`} />
              )}
            </div>
          ))}
        </div>

        {/* Step title */}
        <div className="text-center">
          <h3 className="text-lg font-medium text-white">{currentStepData?.title}</h3>
        </div>

        {/* Step content */}
        <div className="min-h-[300px]">
          {currentStepData?.content}
        </div>
      </div>
    </BaseModal>
  );
};
