/**
 * Shared components index
 * Organized by responsibility and following clean architecture principles
 */

// UI Foundation Components (Pure UI, no business logic)
export * from './ui';

// Layout Components (Structure and positioning)
export * from './layout';

// Form Components (Input handling and validation)
export * from './forms';

// Modal Components (Dialog and overlay patterns)
export * from './modals';

// Navigation Components (Routing and navigation)
export * from './navigation';

// Data Display Components (Tables, lists, charts)
export * from './data-display';

// Feedback Components (Loading, errors, notifications)
export * from './feedback';

// Error Boundary Components (Error handling)
export * from './error-boundary';
