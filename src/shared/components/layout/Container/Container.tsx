/**
 * Container Component
 * Provides consistent content width and spacing
 */

import React, { ReactNode } from 'react';

/**
 * Container size variants
 */
export type ContainerSize = 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';

/**
 * Container props
 */
export interface ContainerProps {
  children: ReactNode;
  size?: ContainerSize;
  className?: string;
  padding?: boolean;
  centerContent?: boolean;
}

/**
 * Size mapping
 */
const sizeMap: Record<ContainerSize, string> = {
  sm: 'max-w-2xl',
  md: 'max-w-4xl',
  lg: 'max-w-6xl',
  xl: 'max-w-7xl',
  '2xl': 'max-w-8xl',
  full: 'max-w-full'
};

/**
 * Container component for consistent content width
 */
export const Container: React.FC<ContainerProps> = ({
  children,
  size = 'xl',
  className = '',
  padding = true,
  centerContent = false
}) => {
  const sizeClass = sizeMap[size];
  const paddingClass = padding ? 'px-4 sm:px-6 lg:px-8' : '';
  const centerClass = centerContent ? 'flex items-center justify-center min-h-full' : '';

  return (
    <div className={`mx-auto ${sizeClass} ${paddingClass} ${centerClass} ${className}`}>
      {children}
    </div>
  );
};

/**
 * Section container for page sections
 */
interface SectionProps extends ContainerProps {
  as?: keyof JSX.IntrinsicElements;
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
}

export const Section: React.FC<SectionProps> = ({
  as: Component = 'section',
  spacing = 'md',
  className = '',
  ...props
}) => {
  const spacingClasses = {
    none: '',
    sm: 'py-4',
    md: 'py-8',
    lg: 'py-12',
    xl: 'py-16'
  };

  return (
    <Component className={`${spacingClasses[spacing]} ${className}`}>
      <Container {...props} />
    </Component>
  );
};

/**
 * Page container with consistent layout
 */
interface PageContainerProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  actions?: ReactNode;
  breadcrumbs?: ReactNode;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
}

export const PageContainer: React.FC<PageContainerProps> = ({
  children,
  title,
  subtitle,
  actions,
  breadcrumbs,
  className = '',
  headerClassName = '',
  contentClassName = ''
}) => {
  const hasHeader = title || subtitle || actions || breadcrumbs;

  return (
    <Container className={className}>
      {hasHeader && (
        <div className={`mb-8 ${headerClassName}`}>
          {breadcrumbs && (
            <div className="mb-4">
              {breadcrumbs}
            </div>
          )}
          
          <div className="flex items-start justify-between">
            <div className="flex-1">
              {title && (
                <h1 className="text-2xl font-semibold text-white mb-2">
                  {title}
                </h1>
              )}
              {subtitle && (
                <p className="text-gray-400">
                  {subtitle}
                </p>
              )}
            </div>
            
            {actions && (
              <div className="ml-4">
                {actions}
              </div>
            )}
          </div>
        </div>
      )}
      
      <div className={contentClassName}>
        {children}
      </div>
    </Container>
  );
};

/**
 * Centered container for forms and focused content
 */
interface CenteredContainerProps {
  children: ReactNode;
  maxWidth?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const CenteredContainer: React.FC<CenteredContainerProps> = ({
  children,
  maxWidth = 'md',
  className = ''
}) => {
  const widthClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl'
  };

  return (
    <div className={`min-h-screen flex items-center justify-center px-4 ${className}`}>
      <div className={`w-full ${widthClasses[maxWidth]}`}>
        {children}
      </div>
    </div>
  );
};
