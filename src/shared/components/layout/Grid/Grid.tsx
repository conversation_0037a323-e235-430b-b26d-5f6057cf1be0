/**
 * Grid Layout Components
 * Flexible grid system for responsive layouts
 */

import React, { ReactNode } from 'react';

/**
 * Grid container props
 */
export interface GridProps {
  children: ReactNode;
  cols?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  responsive?: {
    sm?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
    md?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
    lg?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
    xl?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
  };
  className?: string;
}

/**
 * Grid column mapping
 */
const colsMap = {
  1: 'grid-cols-1',
  2: 'grid-cols-2',
  3: 'grid-cols-3',
  4: 'grid-cols-4',
  5: 'grid-cols-5',
  6: 'grid-cols-6',
  12: 'grid-cols-12'
};

/**
 * Gap mapping
 */
const gapMap = {
  none: 'gap-0',
  sm: 'gap-2',
  md: 'gap-4',
  lg: 'gap-6',
  xl: 'gap-8'
};

/**
 * Responsive grid container
 */
export const Grid: React.FC<GridProps> = ({
  children,
  cols = 1,
  gap = 'md',
  responsive,
  className = ''
}) => {
  const baseClasses = 'grid';
  const colsClass = colsMap[cols];
  const gapClass = gapMap[gap];
  
  const responsiveClasses = responsive ? [
    responsive.sm ? `sm:${colsMap[responsive.sm]}` : '',
    responsive.md ? `md:${colsMap[responsive.md]}` : '',
    responsive.lg ? `lg:${colsMap[responsive.lg]}` : '',
    responsive.xl ? `xl:${colsMap[responsive.xl]}` : ''
  ].filter(Boolean).join(' ') : '';

  return (
    <div className={`${baseClasses} ${colsClass} ${gapClass} ${responsiveClasses} ${className}`}>
      {children}
    </div>
  );
};

/**
 * Grid item props
 */
export interface GridItemProps {
  children: ReactNode;
  span?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
  start?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  end?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13;
  responsive?: {
    sm?: { span?: 1 | 2 | 3 | 4 | 5 | 6 | 12; start?: number; end?: number };
    md?: { span?: 1 | 2 | 3 | 4 | 5 | 6 | 12; start?: number; end?: number };
    lg?: { span?: 1 | 2 | 3 | 4 | 5 | 6 | 12; start?: number; end?: number };
    xl?: { span?: 1 | 2 | 3 | 4 | 5 | 6 | 12; start?: number; end?: number };
  };
  className?: string;
}

/**
 * Span mapping
 */
const spanMap = {
  1: 'col-span-1',
  2: 'col-span-2',
  3: 'col-span-3',
  4: 'col-span-4',
  5: 'col-span-5',
  6: 'col-span-6',
  12: 'col-span-12'
};

/**
 * Grid item component
 */
export const GridItem: React.FC<GridItemProps> = ({
  children,
  span,
  start,
  end,
  responsive,
  className = ''
}) => {
  const spanClass = span ? spanMap[span] : '';
  const startClass = start ? `col-start-${start}` : '';
  const endClass = end ? `col-end-${end}` : '';
  
  const responsiveClasses = responsive ? [
    responsive.sm?.span ? `sm:${spanMap[responsive.sm.span]}` : '',
    responsive.sm?.start ? `sm:col-start-${responsive.sm.start}` : '',
    responsive.sm?.end ? `sm:col-end-${responsive.sm.end}` : '',
    responsive.md?.span ? `md:${spanMap[responsive.md.span]}` : '',
    responsive.md?.start ? `md:col-start-${responsive.md.start}` : '',
    responsive.md?.end ? `md:col-end-${responsive.md.end}` : '',
    responsive.lg?.span ? `lg:${spanMap[responsive.lg.span]}` : '',
    responsive.lg?.start ? `lg:col-start-${responsive.lg.start}` : '',
    responsive.lg?.end ? `lg:col-end-${responsive.lg.end}` : '',
    responsive.xl?.span ? `xl:${spanMap[responsive.xl.span]}` : '',
    responsive.xl?.start ? `xl:col-start-${responsive.xl.start}` : '',
    responsive.xl?.end ? `xl:col-end-${responsive.xl.end}` : ''
  ].filter(Boolean).join(' ') : '';

  return (
    <div className={`${spanClass} ${startClass} ${endClass} ${responsiveClasses} ${className}`}>
      {children}
    </div>
  );
};

/**
 * Flex container for simpler layouts
 */
interface FlexProps {
  children: ReactNode;
  direction?: 'row' | 'col' | 'row-reverse' | 'col-reverse';
  wrap?: boolean;
  justify?: 'start' | 'end' | 'center' | 'between' | 'around' | 'evenly';
  align?: 'start' | 'end' | 'center' | 'baseline' | 'stretch';
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export const Flex: React.FC<FlexProps> = ({
  children,
  direction = 'row',
  wrap = false,
  justify = 'start',
  align = 'start',
  gap = 'none',
  className = ''
}) => {
  const directionClasses = {
    row: 'flex-row',
    col: 'flex-col',
    'row-reverse': 'flex-row-reverse',
    'col-reverse': 'flex-col-reverse'
  };

  const justifyClasses = {
    start: 'justify-start',
    end: 'justify-end',
    center: 'justify-center',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly'
  };

  const alignClasses = {
    start: 'items-start',
    end: 'items-end',
    center: 'items-center',
    baseline: 'items-baseline',
    stretch: 'items-stretch'
  };

  const wrapClass = wrap ? 'flex-wrap' : '';
  const gapClass = gapMap[gap];

  return (
    <div className={`flex ${directionClasses[direction]} ${wrapClass} ${justifyClasses[justify]} ${alignClasses[align]} ${gapClass} ${className}`}>
      {children}
    </div>
  );
};

/**
 * Stack component for vertical layouts
 */
interface StackProps {
  children: ReactNode;
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  align?: 'start' | 'center' | 'end' | 'stretch';
  className?: string;
}

export const Stack: React.FC<StackProps> = ({
  children,
  spacing = 'md',
  align = 'stretch',
  className = ''
}) => {
  return (
    <Flex
      direction="col"
      gap={spacing}
      align={align}
      className={className}
    >
      {children}
    </Flex>
  );
};

/**
 * Inline component for horizontal layouts
 */
interface InlineProps {
  children: ReactNode;
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  align?: 'start' | 'center' | 'end' | 'baseline';
  justify?: 'start' | 'end' | 'center' | 'between' | 'around' | 'evenly';
  wrap?: boolean;
  className?: string;
}

export const Inline: React.FC<InlineProps> = ({
  children,
  spacing = 'md',
  align = 'center',
  justify = 'start',
  wrap = false,
  className = ''
}) => {
  return (
    <Flex
      direction="row"
      gap={spacing}
      align={align}
      justify={justify}
      wrap={wrap}
      className={className}
    >
      {children}
    </Flex>
  );
};
