/**
 * Loading State Components
 * Consistent loading indicators and skeletons
 */

import React, { ReactNode } from 'react';
import { Spinner } from '@heroui/react';
import { Icon } from '../../ui/Icon';
import { Stack } from '../../layout/Grid';

/**
 * Loading spinner props
 */
export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  label?: string;
  className?: string;
}

/**
 * Loading spinner component
 */
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  label,
  className = ''
}) => {
  return (
    <div className={`flex flex-col items-center justify-center gap-2 ${className}`}>
      <Spinner size={size} color="primary" />
      {label && (
        <span className="text-custom-muted text-sm">{label}</span>
      )}
    </div>
  );
};

/**
 * Full page loading component
 */
export interface PageLoadingProps {
  message?: string;
  className?: string;
}

export const PageLoading: React.FC<PageLoadingProps> = ({
  message = 'Loading...',
  className = ''
}) => {
  return (
    <div className={`min-h-screen flex items-center justify-center ${className}`}>
      <LoadingSpinner size="lg" label={message} />
    </div>
  );
};

/**
 * Skeleton loading component
 */
export interface SkeletonProps {
  width?: string | number;
  height?: string | number;
  className?: string;
  rounded?: boolean;
  lines?: number;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = '1rem',
  className = '',
  rounded = false,
  lines = 1
}) => {
  const skeletonClass = `
    bg-gray-700 animate-pulse
    ${rounded ? 'rounded-full' : 'rounded'}
    ${className}
  `;

  if (lines === 1) {
    return (
      <div
        className={skeletonClass}
        style={{ width, height }}
      />
    );
  }

  return (
    <Stack spacing="sm">
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className={skeletonClass}
          style={{
            width: index === lines - 1 ? '75%' : width,
            height
          }}
        />
      ))}
    </Stack>
  );
};

/**
 * Card skeleton for loading cards
 */
export const CardSkeleton: React.FC<{ className?: string }> = ({
  className = ''
}) => {
  return (
    <div className={`bg-custom-card border border-custom-border rounded-lg p-4 ${className}`}>
      <Stack spacing="md">
        <div className="flex items-center gap-3">
          <Skeleton width="3rem" height="3rem" rounded />
          <div className="flex-1">
            <Skeleton width="60%" height="1rem" />
            <div className="mt-2">
              <Skeleton width="40%" height="0.75rem" />
            </div>
          </div>
        </div>
        <Skeleton lines={3} />
      </Stack>
    </div>
  );
};

/**
 * Table skeleton for loading tables
 */
export interface TableSkeletonProps {
  rows?: number;
  columns?: number;
  className?: string;
}

export const TableSkeleton: React.FC<TableSkeletonProps> = ({
  rows = 5,
  columns = 4,
  className = ''
}) => {
  return (
    <div className={`bg-custom-card border border-custom-border rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-custom-sidebar border-b border-custom-border p-4">
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, index) => (
            <Skeleton key={index} width="80%" height="1rem" />
          ))}
        </div>
      </div>
      
      {/* Rows */}
      <div className="divide-y divide-custom-border">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="p-4">
            <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
              {Array.from({ length: columns }).map((_, colIndex) => (
                <Skeleton key={colIndex} width="90%" height="1rem" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * Loading overlay component
 */
export interface LoadingOverlayProps {
  loading: boolean;
  children: ReactNode;
  message?: string;
  className?: string;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  loading,
  children,
  message = 'Loading...',
  className = ''
}) => {
  return (
    <div className={`relative ${className}`}>
      {children}
      {loading && (
        <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <LoadingSpinner size="lg" label={message} />
        </div>
      )}
    </div>
  );
};

/**
 * Inline loading component for buttons and small areas
 */
export interface InlineLoadingProps {
  size?: 'sm' | 'md';
  className?: string;
}

export const InlineLoading: React.FC<InlineLoadingProps> = ({
  size = 'sm',
  className = ''
}) => {
  return (
    <Icon
      icon="lucide:loader-2"
      size={size}
      className={`animate-spin ${className}`}
    />
  );
};

/**
 * Progress bar component
 */
export interface ProgressBarProps {
  value: number;
  max?: number;
  label?: string;
  showPercentage?: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'success' | 'warning' | 'danger';
  className?: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max = 100,
  label,
  showPercentage = true,
  size = 'md',
  color = 'primary',
  className = ''
}) => {
  const percentage = Math.min((value / max) * 100, 100);
  
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  };
  
  const colorClasses = {
    primary: 'bg-blue-600',
    success: 'bg-green-600',
    warning: 'bg-yellow-600',
    danger: 'bg-red-600'
  };

  return (
    <div className={className}>
      {(label || showPercentage) && (
        <div className="flex justify-between items-center mb-2">
          {label && (
            <span className="text-custom-text text-sm font-medium">{label}</span>
          )}
          {showPercentage && (
            <span className="text-custom-muted text-sm">{Math.round(percentage)}%</span>
          )}
        </div>
      )}
      
      <div className={`w-full bg-gray-700 rounded-full ${sizeClasses[size]}`}>
        <div
          className={`${sizeClasses[size]} ${colorClasses[color]} rounded-full transition-all duration-300 ease-out`}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
};
