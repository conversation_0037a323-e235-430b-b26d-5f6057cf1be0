import React, { useState } from 'react';
import { But<PERSON> } from '@heroui/react';
import { TauriIcon as Icon } from '../components/TauriIcon';
import { ScreenVisibility } from '../utils/visibility';

/**
 * Demo component to test the dynamic eye button functionality
 * This demonstrates the implementation without needing the full TopBar context
 */
export const EyeButtonDemo: React.FC = () => {
  const [screenActive, setScreenActive] = useState(false);

  const handleToggle = () => {
    setScreenActive(prev => !prev);
    console.log('Screen active toggled to:', !screenActive);
  };

  return (
    <div className="p-8 bg-custom-background min-h-screen">
      <div className="max-w-md mx-auto">
        <h1 className="text-white text-2xl font-bold mb-6">Eye Button Demo</h1>
        
        <div className="bg-custom-card p-6 rounded-lg border border-custom-border">
          <h2 className="text-white text-lg mb-4">Dynamic Eye <PERSON></h2>
          
          <div className="flex items-center gap-4 mb-4">
            <Button
              isIconOnly
              variant="light"
              className={`
                w-10 h-10 min-w-10 transition-all duration-200 ease-in-out
                hover:scale-105 active:scale-95 rounded-md backdrop-blur-sm shadow-sm
                focus:outline-none focus:ring-2 focus:ring-opacity-40
                ${screenActive
                  ? "text-custom-primary bg-primary/15 hover:bg-primary/25 focus:ring-custom-primary border border-custom-primary/20"
                  : "text-custom-text/80 hover:text-white bg-custom-card/60 hover:bg-custom-card/80 active:bg-custom-card focus:ring-custom-primary"
                }
              `}
              onPress={handleToggle}
              title={ScreenVisibility.getTooltip(screenActive)}
              aria-label={ScreenVisibility.getAriaLabel(screenActive)}
            >
              <Icon 
                icon={ScreenVisibility.getIcon(screenActive)} 
                className="text-xl" 
              />
            </Button>
            
            <div className="text-white">
              <p className="font-medium">Screen Status: {screenActive ? 'Active' : 'Inactive'}</p>
              <p className="text-sm text-custom-muted">
                Icon: {ScreenVisibility.getIcon(screenActive)}
              </p>
            </div>
          </div>

          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-custom-muted">Tooltip:</span>
              <span className="text-white">{ScreenVisibility.getTooltip(screenActive)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-custom-muted">Aria Label:</span>
              <span className="text-white text-xs">{ScreenVisibility.getAriaLabel(screenActive)}</span>
            </div>
          </div>

          <div className="mt-6 pt-4 border-t border-custom-border">
            <h3 className="text-white font-medium mb-2">Test Actions</h3>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="bordered"
                onPress={() => setScreenActive(true)}
                className="text-white border-custom-border"
              >
                Set Active
              </Button>
              <Button
                size="sm"
                variant="bordered"
                onPress={() => setScreenActive(false)}
                className="text-white border-custom-border"
              >
                Set Inactive
              </Button>
              <Button
                size="sm"
                variant="bordered"
                onPress={handleToggle}
                className="text-white border-custom-border"
              >
                Toggle
              </Button>
            </div>
          </div>
        </div>

        <div className="mt-6 bg-custom-sidebar p-4 rounded-lg border border-custom-border">
          <h3 className="text-white font-medium mb-2">Implementation Details</h3>
          <ul className="text-sm text-custom-muted space-y-1">
            <li>• Uses ScreenVisibility utility functions</li>
            <li>• Dynamic icon based on screen_active state</li>
            <li>• Proper accessibility labels and tooltips</li>
            <li>• Consistent styling with TopBar implementation</li>
            <li>• Smooth transitions and hover effects</li>
          </ul>
        </div>
      </div>
    </div>
  );
};
