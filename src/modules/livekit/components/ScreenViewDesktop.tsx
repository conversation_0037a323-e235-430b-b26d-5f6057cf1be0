import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Room, RoomEvent, Track } from 'livekit-client';
import { Icon } from '@iconify/react';

// Tauri APIs will be dynamically imported when needed

// Types
type StatusDot = 'red' | 'orange' | 'blue' | 'green' | 'gray';

interface ScreenViewUserProfile {
  name: string;
  email: string;
}

interface ScreenViewEmployeeData {
  id: number;
  name: string;
}

interface WindowData {
  currentUser: ScreenViewUserProfile;
  selectedEmployee: ScreenViewEmployeeData;
}

interface DragStart {
  x: number;
  y: number;
}

export const ScreenViewDesktop: React.FC = () => {
  // Debug state
  const [debugInfo, setDebugInfo] = useState<string>('Initializing...');

  // Window state
  const [isMaximized, setIsMaximized] = useState(false);
  const [appWindow, setAppWindow] = useState<any>(null);

  // Data state
  const [currentUser, setCurrentUser] = useState<ScreenViewUserProfile | null>(null);
  const [selectedEmployee, setSelectedEmployee] = useState<ScreenViewEmployeeData | null>(null);
  
  // Connection state
  const [room, setRoom] = useState<Room | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [statusDot, setStatusDot] = useState<StatusDot>('red');
  const [isVideoVisible, setIsVideoVisible] = useState(false);
  const [_error, setError] = useState<string | null>(null);

  // Video controls state
  const [scale, setScale] = useState(1.0);
  const [videoPosX, setVideoPosX] = useState(0);
  const [videoPosY, setVideoPosY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState<DragStart | null>(null);

  // Refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Helper function to get status dot color
  const getStatusColor = (status: StatusDot): string => {
    switch (status) {
      case 'red': return 'bg-red-500';
      case 'orange': return 'bg-orange-500';
      case 'blue': return 'bg-blue-500';
      case 'green': return 'bg-green-500';
      case 'gray': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  // Window control handlers
  const handleMinimize = async () => {
    try {
      if (appWindow && typeof appWindow.minimize === 'function') {
        await appWindow.minimize();
      }
    } catch (error) {
      console.warn('⚠️ [SCREEN_VIEW] Minimize failed:', error);
    }
  };

  const handleMaximize = async () => {
    try {
      if (appWindow && typeof appWindow.maximize === 'function' && typeof appWindow.unmaximize === 'function') {
        if (isMaximized) {
          await appWindow.unmaximize();
        } else {
          await appWindow.maximize();
        }
        setIsMaximized(!isMaximized);
      }
    } catch (error) {
      console.warn('⚠️ [SCREEN_VIEW] Maximize failed:', error);
    }
  };

  const handleClose = async () => {
    try {
      if (appWindow && typeof appWindow.close === 'function') {
        await appWindow.close();
      } else {
        // Fallback for web environment
        window.close();
      }
    } catch (error) {
      console.warn('⚠️ [SCREEN_VIEW] Close failed:', error);
    }
  };

  const handleDragStart = async () => {
    try {
      if (appWindow && typeof appWindow.startDragging === 'function') {
        await appWindow.startDragging();
      }
    } catch (error) {
      console.warn('⚠️ [SCREEN_VIEW] Drag failed:', error);
    }
  };

  // LiveKit token request via Tauri
  const requestLiveKitToken = async (name: string, userId: string, roomName: string, eventType: string) => {
    try {
      console.log('🔧 [LIVEKIT] Making Tauri token request with params:', {
        name,
        userId,
        roomName,
        eventType
      });

      // Try to get LiveKit token via Tauri or fallback
      let response: string;

      try {
        // Check if we're in Tauri environment
        if (typeof window !== 'undefined' && (window as any).__TAURI__) {
          // Use global Tauri API if available
          const tauriInvoke = (window as any).__TAURI__.tauri.invoke;
          if (tauriInvoke) {
            response = await tauriInvoke('get_livekit_token', {
              name,
              userId,
              roomName,
              eventType
            });
          } else {
            throw new Error('Tauri invoke not available');
          }
        } else {
          throw new Error('Not in Tauri environment');
        }
      } catch (error) {
        console.warn('⚠️ [SCREEN_VIEW] Tauri token request failed, using mock token:', error);
        // Fallback: Generate a mock token for development/web environment
        response = `mock_token_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      }

      console.log('✅ [LIVEKIT] Token received via Tauri');
      return response as string;
    } catch (error) {
      console.error('❌ [LIVEKIT] Tauri token request failed:', error);
      throw error;
    }
  };

  // Extract window data from Tauri event (not URL parameters)
  const extractWindowData = useCallback((): Promise<WindowData | null> => {
    return new Promise(async (resolve) => {
      try {
        // Check if Tauri is available
        if (typeof window !== 'undefined' && (window as any).__TAURI__) {
          try {
            // Use global Tauri API for event listening
            const tauriEvent = (window as any).__TAURI__.event;
            const tauriWindow = (window as any).__TAURI__.window;

            if (tauriEvent && tauriWindow && tauriWindow.getCurrent) {
              // Listen for employee data from Tauri
              const unlisten = await tauriEvent.listen('employee-data', (event: any) => {
                try {
                  console.log('📨 [SCREEN_VIEW] Received employee data from Tauri:', event.payload);
                  const windowData = JSON.parse(event.payload as string) as WindowData;
                  console.log('📊 [SCREEN_VIEW] Window data parsed for LiveKit connection');
                  unlisten(); // Clean up listener
                  resolve(windowData);
                } catch (error) {
                  console.error('❌ [SCREEN_VIEW] Failed to parse employee data:', error);
                  resolve(null);
                }
              });

              // Timeout after 5 seconds if no data received
              setTimeout(() => {
                console.warn('⚠️ [SCREEN_VIEW] Timeout waiting for employee data');
                unlisten(); // Clean up listener
                resolve(null);
              }, 5000);
            } else {
              throw new Error('Tauri event API not available');
            }
          } catch (tauriError) {
            console.warn('⚠️ [SCREEN_VIEW] Tauri event setup failed:', tauriError);
            // Fall through to URL parameter fallback
          }
        } else {
          // Fallback to URL parameters for web environment
          console.log('🌐 [SCREEN_VIEW] Tauri not available, trying URL parameters...');
          try {
            const urlParams = new URLSearchParams(window.location.search);
            const employeeDataParam = urlParams.get('employee_data');

            if (!employeeDataParam) {
              console.warn('⚠️ [SCREEN_VIEW] No window data found in URL parameters');
              resolve(null);
              return;
            }

            const windowData = JSON.parse(decodeURIComponent(employeeDataParam)) as WindowData;
            console.log('📊 [SCREEN_VIEW] Window data received from URL parameters');
            resolve(windowData);
          } catch (error) {
            console.error('❌ [SCREEN_VIEW] Failed to parse URL parameters:', error);
            resolve(null);
          }
        }

      } catch (error) {
        console.error('❌ [SCREEN_VIEW] Failed to setup data extraction:', error);
        resolve(null);
      }
    });
  }, []);

  // Connect to LiveKit room
  const connectToLiveKit = useCallback(async () => {
    if (!currentUser || !selectedEmployee) {
      console.error('❌ [LIVEKIT] Missing user data for connection');
      return;
    }

    try {
      console.log('🎥 [LIVEKIT] Starting LiveKit connection process...');
      setStatusDot('orange');
      setIsLoading(true);

      // Get auth token via Tauri
      console.log('🟡 [LIVEKIT] Getting auth token via Tauri...');
      const roomName = `${selectedEmployee.id}:teamby`;
      const token = await requestLiveKitToken(
        currentUser.name,
        currentUser.email,
        roomName,
        'screen_view'
      );

      // Create and configure room
      const newRoom = new Room({
        audioCaptureDefaults: {
          autoGainControl: true,
          echoCancellation: true,
          noiseSuppression: true,
        },
        videoCaptureDefaults: {
          resolution: { width: 1280, height: 720, frameRate: 30 },
        },
        publishDefaults: {
          videoEncoding: { maxBitrate: 1_500_000, maxFramerate: 30 },
          screenShareEncoding: { maxBitrate: 1_500_000, maxFramerate: 30 },
        },
      });

      // Set up event listeners
      newRoom.on(RoomEvent.TrackSubscribed, handleTrackSubscribed);
      newRoom.on(RoomEvent.TrackUnsubscribed, handleTrackUnsubscribed);
      newRoom.on(RoomEvent.Disconnected, handleDisconnected);
      newRoom.on(RoomEvent.ConnectionStateChanged, handleConnectionStateChanged);
      newRoom.on(RoomEvent.ParticipantConnected, handleParticipantConnected);

      // Connect to room with timeout
      console.log('🟠 [LIVEKIT] Connecting to LiveKit room...');
      const connectPromise = newRoom.connect('wss://livekit.newhorizonco.uk', token);
      const connectTimeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Connection timeout')), 30000);
      });

      await Promise.race([connectPromise, connectTimeoutPromise]);
      console.log('✅ [LIVEKIT] Connected to room');
      
      setRoom(newRoom);
      setStatusDot('blue');
      console.log('🔵 [LIVEKIT] Waiting for video stream...');

      // Check for existing video tracks from remote participants
      console.log('🔍 [LIVEKIT] Checking for existing video tracks...');
      if (newRoom.remoteParticipants && newRoom.remoteParticipants.size > 0) {
        newRoom.remoteParticipants.forEach((participant, identity) => {
          console.log(`👤 [LIVEKIT] Checking participant: ${identity}`);
          if (participant.videoTrackPublications) {
            participant.videoTrackPublications.forEach((publication, trackSid) => {
              console.log(`📺 [LIVEKIT] Found video publication: ${trackSid}`, {
                subscribed: publication.isSubscribed,
                enabled: publication.isEnabled,
                muted: publication.isMuted,
                source: publication.source
              });
              
              if (publication.isSubscribed && publication.track) {
                console.log('🎥 [LIVEKIT] Existing video track found - attaching...');
                handleTrackSubscribed(publication.track, publication, participant);
              } else if (!publication.isSubscribed) {
                console.log('🔄 [LIVEKIT] Subscribing to existing video track...');
                publication.setSubscribed(true);
              }
            });
          }
        });
      } else {
        console.log('ℹ️ [LIVEKIT] No remote participants found yet');
      }

    } catch (error) {
      console.error('❌ [LIVEKIT] Connection failed:', error);
      console.log('🔧 [LIVEKIT] Connection error details:', {
        errorType: error instanceof Error ? error.constructor.name : typeof error,
        errorMessage: error instanceof Error ? error.message : String(error)
      });

      setStatusDot('gray');
      setError('Failed to connect to LiveKit room');
      setIsLoading(false);
    }
  }, [currentUser, selectedEmployee]);

  // Helper function to attach video with retry mechanism
  const attachVideoWithRetry = useCallback((track: any, maxRetries = 10, delay = 100) => {
    let attempts = 0;
    
    const tryAttach = () => {
      attempts++;
      console.log(`🔄 [LIVEKIT] Attempting to attach video (attempt ${attempts}/${maxRetries})`);
      
      if (videoRef.current) {
        console.log('🖥️ [LIVEKIT] Video element found - attaching...');
        
        // Detach any existing tracks first
        const existingTracks = videoRef.current.srcObject;
        if (existingTracks) {
          console.log('🧹 [LIVEKIT] Detaching existing video tracks');
          videoRef.current.srcObject = null;
        }
        
        track.attach(videoRef.current);
        console.log('✅ [LIVEKIT] Video attached successfully to element');
        
        // Ensure video plays
        videoRef.current.play().catch(error => {
          console.warn('⚠️ [LIVEKIT] Video play failed:', error);
        });
        
        return true; // Success
      } else if (attempts < maxRetries) {
        console.log(`⏳ [LIVEKIT] Video element not ready, retrying in ${delay}ms...`);
        setTimeout(tryAttach, delay);
        return false; // Will retry
      } else {
        console.error('❌ [LIVEKIT] Failed to attach video after maximum retries');
        return false; // Failed
      }
    };
    
    tryAttach();
  }, []);

  const handleTrackSubscribed = useCallback((track: any, publication: any, participant: any) => {
    console.log('📺 [LIVEKIT] Track subscribed:', {
      kind: track.kind,
      source: track.source,
      sid: track.sid,
      enabled: track.enabled,
      muted: track.muted,
      participantIdentity: participant?.identity,
      publicationSid: publication?.sid
    });
    
    if (track.kind === Track.Kind.Video) {
      console.log('🎥 [LIVEKIT] Video track received - processing...');
      setStatusDot('green');
      setIsLoading(false);
      setIsVideoVisible(true);
      
      // Reset video position and scale when new video is attached
      setScale(1.0);
      setVideoPosX(0);
      setVideoPosY(0);
      
      // Use retry mechanism to attach video
      attachVideoWithRetry(track);
    } else if (track.kind === Track.Kind.Audio) {
      console.log('🔊 [LIVEKIT] Audio track received (not processing for screen view)');
    } else {
      console.log('❓ [LIVEKIT] Unknown track type:', track.kind);
    }
  }, [attachVideoWithRetry]);

  const handleTrackUnsubscribed = useCallback((track: any, publication: any, participant: any) => {
    console.log('📺 [LIVEKIT] Track unsubscribed:', {
      kind: track.kind,
      source: track.source,
      sid: track.sid,
      participantIdentity: participant?.identity
    });
    
    if (track.kind === Track.Kind.Video) {
      console.log('🎥 [LIVEKIT] Video track unsubscribed - hiding video');
      setIsVideoVisible(false);
      setStatusDot('blue');
      
      if (videoRef.current) {
        track.detach(videoRef.current);
      }
    }
  }, []);

  const handleDisconnected = useCallback(() => {
    console.log('❌ [LIVEKIT] Disconnected from room');
    setStatusDot('gray');
    setIsVideoVisible(false);
    setError('Disconnected from room');
    
    // No automatic reconnection - just log the disconnection
    console.log('ℹ️ [LIVEKIT] Connection ended - no automatic reconnection');
  }, []);

  const handleConnectionStateChanged = useCallback((state: any) => {
    console.log('🔄 [LIVEKIT] Connection state changed:', state);

    // Handle different connection states
    switch (state) {
      case 'connected':
        console.log('✅ [LIVEKIT] Connection established');
        break;
      case 'connecting':
        console.log('🔄 [LIVEKIT] Connecting...');
        setStatusDot('orange');
        break;
      case 'disconnected':
        console.log('❌ [LIVEKIT] Connection lost');
        setStatusDot('gray');
        break;
    }
  }, []);

  const handleParticipantConnected = useCallback((participant: any) => {
    console.log('👤 [LIVEKIT] Participant connected:', participant.identity);
    
    // Check if this participant has video tracks
    if (participant.videoTrackPublications) {
      participant.videoTrackPublications.forEach((publication: any, trackSid: string) => {
        console.log(`📺 [LIVEKIT] New participant has video publication: ${trackSid}`, {
          subscribed: publication.isSubscribed,
          enabled: publication.isEnabled,
          muted: publication.isMuted,
          source: publication.source
        });
        
        if (!publication.isSubscribed) {
          console.log('🔄 [LIVEKIT] Subscribing to new participant video track...');
          publication.setSubscribed(true);
        }
      });
    } else {
      console.log('ℹ️ [LIVEKIT] New participant has no video tracks yet');
    }
  }, []);

  // Video zoom with wheel based on documentation
  const handleWheel = useCallback((e: React.WheelEvent) => {
    if (!isVideoVisible) return;

    e.preventDefault();

    let newScale: number;
    if (e.deltaY < 0) {
      // Zoom in (from documentation)
      newScale = scale + 0.1;
    } else {
      // Zoom out (from documentation)
      newScale = Math.max(1, scale - 0.1);
    }

    setScale(newScale);

    // Reset position when scale reaches 1 (from documentation)
    if (newScale === 1.0) {
      setVideoPosX(0);
      setVideoPosY(0);
      if (videoRef.current) {
        videoRef.current.style.cursor = 'default';
        videoRef.current.style.left = '0px';
        videoRef.current.style.top = '0px';
      }
    } else if (videoRef.current) {
      // Set cursor to grab when zoomed (from documentation)
      videoRef.current.style.cursor = 'grab';
    }

    console.log('🖱️ [WHEEL] Video scale:', { oldScale: scale, newScale });
  }, [scale, isVideoVisible]);

  // Video drag handlers based on documentation
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (scale <= 1.0 || !isVideoVisible) return;

    setIsDragging(true);
    setDragStart({ x: e.clientX - videoPosX, y: e.clientY - videoPosY });

    // Set cursor to grabbing (from documentation)
    if (videoRef.current) {
      videoRef.current.style.cursor = 'grabbing';
    }

    console.log('🖱️ [DRAG] Started dragging:', {
      clientX: e.clientX,
      clientY: e.clientY,
      videoPosX,
      videoPosY,
      scale
    });
  }, [scale, videoPosX, videoPosY, isVideoVisible]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDragging || scale <= 1.0 || !containerRef.current || !videoRef.current) return;

    const container = containerRef.current;
    const video = videoRef.current;

    // Calculate new position based on documentation formula
    let newPosX = e.clientX - dragStart.x;
    let newPosY = e.clientY - dragStart.y;

    // Calculate boundary constraints (from documentation)
    const maxPosX = (container.clientWidth - video.clientWidth * scale) / 2;
    const maxPosY = (container.clientHeight - video.clientHeight * scale) / 2;

    // Apply constraints to keep video within container bounds
    const constrainedX = Math.max(maxPosX, Math.min(-maxPosX, newPosX));
    const constrainedY = Math.max(maxPosY, Math.min(-maxPosY, newPosY));

    console.log('🖱️ [DRAG] Position calculation:', {
      newPosX,
      newPosY,
      maxPosX,
      maxPosY,
      constrainedX,
      constrainedY,
      scale,
      containerWidth: container.clientWidth,
      containerHeight: container.clientHeight,
      videoWidth: video.clientWidth,
      videoHeight: video.clientHeight
    });

    setVideoPosX(constrainedX);
    setVideoPosY(constrainedY);
  }, [isDragging, dragStart, scale]);

  const handleMouseUp = useCallback(() => {
    if (isDragging) {
      setIsDragging(false);
      // Set cursor back to grab (from documentation)
      if (videoRef.current) {
        videoRef.current.style.cursor = 'grab';
      }
      console.log('🖱️ [DRAG] Stopped dragging');
    }
  }, [isDragging]);

  // Keyboard shortcuts based on documentation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isVideoVisible) return;

      if (e.ctrlKey) {
        switch (e.key) {
          case '+':
          case '=':
            e.preventDefault();
            const newScaleUp = Math.min(3.0, scale + 0.1);
            setScale(newScaleUp);

            // Set cursor to grab when zooming in (from documentation)
            if (newScaleUp > 1 && videoRef.current) {
              videoRef.current.style.cursor = 'grab';
            }

            console.log('⌨️ [KEYBOARD] Zoom in:', { oldScale: scale, newScale: newScaleUp });
            break;

          case '-':
            e.preventDefault();
            const newScaleDown = Math.max(1.0, scale - 0.1);
            setScale(newScaleDown);

            // Reset position and cursor when scale reaches 1 (from documentation)
            if (newScaleDown === 1.0) {
              setVideoPosX(0);
              setVideoPosY(0);
              if (videoRef.current) {
                videoRef.current.style.cursor = 'default';
                videoRef.current.style.left = '0px';
                videoRef.current.style.top = '0px';
              }
            }

            console.log('⌨️ [KEYBOARD] Zoom out:', { oldScale: scale, newScale: newScaleDown });
            break;

          case '0':
            e.preventDefault();
            setScale(1.0);
            setVideoPosX(0);
            setVideoPosY(0);

            // Reset cursor and position (from documentation)
            if (videoRef.current) {
              videoRef.current.style.cursor = 'default';
              videoRef.current.style.left = '0px';
              videoRef.current.style.top = '0px';
            }

            console.log('⌨️ [KEYBOARD] Reset zoom');
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isVideoVisible, scale]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (room) {
        console.log('🧹 [LIVEKIT] Cleaning up room connection...');
        room.disconnect();
      }
    };
  }, [room]);

  // Initialize window and connect to LiveKit
  useEffect(() => {
    const initWindow = async () => {
      try {
        console.log('🔍 [SCREEN_VIEW] Initializing LiveKit screen view...');
        setDebugInfo('Loading...');

        // Initialize Tauri window controls
        try {
          if (typeof window !== 'undefined' && (window as any).__TAURI__) {
            setDebugInfo('Tauri Available');
            // Use global Tauri API for window controls
            const tauriWindow = (window as any).__TAURI__.window;
            if (tauriWindow && tauriWindow.getCurrent) {
              const currentWindow = tauriWindow.getCurrent();
              setAppWindow(currentWindow);

              // Check if window methods are available (Tauri desktop environment)
              if (currentWindow && typeof currentWindow.isMaximized === 'function') {
                const maximized = await currentWindow.isMaximized();
                setIsMaximized(maximized);
                setDebugInfo('Window Controls Ready');
                console.log('✅ [SCREEN_VIEW] Window controls initialized');
              } else {
                setDebugInfo('Window Controls N/A');
                console.log('⚠️ [SCREEN_VIEW] Window controls not available');
              }
            } else {
              setDebugInfo('Tauri Window API N/A');
              console.log('⚠️ [SCREEN_VIEW] Tauri window API not available');
            }
          } else {
            setDebugInfo('Web Mode');
            console.log('🌐 [SCREEN_VIEW] Tauri not available, window controls disabled');
          }
        } catch (windowError) {
          setDebugInfo('Window Init Failed');
          console.warn('⚠️ [SCREEN_VIEW] Window initialization failed:', windowError);
        }

        // Extract window data and set state (async)
        setDebugInfo('Getting Data...');
        const winData = await extractWindowData();

        if (!winData) {
          console.error('❌ [SCREEN_VIEW] No window data available');
          setDebugInfo('No Data');
          setStatusDot('gray');
          setIsLoading(false);
          return;
        }

        // Validate user data
        if (!winData.currentUser?.name || !winData.currentUser?.email) {
          console.error('❌ [SCREEN_VIEW] Invalid user data detected');
          setStatusDot('gray');
          setIsLoading(false);
          return;
        }

        if (!winData.selectedEmployee?.id || !winData.selectedEmployee?.name) {
          console.error('❌ [SCREEN_VIEW] Invalid employee data detected');
          setStatusDot('gray');
          setIsLoading(false);
          return;
        }

        console.log('👤 [SCREEN_VIEW] Current User:', winData.currentUser.name);
        console.log('👥 [SCREEN_VIEW] Target Employee:', winData.selectedEmployee.name, 'ID:', winData.selectedEmployee.id);

        // Set state
        setCurrentUser(winData.currentUser);
        setSelectedEmployee(winData.selectedEmployee);
        setDebugInfo('Ready');

        console.log('✅ [SCREEN_VIEW] Valid user data available, starting LiveKit connection...');
      } catch (error) {
        console.error('❌ [SCREEN_VIEW] Failed to initialize:', error);
        setStatusDot('gray');
        setIsLoading(false);
      }
    };

    initWindow();
  }, [extractWindowData]);

  // Connect to LiveKit when user data is available
  useEffect(() => {
    if (currentUser && selectedEmployee) {
      connectToLiveKit();
    }
  }, [currentUser, selectedEmployee, connectToLiveKit]);

  return (
    <div className="h-screen bg-black text-white flex flex-col">
      {/* Top Bar with Window Controls */}
      <div
        className="h-8 bg-gray-900 bg-opacity-80 flex items-center justify-between px-4 cursor-move"
        onMouseDown={handleDragStart}
      >
        {/* Left: Status dot */}
        <div className={`w-2 h-2 rounded-full ${getStatusColor(statusDot)}`} data-testid="status-dot"></div>

        {/* Center: Title */}
        <div className="text-sm text-gray-300">
          {selectedEmployee ? `Screen View - ${selectedEmployee.name}` : `Screen View - ${debugInfo}`}
        </div>

        {/* Right: Window Controls */}
        <div className="flex items-center gap-1">
          <button
            onClick={handleMinimize}
            className="w-6 h-6 rounded hover:bg-custom-border/30 flex items-center justify-center transition-colors"
          >
            <Icon icon="mdi:window-minimize" className="w-3 h-3" />
          </button>
          <button
            onClick={handleMaximize}
            className="w-6 h-6 rounded hover:bg-custom-border/30 flex items-center justify-center transition-colors"
          >
            <Icon icon={isMaximized ? "mdi:window-restore" : "mdi:window-maximize"} className="w-3 h-3" />
          </button>
          <button
            onClick={handleClose}
            className="w-6 h-6 rounded hover:bg-red-500/20 hover:text-red-400 flex items-center justify-center transition-colors"
          >
            <Icon icon="mdi:close" className="w-3 h-3" />
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 relative">
        {/* Loading State */}
        {isLoading && (
          <div className="h-full bg-black flex items-center justify-center">
            <div className="flex space-x-2" data-testid="loading-spinner">
              <div className="w-4 h-4 bg-blue-400 rounded-full animate-bounce shadow-lg shadow-blue-400/50"></div>
              <div className="w-4 h-4 bg-blue-400 rounded-full animate-bounce shadow-lg shadow-blue-400/50" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-4 h-4 bg-blue-400 rounded-full animate-bounce shadow-lg shadow-blue-400/50" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </div>
        )}

        {/* Video Container */}
        {!isLoading && (
          <div
            ref={containerRef}
            className="h-full bg-black relative overflow-hidden"
            onWheel={handleWheel}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            style={{ cursor: scale > 1.0 ? (isDragging ? 'grabbing' : 'grab') : 'default' }}
          >
            {/* Video Element - styled based on documentation */}
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-full object-contain bg-black rounded-lg"
              style={{
                display: isVideoVisible ? 'flex' : 'none',
                transform: `scale(${scale})`,
                transformOrigin: 'center center',
                position: 'relative',
                left: `${videoPosX}px`,
                top: `${videoPosY}px`,
                outline: 'none',
                border: '2px solid #3497da',
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
                backgroundColor: '#000'
              }}
              onFocus={(e) => {
                (e.target as HTMLVideoElement).style.outline = '2px solid #3498db';
              }}
            />

            {/* No Video State */}
            {!isVideoVisible && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-gray-400">
                  <div className="text-6xl mb-4">📺</div>
                  <div className="text-lg mb-2">
                    {statusDot === 'gray' ? 'Connection Error' : 'Waiting for Screen Share'}
                  </div>
                  <div className="text-sm opacity-70">
                    {statusDot === 'gray' ? 'Failed to connect to screen share' : 'Connecting...'}
                  </div>
                </div>
              </div>
            )}

            {/* Zoom Info Display */}
            {scale > 1 && (
              <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                {Math.round(scale * 100)}% | {Math.round(videoPosX)}, {Math.round(videoPosY)}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
