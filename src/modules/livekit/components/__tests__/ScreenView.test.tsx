import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import type { ScreenViewWindowData } from '../../../../types/ScreenViewTypes';

// Mock <PERSON> invoke
vi.mock('@tauri-apps/api/core', () => ({
  invoke: vi.fn()
}));

// Mock LiveKit
const mockRoom = {
  connect: vi.fn(),
  disconnect: vi.fn(),
  on: vi.fn(),
};
vi.mock('livekit-client', () => ({
  Room: vi.fn(() => mockRoom),
  Track: {
    Kind: {
      Video: 'video'
    }
  },
  RoomEvent: {
    TrackSubscribed: 'trackSubscribed',
    TrackUnsubscribed: 'trackUnsubscribed',
    Disconnected: 'disconnected',
    ConnectionStateChanged: 'connectionStateChanged'
  }
}));

// Import after mocking
import { ScreenView } from '../ScreenView';
import { invoke } from '@tauri-apps/api/core';

describe('ScreenView Component', () => {
  const mockWindowData: ScreenViewWindowData = {
    currentUser: {
      full_name: 'admin developer',
      email: '<EMAIL>',
      avatar: null,
      position: 'Administrator',
      company: 'Test Company',
      is_admin: true,
      screen_active: false
    },
    selectedEmployee: {
      id: 31,
      full_name: 'Alireza Mortezaei',
      position_name: 'Developer',
      avatar: 'https://example.com/avatar.jpg',
      isonline: true,
      screan_active: true,
      email: '<EMAIL>',
      phone: '+1234567890'
    },
    timestamp: Date.now(),
    source: 'team-sidebar'
  };

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Mock successful token response
    vi.mocked(invoke).mockResolvedValue({ token: 'mock-livekit-token' });

    // Mock successful room connection
    mockRoom.connect.mockResolvedValue(undefined);
  });

  afterEach(() => {
    // Clean up URL parameters
    delete (window as any).location;
    (window as any).location = { search: '' };
  });

  describe('URL Parameter Parsing', () => {
    it('should fail when no employee_data parameter is present', async () => {
      // Arrange: No URL parameters
      Object.defineProperty(window, 'location', {
        value: { search: '' },
        writable: true
      });

      // Act
      render(<ScreenView />);

      // Assert: Should show error state (gray status dot) due to missing data
      await waitFor(() => {
        const statusDot = screen.getByTestId('status-dot');
        expect(statusDot).toHaveClass('bg-gray-500');
      });

      // Wait and verify no LiveKit connection attempt
      await waitFor(() => {
        expect(invoke).not.toHaveBeenCalled();
      }, { timeout: 1000 });
    });

    it('should fail when employee_data parameter contains invalid JSON', async () => {
      // Arrange: Invalid JSON in URL parameter
      Object.defineProperty(window, 'location', {
        value: { search: '?employee_data=invalid-json' },
        writable: true
      });

      // Act
      render(<ScreenView />);

      // Assert: Should show error state (gray status dot) due to parsing error
      await waitFor(() => {
        const statusDot = screen.getByTestId('status-dot');
        expect(statusDot).toHaveClass('bg-gray-500');
      });

      // Wait and verify no LiveKit connection attempt
      await waitFor(() => {
        expect(invoke).not.toHaveBeenCalled();
      }, { timeout: 1000 });
    });

    it('should successfully parse valid employee_data parameter', async () => {
      // Arrange: Valid encoded JSON in URL parameter
      const encodedData = encodeURIComponent(JSON.stringify(mockWindowData));
      Object.defineProperty(window, 'location', {
        value: { search: `?employee_data=${encodedData}` },
        writable: true
      });

      // Act
      render(<ScreenView />);

      // Assert: Should show blue status (waiting for video) since web fallback works
      await waitFor(() => {
        const statusDot = screen.getByTestId('status-dot');
        expect(statusDot).toHaveClass('bg-blue-500');
      });

      // Verify that data was parsed correctly and web fallback was used
      expect(invoke).not.toHaveBeenCalled(); // Should not be called in web environment
    });

    it('should fail when currentUser data is missing required fields', async () => {
      // Arrange: Missing required currentUser fields
      const invalidData = {
        ...mockWindowData,
        currentUser: {
          full_name: '',  // Missing required field
          email: '',      // Missing required field
          avatar: null,
          position: null,
          company: null,
          is_admin: false,
          screen_active: false
        }
      };
      
      const encodedData = encodeURIComponent(JSON.stringify(invalidData));
      Object.defineProperty(window, 'location', {
        value: { search: `?employee_data=${encodedData}` },
        writable: true
      });

      // Act
      render(<ScreenView />);

      // Assert: Should show error state (gray status dot)
      await waitFor(() => {
        const statusDot = screen.getByTestId('status-dot');
        expect(statusDot).toHaveClass('bg-gray-500');
      });
    });

    it('should fail when selectedEmployee data is missing required fields', async () => {
      // Arrange: Missing required selectedEmployee fields
      const invalidData = {
        ...mockWindowData,
        selectedEmployee: {
          ...mockWindowData.selectedEmployee,
          id: 0,           // Invalid ID
          full_name: ''    // Missing required field
        }
      };
      
      const encodedData = encodeURIComponent(JSON.stringify(invalidData));
      Object.defineProperty(window, 'location', {
        value: { search: `?employee_data=${encodedData}` },
        writable: true
      });

      // Act
      render(<ScreenView />);

      // Assert: Should show error state (gray status dot)
      await waitFor(() => {
        const statusDot = screen.getByTestId('status-dot');
        expect(statusDot).toHaveClass('bg-gray-500');
      });
    });
  });

  describe('State Management Timing', () => {
    it('should set currentUser and selectedEmployee state before calling connectToLiveKit', async () => {
      // Test that state management timing is fixed
      const encodedData = encodeURIComponent(JSON.stringify(mockWindowData));
      Object.defineProperty(window, 'location', {
        value: { search: `?employee_data=${encodedData}` },
        writable: true
      });

      render(<ScreenView />);

      // Should show blue status (waiting for video) since web fallback works and timing is fixed
      await waitFor(() => {
        const statusDot = screen.getByTestId('status-dot');
        expect(statusDot).toHaveClass('bg-blue-500');
      });

      // The fact that we reach this point without "Missing user data" error proves timing is fixed
      expect(invoke).not.toHaveBeenCalled(); // Should not be called in web environment
    });
  });

  describe('Invoke Function Availability', () => {
    it('should properly detect when invoke is not available even after initialization', async () => {
      // This test exposes the real issue: invoke is undefined even after "successful" initialization
      const encodedData = encodeURIComponent(JSON.stringify(mockWindowData));
      Object.defineProperty(window, 'location', {
        value: { search: `?employee_data=${encodedData}` },
        writable: true
      });

      // Mock __TAURI__ to simulate Tauri environment
      (window as any).__TAURI__ = true;

      render(<ScreenView />);

      // Should detect that invoke is actually undefined and fallback to web
      await waitFor(() => {
        const statusDot = screen.getByTestId('status-dot');
        expect(statusDot).toHaveClass('bg-blue-500');
      });
    });

    it('should fallback to web when invoke function throws error', async () => {
      // Simulate invoke function that exists but throws error
      const encodedData = encodeURIComponent(JSON.stringify(mockWindowData));
      Object.defineProperty(window, 'location', {
        value: { search: `?employee_data=${encodedData}` },
        writable: true
      });

      // Mock __TAURI__ and make invoke throw error
      (window as any).__TAURI__ = true;
      const mockInvoke = vi.fn().mockRejectedValue(new Error('invoke is not defined'));
      vi.doMock('@tauri-apps/api/core', () => ({
        invoke: mockInvoke
      }));

      render(<ScreenView />);

      // Should fallback to web and still work
      await waitFor(() => {
        const statusDot = screen.getByTestId('status-dot');
        expect(statusDot).toHaveClass('bg-blue-500');
      });
    });
  });
});
