import React, { useState, useEffect, useCallback, useRef } from 'react';
// import { getCurrentWindow } from '@tauri-apps/api/window';
import { Room, Track, RoomEvent } from 'livekit-client';
// import { TauriIcon as Icon } from '../../../components/TauriIcon';
import type { ScreenViewEmployeeData, ScreenViewWindowData, ScreenViewUserProfile } from "../../../types/ScreenViewTypes";

// COMMENTED OUT: Dynamic import for <PERSON><PERSON> invoke to handle both dev and production environments
// let invoke: any = null;

// COMMENTED OUT: Initialize Tauri invoke
// const initializeTauriInvoke = async () => {
//   try {
//     if (typeof window !== 'undefined' && (window as any).__TAURI__) {
//       const tauriCore = await import('@tauri-apps/api/core');
//       invoke = tauriCore.invoke;
//       console.log('✅ [TAURI] Invoke function initialized successfully');
//       return true;
//     } else {
//       console.warn('⚠️ [TAURI] Not running in Tauri environment');
//       return false;
//     }
//   } catch (error) {
//     console.error('❌ [TAURI] Failed to initialize invoke:', error);
//     return false;
//   }
// };

// Status dot types
type StatusDot = 'red' | 'yellow' | 'orange' | 'blue' | 'green' | 'gray';

// LiveKit token response interface
interface LiveKitTokenResponse {
  token: string;
}

export const ScreenView: React.FC = () => {
  // COMMENTED OUT: Window state (Tauri only)
  // const [isMaximized, setIsMaximized] = useState(false);
  // const [appWindow, setAppWindow] = useState<any>(null);

  // Data state
  const [currentUser, setCurrentUser] = useState<ScreenViewUserProfile | null>(null);
  const [selectedEmployee, setSelectedEmployee] = useState<ScreenViewEmployeeData | null>(null);

  // LiveKit state
  const [room, setRoom] = useState<Room | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [statusDot, setStatusDot] = useState<StatusDot>('red');
  const [isVideoVisible, setIsVideoVisible] = useState(false);
  const [_error, setError] = useState<string | null>(null);

  // REMOVED: Reconnection state (causing infinite loops)

  // Video controls state
  const [scale, setScale] = useState(1.0);
  const [videoPosX, setVideoPosX] = useState(0);
  const [videoPosY, setVideoPosY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  // Refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // COMMENTED OUT: Environment detection (not needed for web-only)
  // const isTauriEnvironment = typeof window !== 'undefined' && (window as any).__TAURI__;

  // Helper function to get status dot color
  const getStatusColor = (status: StatusDot): string => {
    switch (status) {
      case 'red': return 'bg-red-500';
      case 'yellow': return 'bg-yellow-500';
      case 'orange': return 'bg-orange-500';
      case 'blue': return 'bg-blue-500';
      case 'green': return 'bg-green-500';
      case 'gray': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  // Data validation functions
  const validateCurrentUser = (user: ScreenViewUserProfile | null): boolean => {
    if (!user) return false;
    return !!(user.full_name && user.full_name.trim() && user.email && user.email.trim());
  };

  const validateSelectedEmployee = (employee: ScreenViewEmployeeData | null): boolean => {
    if (!employee) return false;
    return !!(employee.id && employee.id > 0 && employee.full_name && employee.full_name.trim());
  };

  // COMMENTED OUT: Window control handlers
  // const handleMinimize = async () => {
  //   try {
  //     if (appWindow && typeof appWindow.minimize === 'function') {
  //       await appWindow.minimize();
  //     }
  //   } catch (error) {
  //     console.warn('⚠️ [SCREEN_VIEW] Minimize failed:', error);
  //   }
  // };

  // const handleMaximize = async () => {
  //   try {
  //     if (appWindow && typeof appWindow.maximize === 'function' && typeof appWindow.unmaximize === 'function') {
  //       if (isMaximized) {
  //         await appWindow.unmaximize();
  //       } else {
  //         await appWindow.maximize();
  //       }
  //       setIsMaximized(!isMaximized);
  //     }
  //   } catch (error) {
  //     console.warn('⚠️ [SCREEN_VIEW] Maximize failed:', error);
  //   }
  // };

  // const handleClose = async () => {
  //   try {
  //     if (appWindow && typeof appWindow.close === 'function') {
  //       await appWindow.close();
  //     } else {
  //       // Fallback for web environment
  //       window.close();
  //     }
  //   } catch (error) {
  //     console.warn('⚠️ [SCREEN_VIEW] Close failed:', error);
  //   }
  // };

  // const handleDragStart = async () => {
  //   try {
  //     if (appWindow && typeof appWindow.startDragging === 'function') {
  //       await appWindow.startDragging();
  //     }
  //   } catch (error) {
  //     console.warn('⚠️ [SCREEN_VIEW] Drag failed:', error);
  //   }
  // };

  // Web-based HTTP request function (fallback for non-Tauri environments)
  const requestLiveKitTokenWeb = async (
    name: string,
    userId: string,
    roomName: string,
    eventType: string
  ): Promise<string> => {
    console.log('🌐 [LIVEKIT] Making web-based token request');

    const response = await fetch('https://qa.habibapp.com/meet/livekit-apis/public-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name,
        user_id: userId,
        room_name: roomName,
        event_type: eventType,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json() as LiveKitTokenResponse;
    return data.token;
  };

  // LiveKit token request function (WEB ONLY - Tauri code commented out)
  const requestLiveKitToken = async (
    name: string,
    userId: string,
    roomName: string,
    eventType: string
  ): Promise<string> => {
    try {
      console.log('🔧 [LIVEKIT] Making token request with params:', { name, userId, roomName, eventType });

      // COMMENTED OUT: More robust check for Tauri environment and invoke availability
      // const hasTauriWindow = typeof window !== 'undefined' && (window as any).__TAURI__;

      // console.log('🔧 [LIVEKIT] Environment check:', {
      //   hasTauriWindow,
      //   hasInvokeFunction: invoke && typeof invoke === 'function',
      //   invokeType: typeof invoke,
      //   invokeExists: !!invoke
      // });

      // COMMENTED OUT: Try Tauri invoke first, but with better error handling
      // if (hasTauriWindow) {
      //   try {
      //     console.log('🖥️ [LIVEKIT] Attempting Tauri invoke for token request');
      //
      //     // Double-check invoke availability right before using it
      //     if (!invoke || typeof invoke !== 'function') {
      //       throw new Error('Invoke function not available at runtime');
      //     }
      //
      //     const response = await invoke('request_livekit_token', {
      //       name,
      //       userId,
      //       roomName,
      //       eventType,
      //     }) as LiveKitTokenResponse;
      //
      //     console.log('✅ [LIVEKIT] Tauri invoke successful');
      //     return response.token;
      //   } catch (tauriError) {
      //     console.warn('⚠️ [LIVEKIT] Tauri invoke failed, falling back to web:', tauriError);
      //     return await requestLiveKitTokenWeb(name, userId, roomName, eventType);
      //   }
      // } else {
        console.log('🌐 [LIVEKIT] Using web fetch for token request');
        return await requestLiveKitTokenWeb(name, userId, roomName, eventType);
      // }
    } catch (error) {
      console.error('❌ [LIVEKIT] Token request failed:', error);
      throw error;
    }
  };

  // LiveKit connection function with timeout management
  const connectToLiveKit = async () => {
    // Validate user data
    if (!validateCurrentUser(currentUser)) {
      console.error('❌ [LIVEKIT] Invalid current user data');
      setStatusDot('gray');
      setError('Invalid current user data');
      setIsLoading(false);
      return;
    }

    if (!validateSelectedEmployee(selectedEmployee)) {
      console.error('❌ [LIVEKIT] Invalid selected employee data');
      setStatusDot('gray');
      setError('Invalid selected employee data');
      setIsLoading(false);
      return;
    }

    // Timeout for each stage (30 seconds)
    const STAGE_TIMEOUT = 30000;

    try {
      console.log('🎥 [LIVEKIT] Starting LiveKit connection process...');

      // Step 1: Get token (Yellow) with timeout
      setStatusDot('yellow');
      console.log('🟡 [LIVEKIT] Getting auth token...');

      const roomName = `${selectedEmployee.id}:teamby`;
      const tokenPromise = requestLiveKitToken(
        currentUser.full_name,
        currentUser.email, // Using email as user_id
        roomName,
        'screen_view'
      );

      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Token request timeout')), STAGE_TIMEOUT);
      });

      const token = await Promise.race([tokenPromise, timeoutPromise]);
      console.log('✅ [LIVEKIT] Token received');

      // Step 2: Connect to room (Orange) with timeout
      setStatusDot('orange');
      console.log('🟠 [LIVEKIT] Connecting to LiveKit room...');

      const newRoom = new Room();
      setRoom(newRoom);

      // Set up event listeners
      newRoom.on(RoomEvent.TrackSubscribed, handleTrackSubscribed);
      newRoom.on(RoomEvent.TrackUnsubscribed, handleTrackUnsubscribed);
      newRoom.on(RoomEvent.Disconnected, handleDisconnected);
      newRoom.on(RoomEvent.ConnectionStateChanged, handleConnectionStateChanged);
      newRoom.on(RoomEvent.ParticipantConnected, handleParticipantConnected);

      // Connect to room with timeout
      console.log('🟠 [LIVEKIT] Connecting to LiveKit room...');
      console.log('🔧 [LIVEKIT] Connection details:', {
        url: 'wss://livekit.newhorizonco.uk',
        tokenLength: token.length,
        roomName: `${selectedEmployee?.id}:teamby`,
        tokenPreview: token.substring(0, 20) + '...'
      });
      setStatusDot('orange');

      const connectPromise = newRoom.connect('wss://livekit.newhorizonco.uk', token);
      const connectTimeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Room connection timeout')), STAGE_TIMEOUT);
      });

      await Promise.race([connectPromise, connectTimeoutPromise]);
      console.log('✅ [LIVEKIT] Connected to room');
      console.log('🔧 [LIVEKIT] Room info:', {
        name: newRoom.name,
        participants: newRoom.numParticipants,
        localParticipant: newRoom.localParticipant?.identity,
        remoteParticipants: newRoom.remoteParticipants ? Array.from(newRoom.remoteParticipants.keys()) : []
      });

      // REMOVED: Reset reconnection attempts (no longer needed)

      // Check for existing video tracks from remote participants
      console.log('🔍 [LIVEKIT] Checking for existing video tracks...');
      if (newRoom.remoteParticipants && newRoom.remoteParticipants.size > 0) {
        newRoom.remoteParticipants.forEach((participant, identity) => {
          console.log(`👤 [LIVEKIT] Checking participant: ${identity}`);
          if (participant.videoTrackPublications) {
            participant.videoTrackPublications.forEach((publication, trackSid) => {
              console.log(`📺 [LIVEKIT] Found video publication: ${trackSid}`, {
                subscribed: publication.isSubscribed,
                enabled: publication.isEnabled,
                muted: publication.isMuted,
                source: publication.source
              });

              if (publication.isSubscribed && publication.track) {
                console.log('🎥 [LIVEKIT] Existing video track found - attaching...');
                handleTrackSubscribed(publication.track, publication, participant);
              } else if (!publication.isSubscribed) {
                console.log('🔄 [LIVEKIT] Subscribing to existing video track...');
                publication.setSubscribed(true);
              }
            });
          }
        });
      } else {
        console.log('ℹ️ [LIVEKIT] No remote participants found yet');
      }

      // Step 3: Wait for video (Blue)
      setStatusDot('blue');
      setIsLoading(false);
      console.log('🔵 [LIVEKIT] Waiting for video stream...');

      // Set up timeout for video stream (longer timeout for video)
      setTimeout(() => {
        if (!isVideoVisible) {
          console.warn('⚠️ [LIVEKIT] Video stream timeout - no video received');
          setStatusDot('gray');
          setError('Video stream timeout');
        }
      }, STAGE_TIMEOUT * 2); // 60 seconds for video

    } catch (error) {
      console.error('❌ [LIVEKIT] Connection failed:', error);
      console.log('🔧 [LIVEKIT] Connection error details:', {
        errorType: error instanceof Error ? error.constructor.name : typeof error,
        errorMessage: error instanceof Error ? error.message : String(error)
      });

      setStatusDot('gray');
      setError(error instanceof Error ? error.message : String(error));
      setIsLoading(false);

      // Clean up room if it was created
      if (room) {
        console.log('🧹 [LIVEKIT] Cleaning up failed room connection...');
        room.disconnect();
        setRoom(null);
      }
    }
  };

  // LiveKit event handlers
  // Helper function to attach video with retry mechanism
  const attachVideoWithRetry = useCallback((track: any, maxRetries = 10, delay = 100) => {
    let attempts = 0;

    const tryAttach = () => {
      attempts++;
      console.log(`🔄 [LIVEKIT] Attempting to attach video (attempt ${attempts}/${maxRetries})`);

      if (videoRef.current) {
        console.log('🖥️ [LIVEKIT] Video element found - attaching...');

        // Detach any existing tracks first
        const existingTracks = videoRef.current.srcObject;
        if (existingTracks) {
          console.log('🧹 [LIVEKIT] Detaching existing video tracks');
          videoRef.current.srcObject = null;
        }

        track.attach(videoRef.current);
        console.log('✅ [LIVEKIT] Video attached successfully to element');

        // Ensure video plays
        videoRef.current.play().catch(error => {
          console.warn('⚠️ [LIVEKIT] Video play failed:', error);
        });

        return true; // Success
      } else if (attempts < maxRetries) {
        console.log(`⏳ [LIVEKIT] Video element not ready, retrying in ${delay}ms...`);
        setTimeout(tryAttach, delay);
        return false; // Will retry
      } else {
        console.error('❌ [LIVEKIT] Failed to attach video after maximum retries');
        return false; // Failed
      }
    };

    tryAttach();
  }, []);

  const handleTrackSubscribed = useCallback((track: any, publication: any, participant: any) => {
    console.log('📺 [LIVEKIT] Track subscribed:', {
      kind: track.kind,
      source: track.source,
      sid: track.sid,
      enabled: track.enabled,
      muted: track.muted,
      participantIdentity: participant?.identity,
      publicationSid: publication?.sid
    });

    if (track.kind === Track.Kind.Video) {
      console.log('🎥 [LIVEKIT] Video track received - processing...');
      setStatusDot('green');
      setIsLoading(false);
      setIsVideoVisible(true);

      // Reset video position and scale when new video is attached
      setScale(1.0);
      setVideoPosX(0);
      setVideoPosY(0);

      // Use retry mechanism to attach video
      attachVideoWithRetry(track);
    } else if (track.kind === Track.Kind.Audio) {
      console.log('🔊 [LIVEKIT] Audio track received (not processing for screen view)');
    } else {
      console.log('❓ [LIVEKIT] Unknown track type:', track.kind);
    }
  }, [attachVideoWithRetry]);

  const handleTrackUnsubscribed = useCallback((track: any, _publication: any, _participant: any) => {
    console.log('🎥 [LIVEKIT] Track unsubscribed:', track.kind);
    if (track.kind === Track.Kind.Video) {
      console.log('⚠️ [LIVEKIT] Video track lost');
      setIsVideoVisible(false);
      setStatusDot('blue'); // Back to waiting
      if (videoRef.current) {
        track.detach(videoRef.current);
      }
    }
  }, []);

  const handleDisconnected = useCallback(() => {
    console.log('❌ [LIVEKIT] Disconnected from room');
    setStatusDot('gray');
    setIsVideoVisible(false);
    setError('Disconnected from room');

    // REMOVED: No automatic reconnection - just log the disconnection
    console.log('ℹ️ [LIVEKIT] Connection ended - no automatic reconnection');
  }, []);

  const handleConnectionStateChanged = useCallback((state: any) => {
    console.log('🔄 [LIVEKIT] Connection state changed:', state);

    // Handle different connection states
    switch (state) {
      case 'connected':
        console.log('✅ [LIVEKIT] Connection established');
        break;
      case 'connecting':
        console.log('🔄 [LIVEKIT] Connecting...');
        setStatusDot('orange');
        break;
      case 'disconnected':
        console.log('❌ [LIVEKIT] Connection lost');
        setStatusDot('gray');
        break;
      // REMOVED: case 'reconnecting' - no automatic reconnection
    }
  }, []);

  const handleParticipantConnected = useCallback((participant: any) => {
    console.log('👤 [LIVEKIT] Participant connected:', participant.identity);

    // Check if this participant has video tracks
    if (participant.videoTrackPublications) {
      participant.videoTrackPublications.forEach((publication: any, trackSid: string) => {
        console.log(`📺 [LIVEKIT] New participant has video publication: ${trackSid}`, {
          subscribed: publication.isSubscribed,
          enabled: publication.isEnabled,
          muted: publication.isMuted,
          source: publication.source
        });

        if (!publication.isSubscribed) {
          console.log('🔄 [LIVEKIT] Subscribing to new participant video track...');
          publication.setSubscribed(true);
        }
      });
    } else {
      console.log('ℹ️ [LIVEKIT] New participant has no video tracks yet');
    }
  }, []);

  // Video zoom with wheel based on documentation
  const handleWheel = useCallback((e: React.WheelEvent) => {
    if (!isVideoVisible) return;

    e.preventDefault();

    let newScale: number;
    if (e.deltaY < 0) {
      // Zoom in (from documentation)
      newScale = scale + 0.1;
    } else {
      // Zoom out (from documentation)
      newScale = Math.max(1, scale - 0.1);
    }

    setScale(newScale);

    // Reset position when scale reaches 1 (from documentation)
    if (newScale === 1.0) {
      setVideoPosX(0);
      setVideoPosY(0);
      if (videoRef.current) {
        videoRef.current.style.cursor = 'default';
        videoRef.current.style.left = '0px';
        videoRef.current.style.top = '0px';
      }
    } else if (videoRef.current) {
      // Set cursor to grab when zoomed (from documentation)
      videoRef.current.style.cursor = 'grab';
    }

    console.log('🖱️ [WHEEL] Video scale:', { oldScale: scale, newScale });
  }, [scale, isVideoVisible]);

  // Video drag handlers based on documentation
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (scale <= 1.0 || !isVideoVisible) return;

    setIsDragging(true);
    setDragStart({ x: e.clientX - videoPosX, y: e.clientY - videoPosY });

    // Set cursor to grabbing (from documentation)
    if (videoRef.current) {
      videoRef.current.style.cursor = 'grabbing';
    }

    console.log('🖱️ [DRAG] Started dragging:', {
      clientX: e.clientX,
      clientY: e.clientY,
      videoPosX,
      videoPosY,
      scale
    });
  }, [scale, videoPosX, videoPosY, isVideoVisible]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDragging || scale <= 1.0 || !containerRef.current || !videoRef.current) return;

    const container = containerRef.current;
    const video = videoRef.current;

    // Calculate new position based on documentation formula
    let newPosX = e.clientX - dragStart.x;
    let newPosY = e.clientY - dragStart.y;

    // Calculate boundary constraints (from documentation)
    const maxPosX = (container.clientWidth - video.clientWidth * scale) / 2;
    const maxPosY = (container.clientHeight - video.clientHeight * scale) / 2;

    // Apply constraints to keep video within container bounds
    const constrainedX = Math.max(maxPosX, Math.min(-maxPosX, newPosX));
    const constrainedY = Math.max(maxPosY, Math.min(-maxPosY, newPosY));

    console.log('🖱️ [DRAG] Position calculation:', {
      newPosX,
      newPosY,
      maxPosX,
      maxPosY,
      constrainedX,
      constrainedY,
      scale,
      containerWidth: container.clientWidth,
      containerHeight: container.clientHeight,
      videoWidth: video.clientWidth,
      videoHeight: video.clientHeight
    });

    setVideoPosX(constrainedX);
    setVideoPosY(constrainedY);
  }, [isDragging, dragStart, scale]);

  const handleMouseUp = useCallback(() => {
    if (isDragging) {
      setIsDragging(false);
      // Set cursor back to grab (from documentation)
      if (videoRef.current) {
        videoRef.current.style.cursor = 'grab';
      }
      console.log('🖱️ [DRAG] Stopped dragging');
    }
  }, [isDragging]);

  // Keyboard shortcuts based on documentation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isVideoVisible) return;

      if (e.ctrlKey) {
        switch (e.key) {
          case '+':
          case '=':
            e.preventDefault();
            const newScaleUp = Math.min(3.0, scale + 0.1);
            setScale(newScaleUp);

            // Set cursor to grab when zooming in (from documentation)
            if (newScaleUp > 1 && videoRef.current) {
              videoRef.current.style.cursor = 'grab';
            }

            console.log('⌨️ [KEYBOARD] Zoom in:', { oldScale: scale, newScale: newScaleUp });
            break;

          case '-':
            e.preventDefault();
            const newScaleDown = Math.max(1.0, scale - 0.1);
            setScale(newScaleDown);

            // Reset position and cursor when scale reaches 1 (from documentation)
            if (newScaleDown === 1.0) {
              setVideoPosX(0);
              setVideoPosY(0);
              if (videoRef.current) {
                videoRef.current.style.cursor = 'default';
                videoRef.current.style.left = '0px';
                videoRef.current.style.top = '0px';
              }
            }

            console.log('⌨️ [KEYBOARD] Zoom out:', { oldScale: scale, newScale: newScaleDown });
            break;

          case '0':
            e.preventDefault();
            setScale(1.0);
            setVideoPosX(0);
            setVideoPosY(0);

            // Reset cursor and position (from documentation)
            if (videoRef.current) {
              videoRef.current.style.cursor = 'default';
              videoRef.current.style.left = '0px';
              videoRef.current.style.top = '0px';
            }

            console.log('⌨️ [KEYBOARD] Reset zoom');
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isVideoVisible, scale]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (room) {
        console.log('🧹 [LIVEKIT] Cleaning up room connection...');
        room.disconnect();
      }
    };
  }, [room]);

  // Extract window data from URL parameters
  const extractWindowData = useCallback(() => {
    try {
      const urlParams = new URLSearchParams(window.location.search);
      const employeeDataParam = urlParams.get('employee_data');

      if (employeeDataParam) {
        const decodedData = decodeURIComponent(employeeDataParam);
        const parsedData: ScreenViewWindowData = JSON.parse(decodedData);

        console.log('📊 [SCREEN_VIEW] Window data received for LiveKit connection');
        console.log('👤 [SCREEN_VIEW] Current User:', parsedData.currentUser.full_name);
        console.log('👥 [SCREEN_VIEW] Target Employee:', parsedData.selectedEmployee.full_name, 'ID:', parsedData.selectedEmployee.id);

        setCurrentUser(parsedData.currentUser);
        setSelectedEmployee(parsedData.selectedEmployee);
        return parsedData;
      } else {
        console.warn('⚠️ [SCREEN_VIEW] No window data found in URL parameters');
        return null;
      }
    } catch (error) {
      console.error('❌ [SCREEN_VIEW] Failed to parse window data:', error);
      return null;
    }
  }, []);

  // Initialize window and extract data
  useEffect(() => {
    const initWindow = async () => {
      try {
        console.log('🔍 [SCREEN_VIEW] Initializing LiveKit screen view...');
        setStatusDot('red');

        // COMMENTED OUT: Initialize Tauri invoke first (if in Tauri environment)
        // if (isTauriEnvironment) {
        //   const tauriInitialized = await initializeTauriInvoke();
        //   if (!tauriInitialized) {
        //     console.error('❌ [SCREEN_VIEW] Tauri not available in Tauri environment');
        //     setStatusDot('gray');
        //     setIsLoading(false);
        //     return;
        //   }
        // } else {
          console.log('ℹ️ [SCREEN_VIEW] Running in web environment - using web-based API calls');
        // }

        // COMMENTED OUT: Initialize window controls (only in Tauri environment)
        // try {
        //   const window = getCurrentWindow();
        //   setAppWindow(window);

        //   // Check if window methods are available (Tauri desktop environment)
        //   if (window && typeof window.isMaximized === 'function') {
        //     const maximized = await window.isMaximized();
        //     setIsMaximized(maximized);
        //     console.log('✅ [SCREEN_VIEW] Window controls initialized');
        //   } else {
        //     console.log('⚠️ [SCREEN_VIEW] Window controls not available (web environment)');
        //   }
        // } catch (windowError) {
        //   console.warn('⚠️ [SCREEN_VIEW] Window initialization failed:', windowError);
        //   // Continue without window controls in web environment
        // }

        // Extract window data and set state
        const winData = extractWindowData();

        if (!winData) {
          console.error('❌ [SCREEN_VIEW] No window data available');
          setStatusDot('gray');
          setIsLoading(false);
        }
        // Note: Don't call connectToLiveKit here - wait for state to be set
      } catch (error) {
        console.error('❌ [SCREEN_VIEW] Failed to initialize:', error);
        setStatusDot('gray');
        setIsLoading(false);
      }
    };

    initWindow();
  }, [extractWindowData]);

  // Start LiveKit connection when valid user data is available
  useEffect(() => {
    if (validateCurrentUser(currentUser) && validateSelectedEmployee(selectedEmployee)) {
      console.log('✅ [SCREEN_VIEW] Valid user data available, starting LiveKit connection...');
      connectToLiveKit();
    } else if (currentUser || selectedEmployee) {
      // We have some data but it's invalid
      console.error('❌ [SCREEN_VIEW] Invalid user data detected');
      setStatusDot('gray');
      setIsLoading(false);
    }
  }, [currentUser, selectedEmployee]);



  // Loading screen with animated spinner (no text)
  if (isLoading) {
    return (
      <div className="h-screen bg-black flex items-center justify-center">
        <div className="flex space-x-2" data-testid="loading-spinner">
          <div className="w-4 h-4 bg-blue-400 rounded-full animate-bounce shadow-lg shadow-blue-400/50"></div>
          <div className="w-4 h-4 bg-blue-400 rounded-full animate-bounce shadow-lg shadow-blue-400/50" style={{animationDelay: '0.1s'}}></div>
          <div className="w-4 h-4 bg-blue-400 rounded-full animate-bounce shadow-lg shadow-blue-400/50" style={{animationDelay: '0.2s'}}></div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-black text-white flex flex-col">
      {/* Top Status Bar (Window Controls commented out) */}
      <div
        className="absolute top-0 left-0 right-0 h-8 bg-gray-900 bg-opacity-80 flex items-center justify-between px-4 z-50"
        // onMouseDown={handleDragStart}
      >
        {/* Left: Status dot */}
        <div className={`w-2 h-2 rounded-full ${getStatusColor(statusDot)}`} data-testid="status-dot"></div>

        {/* Center: Info icon with tooltip */}
        <div className="relative group">
          <span className="text-white cursor-help">ⓘ</span>
          <div className="absolute right-0 top-6 bg-black text-white text-xs p-2 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            🔴 Initial connection<br/>
            🟡 Getting auth token<br/>
            🟠 Connecting to LiveKit room<br/>
            🔵 Waiting for video<br/>
            🟢 Connected with active video<br/>
            ⚫ Error or disconnected
          </div>
        </div>

        {/* COMMENTED OUT: Right: Window Controls (only in Tauri environment) */}
        {/* {appWindow && (
          <div className="flex items-center gap-1">
            <button
              onClick={handleMinimize}
              className="w-6 h-6 rounded hover:bg-custom-border/30 flex items-center justify-center transition-colors"
            >
              <Icon icon="lucide:minus" className="w-3 h-3" />
            </button>
            <button
              onClick={handleMaximize}
              className="w-6 h-6 rounded hover:bg-custom-border/30 flex items-center justify-center transition-colors"
            >
              <Icon icon={isMaximized ? "lucide:minimize-2" : "lucide:maximize-2"} className="w-3 h-3" />
            </button>
            <button
              onClick={handleClose}
              className="w-6 h-6 rounded hover:bg-red-500/20 hover:text-red-400 flex items-center justify-center transition-colors"
            >
              <Icon icon="lucide:x" className="w-3 h-3" />
            </button>
          </div>
        )} */}
      </div>

      {/* Main Video Container */}
      <div
        ref={containerRef}
        className="flex-1 bg-black relative overflow-hidden"
        onWheel={handleWheel}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        style={{ cursor: scale > 1.0 ? (isDragging ? 'grabbing' : 'grab') : 'default' }}
      >
        {/* Video Element - styled based on documentation */}
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted
          className="w-full h-full object-contain bg-black rounded-lg"
          style={{
            display: isVideoVisible ? 'flex' : 'none',
            transform: `scale(${scale})`,
            transformOrigin: 'center center',
            position: 'relative',
            left: `${videoPosX}px`,
            top: `${videoPosY}px`,
            outline: 'none',
            border: '2px solid #3497da',
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
            backgroundColor: '#000'
          }}
          onFocus={(e) => {
            (e.target as HTMLVideoElement).style.outline = '2px solid #3498db';
          }}
        />

        {/* Zoom Info Display */}
        {scale > 1 && (
          <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
            {Math.round(scale * 100)}% | {Math.round(videoPosX)}, {Math.round(videoPosY)}
          </div>
        )}

        {/* Loading State (when not showing video) */}
        {!isVideoVisible && !isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-gray-400">
              <div className="text-6xl mb-4">📺</div>
              <div className="text-lg mb-2">
                {statusDot === 'gray' ? 'Connection Error' : 'Waiting for Video'}
              </div>
              {selectedEmployee && (
                <div className="text-sm opacity-70">
                  {statusDot === 'gray'
                    ? 'Failed to connect to screen share'
                    : `Waiting for ${selectedEmployee.full_name}'s screen...`
                  }
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
