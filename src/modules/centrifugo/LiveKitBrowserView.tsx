// LiveKit Browser View - Route component for Linux systems
// Displays LiveKit screen share with TopBar

import React, { useState, useEffect, useRef } from 'react';
import * as LivekitClient from 'livekit-client';
import { Room, RemoteTrack, RemoteTrackPublication, RemoteParticipant } from 'livekit-client';
import { requestLiveKitTokenWeb, type LiveKitTokenResponse } from './LiveKitScreenShareService';
import { useSearchParams, useNavigate } from 'react-router-dom';

// Environment detection
const isTauriEnvironment = () => {
  return typeof window !== 'undefined' &&
         window.__TAURI_INTERNALS__ !== undefined;
};

// TopBar Component (simplified version)
const TopBar: React.FC<{ employeeName: string; status: string }> = ({ employeeName, status }) => {
  const navigate = useNavigate();

  return (
    <div className="bg-gray-900/80 backdrop-blur-sm border-b border-gray-700/50 px-4 py-2 flex items-center justify-between">
      <div className="flex items-center space-x-3">
        <div className="w-3 h-3 rounded-full bg-green-500"></div>
        <span className="text-white font-medium">TeamBy - Screen Share</span>
      </div>

      <div className="flex items-center space-x-4">
        <div className="text-sm text-gray-300">
          Viewing: <span className="text-white font-medium">{employeeName}</span>
        </div>
        <div className="text-sm text-gray-400">
          Status: <span className="text-green-400">{status}</span>
        </div>
      </div>

      <button
        onClick={() => {
          // In browser environment, try to go back or close tab
          if (window.history.length > 1) {
            window.history.back();
          } else {
            window.close();
          }
        }}
        className="text-gray-400 hover:text-white transition-colors px-2 py-1 rounded"
        title="Close"
      >
        ✕
      </button>
    </div>
  );
};

// Main LiveKit Browser View Component
const LiveKitBrowserView: React.FC = () => {
  const [status, setStatus] = useState<string>('Initializing...');
  const [room, setRoom] = useState<Room | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [employeeData, setEmployeeData] = useState<{
    employeeId: number;
    employeeName: string;
    currentUserName: string;
  } | null>(null);
  
  const videoRef = useRef<HTMLVideoElement>(null);

  // Get URL parameters using React Router
  const [searchParams] = useSearchParams();

  useEffect(() => {
    console.log('🔍 [LIVEKIT-BROWSER] Parsing URL parameters...');
    const employeeId = searchParams.get('employeeId');
    const employeeName = searchParams.get('employeeName');
    const currentUserName = searchParams.get('currentUserName');

    console.log('📋 [LIVEKIT-BROWSER] URL Parameters:', {
      employeeId,
      employeeName,
      currentUserName,
      fullURL: window.location.href
    });

    if (employeeId && employeeName && currentUserName) {
      const parsedData = {
        employeeId: parseInt(employeeId),
        employeeName: decodeURIComponent(employeeName),
        currentUserName: decodeURIComponent(currentUserName)
      };

      console.log('✅ [LIVEKIT-BROWSER] Employee data parsed successfully:', parsedData);
      setEmployeeData(parsedData);
    } else {
      console.error('❌ [LIVEKIT-BROWSER] Missing required parameters');
      setError('Missing required parameters');
    }
  }, [searchParams]);

  // Screen sharing function for browser environment
  const startScreenShare = async (currentRoom: Room) => {
    try {
      console.log('🖥️ [LIVEKIT-BROWSER] Starting screen share...');

      // Get screen stream using standard web API
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          width: 1280,
          height: 720,
          frameRate: 30,
        },
        audio: false,
      });

      const videoTrack = stream.getVideoTracks()[0];
      if (!videoTrack) {
        throw new Error('No video track found in screen stream');
      }

      // Publish screen share to room if other participants exist
      if (currentRoom && currentRoom.localParticipant) {
        const localTrackPublication = await currentRoom.localParticipant.publishTrack(videoTrack, {
          name: "screen_share",
          source: LivekitClient.Track.Source.ScreenShare,
        });

        console.log("✅ [LIVEKIT-BROWSER] Screen sharing started and published to the room");
        setStatus('Screen sharing active');
      }

    } catch (error) {
      console.error('❌ [LIVEKIT-BROWSER] Failed to start screen share:', error);
      setStatus(`Screen share failed: ${error}`);
    }
  };

  // Connect to LiveKit
  useEffect(() => {
    if (!employeeData) return;

    const connectToLiveKit = async () => {
      try {
        setStatus('Requesting token...');
        console.log(`🎫 [LIVEKIT-BROWSER] Requesting token for employee ${employeeData.employeeId} (${employeeData.employeeName})`);

        // Request LiveKit token via web
        const roomName = `${employeeData.employeeId}:teamby`;
        const token = await requestLiveKitTokenWeb(
          employeeData.currentUserName,
          employeeData.employeeId.toString(),
          roomName,
          'screen_view'
        );

        setStatus('Connecting to LiveKit...');
        console.log(`🔗 [LIVEKIT-BROWSER] Connecting to room: ${roomName}`);

        // Set LiveKit log level for debugging
        LivekitClient.setLogLevel(LivekitClient.LogLevel.debug);

        // Create and configure room with unified settings
        const newRoom = new LivekitClient.Room({
          audioCaptureDefaults: {
            autoGainControl: true,
            deviceId: "",
            echoCancellation: true,
            noiseSuppression: true,
          },
          videoCaptureDefaults: {
            deviceId: "",
            facingMode: "user",
            resolution: {
              width: 1280,
              height: 720,
              frameRate: 30,
            },
          },
          publishDefaults: {
            videoEncoding: {
              maxBitrate: 1_500_000,
              maxFramerate: 30,
            },
            screenShareEncoding: {
              maxBitrate: 1_500_000,
              maxFramerate: 30,
            },
          },
        });

        // Setup event handlers
        newRoom.on('connected', async () => {
          console.log(`✅ [LIVEKIT-BROWSER] Connected to LiveKit room: ${roomName}`);
          setStatus('Connected - Starting screen share...');
          setIsConnected(true);

          // Start screen sharing immediately after connection
          await startScreenShare(newRoom);
        });

        newRoom.on('disconnected', () => {
          console.log(`❌ [LIVEKIT-BROWSER] Disconnected from LiveKit room: ${roomName}`);
          setStatus('Disconnected');
          setIsConnected(false);
        });

        newRoom.on('connectionStateChanged', (state) => {
          console.log(`🔄 [LIVEKIT-BROWSER] Connection state changed: ${state}`);
          setStatus(`Connection: ${state}`);
        });

        // Participant event handlers
        newRoom.on('participantConnected', (participant: RemoteParticipant) => {
          console.log(`👤 [LIVEKIT-BROWSER] Participant connected: ${participant.identity}`);
          setStatus(`Participant joined: ${participant.identity}`);
        });

        newRoom.on('participantDisconnected', async (participant: RemoteParticipant) => {
          console.log(`👤 [LIVEKIT-BROWSER] Participant disconnected: ${participant.identity}`);
          setStatus(`Participant left: ${participant.identity}`);

          // Disconnect room and close browser tab
          try {
            console.log('🔌 [LIVEKIT-BROWSER] Disconnecting room due to participant leaving...');
            await newRoom.disconnect();

            console.log('🌐 [LIVEKIT-BROWSER] Closing browser tab...');
            window.close();
          } catch (error) {
            console.error('❌ [LIVEKIT-BROWSER] Error during cleanup:', error);
          }
        });

        // Handle track subscriptions
        newRoom.on('trackSubscribed', (track: RemoteTrack, publication: RemoteTrackPublication, participant: RemoteParticipant) => {
          console.log(`📺 [LIVEKIT-BROWSER] Track subscribed:`, track.kind);
          
          if (track.kind === 'video' && videoRef.current) {
            track.attach(videoRef.current);
            console.log(`✅ [LIVEKIT-BROWSER] Video track attached to element`);
          }
        });

        newRoom.on('trackUnsubscribed', (track: RemoteTrack) => {
          console.log(`📺 [LIVEKIT-BROWSER] Track unsubscribed:`, track.kind);
          track.detach();
        });

        // Connect to room
        await newRoom.connect('wss://livekit.newhorizonco.uk', token);
        setRoom(newRoom);

        console.log(`🎉 [LIVEKIT-BROWSER] Successfully connected to LiveKit for employee ${employeeData.employeeName} (ID: ${employeeData.employeeId})`);

        // Print connection success message in Persian
        console.log(`✅ کانکت شد! متصل به صفحه نمایش ${employeeData.employeeName}`);

      } catch (error) {
        console.error(`❌ [LIVEKIT-BROWSER] Failed to connect to LiveKit:`, error);
        setStatus(`Connection failed: ${error}`);
        setError(error instanceof Error ? error.message : 'Unknown error');
      }
    };

    connectToLiveKit();

    // Cleanup on unmount
    return () => {
      if (room) {
        room.disconnect();
        console.log(`🗑️ [LIVEKIT-BROWSER] Cleaned up LiveKit connection for employee ${employeeData.employeeId}`);
      }
    };
  }, [employeeData]);

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 text-white">
        <TopBar employeeName="Error" status="Failed" />
        <div className="flex items-center justify-center h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-400 mb-4">Connection Error</h1>
            <p className="text-gray-300">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!employeeData) {
    return (
      <div className="min-h-screen bg-gray-900 text-white">
        <TopBar employeeName="Loading..." status="Initializing" />
        <div className="flex items-center justify-center h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p className="text-gray-300">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <TopBar employeeName={employeeData?.employeeName || 'Unknown'} status={status} />

      <div className="flex-1 flex items-center justify-center p-4">
        {!isConnected ? (
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p className="text-gray-300">{status}</p>
          </div>
        ) : (
          <div className="w-full h-full max-w-6xl max-h-[80vh]">
            <video
              ref={videoRef}
              autoPlay
              playsInline
              className="w-full h-full object-contain bg-black rounded-lg"
            />
          </div>
        )}
      </div>

      {isConnected && employeeData && (
        <div className="absolute bottom-4 left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm">
          ✅ Connected to {employeeData.employeeName}'s screen
        </div>
      )}
    </div>
  );
};

export default LiveKitBrowserView;
