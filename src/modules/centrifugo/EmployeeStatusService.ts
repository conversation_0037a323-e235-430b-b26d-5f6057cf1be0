// Employee Status Service for Centrifugo Module
// Handles employee_online and employee_offline events

import { invoke } from '@tauri-apps/api/core';

export interface CentrifugoEmployeeMessage {
  employee_id: number;
  status: 'online' | 'offline';
  timestamp: number;
  type: 'employee_online' | 'employee_offline';
}

export interface Employee {
  id: number;
  full_name: string;
  position_name: string;
  avatar?: string;
  isonline: boolean;
  screan_active?: boolean;
}

export class EmployeeStatusService {
  private employeeListUpdater: ((updater: (employees: Employee[]) => Employee[]) => void) | null = null;

  // Initialize service with employee list updater
  init(employeeListUpdater: (updater: (employees: Employee[]) => Employee[]) => void): void {
    this.employeeListUpdater = employeeListUpdater;
    console.log('✅ [EMPLOYEE-STATUS-SERVICE] Service initialized');
  }

  // Process employee status message
  processMessage(message: CentrifugoEmployeeMessage): void {
    console.log('👤 [EMPLOYEE-STATUS-SERVICE] Processing message:', message);

    // Debug log to Tauri terminal
    invoke('debug_log', {
      message: `Processing message: ${JSON.stringify(message)}`,
      tag: 'EMPLOYEE-STATUS-SERVICE'
    });

    if (!this.employeeListUpdater) {
      console.error('❌ [EMPLOYEE-STATUS-SERVICE] No employee list updater available');
      invoke('debug_log', {
        message: 'No employee list updater available',
        tag: 'EMPLOYEE-STATUS-SERVICE'
      });
      return;
    }

    const { employee_id, status, type } = message;

    if (!employee_id) {
      console.error('❌ [EMPLOYEE-STATUS-SERVICE] No employee_id in message');
      invoke('debug_log', {
        message: 'No employee_id in message',
        tag: 'EMPLOYEE-STATUS-SERVICE'
      });
      return;
    }

    console.log(`👤 [EMPLOYEE-STATUS-SERVICE] Processing ${type} for employee ID: ${employee_id}`);
    invoke('debug_log', {
      message: `Processing ${type} for employee ID: ${employee_id}`,
      tag: 'EMPLOYEE-STATUS-SERVICE'
    });

    if (type === 'employee_online') {
      this.handleEmployeeOnline(employee_id);
    } else if (type === 'employee_offline') {
      this.handleEmployeeOffline(employee_id);
    } else {
      console.warn(`⚠️ [EMPLOYEE-STATUS-SERVICE] Unknown message type: ${type}`);
      invoke('debug_log', {
        message: `Unknown message type: ${type}`,
        tag: 'EMPLOYEE-STATUS-SERVICE'
      });
    }
  }

  // Handle employee online
  private handleEmployeeOnline(employeeId: number): void {
    console.log(`🟢 [EMPLOYEE-STATUS-SERVICE] Setting employee ${employeeId} to ONLINE`);

    this.employeeListUpdater!((employees: Employee[]) => {
      const employeeIndex = employees.findIndex(emp => emp.id === employeeId);
      
      if (employeeIndex === -1) {
        console.log(`⚠️ [EMPLOYEE-STATUS-SERVICE] Employee ${employeeId} not found in list`);
        return employees;
      }

      const updatedEmployees = [...employees];
      const employee = { ...updatedEmployees[employeeIndex] };
      
      // Set employee to online
      employee.isonline = true;
      
      // Remove from current position
      updatedEmployees.splice(employeeIndex, 1);
      
      // Add to top of list (online employees first)
      updatedEmployees.unshift(employee);
      
      console.log(`✅ [EMPLOYEE-STATUS-SERVICE] Employee ${employee.full_name} set online and moved to top`);

      // Debug log to Tauri terminal
      invoke('debug_log', {
        message: `Employee ${employee.full_name} (ID: ${employeeId}) set online and moved to top`,
        tag: 'EMPLOYEE-STATUS-SERVICE'
      });

      return updatedEmployees;
    });
  }

  // Handle employee offline
  private handleEmployeeOffline(employeeId: number): void {
    console.log(`🔴 [EMPLOYEE-STATUS-SERVICE] Setting employee ${employeeId} to OFFLINE`);

    this.employeeListUpdater!((employees: Employee[]) => {
      const employeeIndex = employees.findIndex(emp => emp.id === employeeId);
      
      if (employeeIndex === -1) {
        console.log(`⚠️ [EMPLOYEE-STATUS-SERVICE] Employee ${employeeId} not found in list`);
        return employees;
      }

      const updatedEmployees = [...employees];
      const employee = { ...updatedEmployees[employeeIndex] };
      
      // Set employee to offline
      employee.isonline = false;
      
      // Remove from current position
      updatedEmployees.splice(employeeIndex, 1);
      
      // Find position after last online employee
      const lastOnlineIndex = updatedEmployees.findLastIndex(emp => emp.isonline === true);
      const insertPosition = lastOnlineIndex + 1;
      
      // Insert after last online employee
      updatedEmployees.splice(insertPosition, 0, employee);
      
      console.log(`✅ [EMPLOYEE-STATUS-SERVICE] Employee ${employee.full_name} set offline and moved to position ${insertPosition}`);

      // Debug log to Tauri terminal
      invoke('debug_log', {
        message: `Employee ${employee.full_name} (ID: ${employeeId}) set offline and moved to position ${insertPosition}`,
        tag: 'EMPLOYEE-STATUS-SERVICE'
      });

      return updatedEmployees;
    });
  }

  // Cleanup service
  cleanup(): void {
    this.employeeListUpdater = null;
    console.log('🗑️ [EMPLOYEE-STATUS-SERVICE] Service cleaned up');
  }
}

// Singleton instance
export const employeeStatusService = new EmployeeStatusService();
