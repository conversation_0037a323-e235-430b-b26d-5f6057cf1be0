// LiveKit Screen Share Service for Centrifugo Module
// Handles livekit_share_ascreen_view events

import React from 'react';
import ReactDOM from 'react-dom/client';
import { invoke } from '@tauri-apps/api/core';
import * as LivekitClient from 'livekit-client';
import { Room, Track, RemoteTrack, RemoteTrackPublication, RemoteParticipant } from 'livekit-client';

export interface LiveKitScreenShareMessage {
  employee_id: number;
  employee_name: string;
  share_type: string;
  timestamp: number;
  type: 'livekit_share_ascreen_view';
}

export interface LiveKitTokenResponse {
  token: string;
}

// Web-based HTTP request function (fallback for non-Tauri environments)
export const requestLiveKitTokenWeb = async (
  name: string,
  userId: string,
  roomName: string,
  eventType: string
): Promise<string> => {
  console.log('🌐 [LIVEKIT-SHARE] Making web-based token request');
  console.log('🔧 [LIVEKIT-SHARE] Web request params:', { name, userId, roomName, eventType });

  const response = await fetch('https://qa.habibapp.com/meet/livekit-apis/public-token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      name,
      user_id: userId,
      room_name: roomName,
      event_type: eventType,
    }),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json() as LiveKitTokenResponse;
  console.log('✅ [LIVEKIT-SHARE] Web token received successfully');
  return data.token;
};

// React Component for LiveKit Connection
const LiveKitScreenShareComponent: React.FC<{
  employeeId: number;
  employeeName: string;
}> = ({ employeeId, employeeName }) => {
  const [status, setStatus] = React.useState<string>('Initializing...');
  const [room, setRoom] = React.useState<Room | null>(null);

  // Screen sharing function for Tauri environment
  const startScreenShare = async (currentRoom: Room) => {
    try {
      console.log('🖥️ [LIVEKIT-SHARE] Starting screen share...');

      // Get desktop sources via Tauri
      const sources = await invoke<any[]>('get-sources');
      if (!sources || sources.length === 0) {
        throw new Error('No desktop sources available');
      }

      // Use first desktop source by default
      const defaultSource = sources[0];
      console.log('🖥️ [LIVEKIT-SHARE] Using desktop source:', defaultSource.id);

      // Get screen stream using Tauri-specific method
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: false,
        video: {
          mandatory: {
            chromeMediaSource: "desktop",
            chromeMediaSourceId: defaultSource.id,
          },
        } as any,
      });

      const videoTrack = stream.getVideoTracks()[0];
      if (!videoTrack) {
        throw new Error('No video track found in screen stream');
      }

      // Publish screen share to room if other participants exist
      if (currentRoom && currentRoom.localParticipant) {
        const localTrackPublication = await currentRoom.localParticipant.publishTrack(videoTrack, {
          name: "screen_share",
          source: LivekitClient.Track.Source.ScreenShare,
        });

        console.log("✅ [LIVEKIT-SHARE] Screen sharing started and published to the room");
        setStatus('Screen sharing active');
      }

    } catch (error) {
      console.error('❌ [LIVEKIT-SHARE] Failed to start screen share:', error);
      setStatus(`Screen share failed: ${error}`);
    }
  };

  React.useEffect(() => {
    const connectToLiveKit = async () => {
      try {
        setStatus('Getting current user...');
        console.log(`🎫 [LIVEKIT-SHARE] Requesting token for employee ${employeeId} (${employeeName})`);

        // Get current user profile from Tauri store via command
        const currentUserProfile = await invoke<any>('get_cached_user_profile');

        if (!currentUserProfile || !currentUserProfile.email) {
          throw new Error('Current user profile not found');
        }

        setStatus('Requesting token...');
        console.log(`👤 [LIVEKIT-SHARE] Current user: ${currentUserProfile.full_name} (${currentUserProfile.email})`);

        // Request LiveKit token via Tauri (matching ScreenViewDesktop parameters)
        const roomName = `${employeeId}:teamby`;

        // Debug: Log exact parameters being sent
        const tokenParams = {
          name: currentUserProfile.full_name,
          user_id: employeeId.toString(), // Use target employee ID as string with correct key
          room_name: roomName,
          event_type: 'screen_view'
        };

        console.log(`🔧 [LIVEKIT-SHARE] DEBUG - Sending token request with params:`, tokenParams);
        console.log(`🔧 [LIVEKIT-SHARE] DEBUG - employeeId: ${employeeId}, toString(): ${employeeId.toString()}`);

        let token: string;

        try {
          // Try Tauri first
          const response = await invoke<any>('request_livekit_token', {
            name: tokenParams.name,
            userId: tokenParams.user_id,
            roomName: tokenParams.room_name,
            eventType: tokenParams.event_type
          });
          token = response.token;
          console.log('✅ [LIVEKIT-SHARE] Token received via Tauri');
        } catch (tauriError) {
          console.warn('⚠️ [LIVEKIT-SHARE] Tauri token request failed, falling back to web:', tauriError);
          // Fallback to web request
          token = await requestLiveKitTokenWeb(
            tokenParams.name,
            tokenParams.user_id,
            tokenParams.room_name,
            tokenParams.event_type
          );
          console.log('✅ [LIVEKIT-SHARE] Token received via web fallback');
        }

        setStatus('Connecting to LiveKit...');
        console.log(`🔗 [LIVEKIT-SHARE] Connecting to room: ${roomName}`);

        // Set LiveKit log level for debugging
        LivekitClient.setLogLevel(LivekitClient.LogLevel.debug);

        // Create and configure room with unified settings
        const newRoom = new LivekitClient.Room({
          audioCaptureDefaults: {
            autoGainControl: true,
            deviceId: "",
            echoCancellation: true,
            noiseSuppression: true,
          },
          videoCaptureDefaults: {
            deviceId: "",
            facingMode: "user",
            resolution: {
              width: 1280,
              height: 720,
              frameRate: 30,
            },
          },
          publishDefaults: {
            videoEncoding: {
              maxBitrate: 1_500_000,
              maxFramerate: 30,
            },
            screenShareEncoding: {
              maxBitrate: 1_500_000,
              maxFramerate: 30,
            },
          },
        });

        // Setup event handlers
        newRoom.on('connected', async () => {
          console.log(`✅ [LIVEKIT-SHARE] Connected to LiveKit room: ${roomName}`);
          setStatus('Connected to LiveKit - Starting screen share...');

          // Start screen sharing immediately after connection
          await startScreenShare(newRoom);
        });

        newRoom.on('disconnected', () => {
          console.log(`❌ [LIVEKIT-SHARE] Disconnected from LiveKit room: ${roomName}`);
          setStatus('Disconnected from LiveKit');
        });

        newRoom.on('connectionStateChanged', (state) => {
          console.log(`🔄 [LIVEKIT-SHARE] Connection state changed: ${state}`);
          setStatus(`Connection state: ${state}`);
        });

        // Participant event handlers
        newRoom.on('participantConnected', (participant: RemoteParticipant) => {
          console.log(`👤 [LIVEKIT-SHARE] Participant connected: ${participant.identity}`);
          setStatus(`Participant joined: ${participant.identity}`);
        });

        newRoom.on('participantDisconnected', async (participant: RemoteParticipant) => {
          console.log(`👤 [LIVEKIT-SHARE] Participant disconnected: ${participant.identity}`);
          setStatus(`Participant left: ${participant.identity}`);

          // Disconnect room and close screen view window
          try {
            console.log('🔌 [LIVEKIT-SHARE] Disconnecting room due to participant leaving...');
            await newRoom.disconnect();

            console.log('🗑️ [LIVEKIT-SHARE] Closing screen view window...');
            await invoke('close_screen_view_window');
          } catch (error) {
            console.error('❌ [LIVEKIT-SHARE] Error during cleanup:', error);
          }
        });

        // Connect to room
        await newRoom.connect('wss://livekit.newhorizonco.uk', token);
        setRoom(newRoom);

        console.log(`🎉 [LIVEKIT-SHARE] Successfully connected to LiveKit for employee ${employeeName} (ID: ${employeeId})`);

      } catch (error) {
        console.error(`❌ [LIVEKIT-SHARE] Failed to connect to LiveKit:`, error);
        setStatus(`Connection failed: ${error}`);
      }
    };

    connectToLiveKit();

    // Cleanup on unmount
    return () => {
      if (room) {
        room.disconnect();
        console.log(`🗑️ [LIVEKIT-SHARE] Cleaned up LiveKit connection for employee ${employeeId}`);
      }
    };
  }, [employeeId, employeeName]);

  return React.createElement('div', {
    style: { display: 'none' } // Hidden component - no UI needed
  }, `LiveKit Screen Share - ${employeeName} (${employeeId}) - Status: ${status}`);
};

export class LiveKitScreenShareService {
  private activeConnections = new Map<number, { root: any; container: HTMLDivElement }>();

  // Process LiveKit screen share message
  processMessage(message: LiveKitScreenShareMessage): void {
    console.log('📺 [LIVEKIT-SHARE-SERVICE] Processing message:', message);

    // Debug log to Tauri terminal
    invoke('debug_log', {
      message: `Processing LiveKit screen share message: ${JSON.stringify(message)}`,
      tag: 'LIVEKIT-SHARE-SERVICE'
    });

    const { employee_id, employee_name } = message;

    // Check if already connected to this employee
    if (this.activeConnections.has(employee_id)) {
      console.log(`⚠️ [LIVEKIT-SHARE-SERVICE] Already connected to employee ${employee_id} (${employee_name})`);
      return;
    }

    try {
      // Create hidden container for React component
      const container = document.createElement('div');
      container.id = `livekit-share-${employee_id}`;
      container.style.display = 'none';
      document.body.appendChild(container);

      // Create React root and render component
      const root = ReactDOM.createRoot(container);
      root.render(React.createElement(LiveKitScreenShareComponent, {
        employeeId: employee_id,
        employeeName: employee_name
      }));

      // Store connection reference
      this.activeConnections.set(employee_id, { root, container });

      console.log(`✅ [LIVEKIT-SHARE-SERVICE] Started LiveKit connection for employee ${employee_name} (ID: ${employee_id})`);

      // Debug log to Tauri terminal
      invoke('debug_log', {
        message: `Started LiveKit connection for employee ${employee_name} (ID: ${employee_id})`,
        tag: 'LIVEKIT-SHARE-SERVICE'
      });

    } catch (error) {
      console.error(`❌ [LIVEKIT-SHARE-SERVICE] Failed to start LiveKit connection:`, error);
      
      // Debug log to Tauri terminal
      invoke('debug_log', {
        message: `Failed to start LiveKit connection: ${error}`,
        tag: 'LIVEKIT-SHARE-SERVICE'
      });
    }
  }

  // Disconnect from specific employee
  disconnectEmployee(employeeId: number): void {
    const connection = this.activeConnections.get(employeeId);
    if (connection) {
      connection.root.unmount();
      document.body.removeChild(connection.container);
      this.activeConnections.delete(employeeId);
      
      console.log(`🗑️ [LIVEKIT-SHARE-SERVICE] Disconnected from employee ${employeeId}`);
      
      // Debug log to Tauri terminal
      invoke('debug_log', {
        message: `Disconnected from employee ${employeeId}`,
        tag: 'LIVEKIT-SHARE-SERVICE'
      });
    }
  }

  // Cleanup all connections
  cleanup(): void {
    for (const [, connection] of this.activeConnections) {
      connection.root.unmount();
      document.body.removeChild(connection.container);
    }
    this.activeConnections.clear();
    
    console.log('🗑️ [LIVEKIT-SHARE-SERVICE] All connections cleaned up');
    
    // Debug log to Tauri terminal
    invoke('debug_log', {
      message: 'All LiveKit screen share connections cleaned up',
      tag: 'LIVEKIT-SHARE-SERVICE'
    });
  }

  // Get active connections count
  getActiveConnectionsCount(): number {
    return this.activeConnections.size;
  }

  // Get list of connected employee IDs
  getConnectedEmployeeIds(): number[] {
    return Array.from(this.activeConnections.keys());
  }
}

// Singleton instance
export const liveKitScreenShareService = new LiveKitScreenShareService();
