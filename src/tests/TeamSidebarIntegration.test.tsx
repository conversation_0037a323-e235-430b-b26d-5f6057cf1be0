import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { TeamSidebar } from '../components/team-sidebar';

describe('TeamSidebar Integration', () => {
  const mockOnClose = vi.fn();

  beforeEach(() => {
    mockOnClose.mockClear();
  });

  it('should render with consistent sidebar width when open', () => {
    render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);

    const sidebar = screen.getByTestId('team-sidebar');
    expect(sidebar).toHaveClass('w-[300px]');
  });

  it('should have proper CSS classes for sidebar functionality', () => {
    render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);

    const sidebar = screen.getByTestId('team-sidebar');
    const classes = sidebar.className;

    // Check that it has the fixed width class
    expect(classes).toContain('w-[300px]');

    // Check positioning classes
    expect(classes).toContain('absolute');
    expect(classes).toContain('top-0');
    expect(classes).toContain('right-0');
    expect(classes).toContain('h-full');

    // Check z-index for proper layering
    expect(classes).toContain('z-10');

    // Check transition classes
    expect(classes).toContain('transition-all');
    expect(classes).toContain('duration-300');
  });

  it('should maintain sidebar structure regardless of content', () => {
    render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);

    const sidebar = screen.getByTestId('team-sidebar');

    // The sidebar should have absolute positioning and sizing
    expect(sidebar).toHaveClass('absolute');
    expect(sidebar).toHaveClass('flex');
    expect(sidebar).toHaveClass('flex-col');
    expect(sidebar).toHaveClass('w-[300px]');
    expect(sidebar).toHaveClass('h-full');
  });

  it('should handle animation states properly', async () => {
    const { rerender } = render(<TeamSidebar isOpen={false} onClose={mockOnClose} />);

    // Initially closed - sidebar should have w-0
    const sidebar = screen.getByTestId('team-sidebar');
    expect(sidebar).toHaveClass('w-0');

    // Open the sidebar
    rerender(<TeamSidebar isOpen={true} onClose={mockOnClose} />);

    // Sidebar should have full width
    await waitFor(() => {
      expect(screen.getByTestId('team-sidebar')).toHaveClass('w-[300px]');
    });
  });

  it('should handle sidebar visibility correctly', () => {
    const { rerender } = render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);

    // Sidebar should be visible when open
    const sidebar = screen.getByTestId('team-sidebar');
    expect(sidebar).toHaveClass('w-[300px]');

    // Sidebar should be hidden when closed
    rerender(<TeamSidebar isOpen={false} onClose={mockOnClose} />);
    expect(sidebar).toHaveClass('w-0');
  });

  it('should be optimized for desktop layout', () => {
    render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);

    const sidebar = screen.getByTestId('team-sidebar');

    // Width should be appropriate for desktop (300px)
    expect(sidebar).toHaveClass('w-[300px]');

    // Should be positioned absolutely
    expect(sidebar).toHaveClass('absolute');
    expect(sidebar).toHaveClass('top-0');
    expect(sidebar).toHaveClass('right-0');
  });
});
