import { describe, it, expect } from 'vitest';

describe('Chat Main Content CSS Fix', () => {
  it('should not have margin-left in chat-main-content class', () => {
    // Create a test element with chat-main-content class
    const testElement = document.createElement('div');
    testElement.className = 'chat-main-content';
    document.body.appendChild(testElement);
    
    // Get computed styles
    const computedStyle = window.getComputedStyle(testElement);
    
    // Check that margin-left is not set to 332px (the problematic value)
    expect(computedStyle.marginLeft).not.toBe('332px');
    
    // Clean up
    document.body.removeChild(testElement);
  });

  it('should have transition property for smooth animations', () => {
    const testElement = document.createElement('div');
    testElement.className = 'chat-main-content';
    document.body.appendChild(testElement);
    
    const computedStyle = window.getComputedStyle(testElement);
    
    // Should have transition for smooth animations
    expect(computedStyle.transition).toContain('all');
    expect(computedStyle.transition).toContain('0.3s');
    expect(computedStyle.transition).toContain('ease');
    
    document.body.removeChild(testElement);
  });
});
