import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TeamSidebar } from '../components/team-sidebar';

describe('TeamSidebar Drawer Functionality', () => {
  const mockOnClose = vi.fn();

  beforeEach(() => {
    mockOnClose.mockClear();
  });

  describe('Animation and Transitions', () => {
    it('should have proper animation classes for drawer', () => {
      render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      const drawer = screen.getByTestId('team-sidebar-drawer');
      
      // Check for motion.aside element (Framer Motion component)
      expect(drawer).toBeInTheDocument();
      expect(drawer.tagName.toLowerCase()).toBe('aside');
    });

    it('should have proper backdrop animation classes', () => {
      render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      const backdrop = screen.getByTestId('team-sidebar-backdrop');
      
      // Check for motion.div element (Framer Motion component)
      expect(backdrop).toBeInTheDocument();
      expect(backdrop.tagName.toLowerCase()).toBe('div');
    });

    it('should handle open/close state transitions', async () => {
      const { rerender } = render(<TeamSidebar isOpen={false} onClose={mockOnClose} />);
      
      // Initially closed
      expect(screen.queryByTestId('team-sidebar-drawer')).not.toBeInTheDocument();
      expect(screen.queryByTestId('team-sidebar-backdrop')).not.toBeInTheDocument();
      
      // Open the drawer
      rerender(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      // Should appear with animation
      await waitFor(() => {
        expect(screen.getByTestId('team-sidebar-drawer')).toBeInTheDocument();
        expect(screen.getByTestId('team-sidebar-backdrop')).toBeInTheDocument();
      });
    });
  });

  describe('Desktop Optimization', () => {
    it('should have desktop-optimized dimensions', () => {
      render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      const drawer = screen.getByTestId('team-sidebar-drawer');
      
      // Width optimized for 1200px desktop (380px = ~32%)
      expect(drawer).toHaveClass('w-[380px]');
      expect(drawer).toHaveClass('max-w-[32%]');
    });

    it('should be positioned correctly for desktop layout', () => {
      render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      const drawer = screen.getByTestId('team-sidebar-drawer');
      
      // Fixed positioning below TopBar
      expect(drawer).toHaveClass('fixed');
      expect(drawer).toHaveClass('top-[60px]'); // Below 60px TopBar
      expect(drawer).toHaveClass('right-0');
      expect(drawer).toHaveClass('bottom-0');
    });

    it('should have proper z-index layering', () => {
      render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      const drawer = screen.getByTestId('team-sidebar-drawer');
      const backdrop = screen.getByTestId('team-sidebar-backdrop');
      
      // Drawer should be above backdrop
      expect(drawer).toHaveClass('z-50');
      expect(backdrop).toHaveClass('z-40');
    });

    it('should have shadow for depth perception', () => {
      render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      const drawer = screen.getByTestId('team-sidebar-drawer');
      expect(drawer).toHaveClass('shadow-2xl');
    });
  });

  describe('User Interactions', () => {
    it('should close on backdrop click', () => {
      render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      const backdrop = screen.getByTestId('team-sidebar-backdrop');
      fireEvent.click(backdrop);
      
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should close on Escape key press', () => {
      render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      fireEvent.keyDown(document, { key: 'Escape' });
      
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should not close on other key presses', () => {
      render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      fireEvent.keyDown(document, { key: 'Enter' });
      fireEvent.keyDown(document, { key: 'Space' });
      fireEvent.keyDown(document, { key: 'Tab' });
      
      expect(mockOnClose).not.toHaveBeenCalled();
    });

    it('should not close when clicking inside drawer', () => {
      render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      const drawer = screen.getByTestId('team-sidebar-drawer');
      fireEvent.click(drawer);
      
      expect(mockOnClose).not.toHaveBeenCalled();
    });

    it('should handle team member interactions', () => {
      render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      const teamMember = screen.getByText('Mirhafez');
      expect(teamMember).toBeInTheDocument();
      
      // Should be clickable
      fireEvent.click(teamMember);
      
      // Should not close drawer when clicking team member
      expect(mockOnClose).not.toHaveBeenCalled();
    });
  });

  describe('Content and Functionality', () => {
    it('should render search functionality', () => {
      render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      const searchInput = screen.getByPlaceholderText('Search');
      expect(searchInput).toBeInTheDocument();
      
      // Should be functional
      fireEvent.change(searchInput, { target: { value: 'test search' } });
      expect(searchInput).toHaveValue('test search');
    });

    it('should render team members with proper styling', () => {
      render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      // Check for team member elements
      const teamMember = screen.getByText('Mirhafez');
      expect(teamMember).toBeInTheDocument();
      
      // Should have role information
      const roleElement = screen.getByText('UI UX Designer');
      expect(roleElement).toBeInTheDocument();
    });

    it('should maintain scrollable content area', () => {
      render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      const drawer = screen.getByTestId('team-sidebar-drawer');
      
      // Should have flex column layout for scrollable content
      expect(drawer).toHaveClass('flex');
      expect(drawer).toHaveClass('flex-col');
    });
  });

  describe('Accessibility', () => {
    it('should handle keyboard navigation properly', () => {
      render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      // Escape key should work
      fireEvent.keyDown(document, { key: 'Escape' });
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should have proper test identifiers', () => {
      render(<TeamSidebar isOpen={true} onClose={mockOnClose} />);
      
      expect(screen.getByTestId('team-sidebar-drawer')).toBeInTheDocument();
      expect(screen.getByTestId('team-sidebar-backdrop')).toBeInTheDocument();
    });
  });
});
