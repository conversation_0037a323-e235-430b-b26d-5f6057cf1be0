import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';

describe('Persistent Sidebar Layout Integration', () => {
  it('should demonstrate main content reflow with sidebar', () => {
    // Create a test layout similar to App.tsx structure
    const TestLayout = ({ sidebarOpen }: { sidebarOpen: boolean }) => (
      <div className="flex">
        <main
          className={`flex-grow transition-all duration-300 ease-in-out ${
            sidebarOpen ? 'mr-[300px]' : 'mr-0'
          }`}
          data-testid="main-content"
        >
          Main Content
        </main>
        <aside
          className={`${
            sidebarOpen ? 'w-[300px]' : 'w-0'
          } transition-all duration-300 ease-in-out overflow-hidden bg-custom-card border-l border-custom-border`}
          data-testid="sidebar"
        >
          {sidebarOpen && <div>Sidebar Content</div>}
        </aside>
      </div>
    );

    // Test with sidebar closed
    const { rerender } = render(<TestLayout sidebarOpen={false} />);
    
    const mainContent = screen.getByTestId('main-content');
    const sidebar = screen.getByTestId('sidebar');
    
    expect(mainContent).toHaveClass('mr-0');
    expect(sidebar).toHaveClass('w-0');
    
    // Test with sidebar open
    rerender(<TestLayout sidebarOpen={true} />);
    
    expect(mainContent).toHaveClass('mr-[300px]');
    expect(sidebar).toHaveClass('w-[300px]');
    
    // Check that sidebar content is rendered
    expect(screen.getByText('Sidebar Content')).toBeInTheDocument();
  });

  it('should have smooth transition classes', () => {
    const TestLayout = ({ sidebarOpen }: { sidebarOpen: boolean }) => (
      <div className="flex">
        <main
          className={`flex-grow transition-all duration-300 ease-in-out ${
            sidebarOpen ? 'mr-[300px]' : 'mr-0'
          }`}
          data-testid="main-content"
        >
          Main Content
        </main>
        <aside
          className={`${
            sidebarOpen ? 'w-[300px]' : 'w-0'
          } transition-all duration-300 ease-in-out overflow-hidden`}
          data-testid="sidebar"
        >
          Sidebar
        </aside>
      </div>
    );

    render(<TestLayout sidebarOpen={true} />);
    
    const mainContent = screen.getByTestId('main-content');
    const sidebar = screen.getByTestId('sidebar');
    
    // Both elements should have transition classes
    expect(mainContent).toHaveClass('transition-all', 'duration-300', 'ease-in-out');
    expect(sidebar).toHaveClass('transition-all', 'duration-300', 'ease-in-out');
  });
});
