# مستندات صفحه نمایش اسکرین (Screen View)

## فهرست مطالب

1. [معرفی کلی صفحه](#معرفی-کلی-صفحه)
2. [عناصر موجود در صفحه](#عناصر-موجود-در-صفحه)
3. [سیستم لودینگ و حالات مختلف](#سیستم-لودینگ-و-حالات-مختلف)
4. [عملیات انجام شده](#عملیات-انجام-شده)
5. [اتصال به LiveKit](#اتصال-به-livekit)
6. [مدیریت ویدیو و Track ها](#مدیریت-ویدیو-و-track-ها)
7. [تابع handleTrackUnsubscribed](#تابع-handletrackUnsubscribed)
8. [جریان کامل سیستم](#جریان-کامل-سیستم)

---

## معرفی کلی صفحه

صفحه `screen-view.html` یک **نمایشگر ویدیو** برای مشاهده اسکرین‌شیرینگ کارمندان است که با استفاده از **LiveKit** پیاده‌سازی شده. این صفحه امکان مشاهده زنده اسکرین کاربران را فراهم می‌کند.

### ویژگی‌های کلیدی:
- **نمایش ویدیو زنده** از اسکرین کاربران
- **کنترل‌های زوم و درگ** برای ویدیو
- **سیستم لودینگ پیشرفته** با حالات مختلف
- **مدیریت خطا** و بازاتصال خودکار
- **رابط کاربری زیبا** با انیمیشن‌های متحرک

---

## عناصر موجود در صفحه

### 1. **عناصر کنترلی:**
```html
<!-- دکمه بستن -->
<button class="close-button">
  <i class="fas fa-times"></i>
</button>

<!-- دکمه تمام صفحه -->
<button class="fullscreen-button" id="fullscreen-button">
  <i class="fas fa-expand"></i>
</button>

<!-- نوار درگ -->
<div class="drag-bar"></div>
```

### 2. **نمایش اطلاعات کاربر:**
```html
<!-- پروفایل کاربر -->
<div class="user-profile">
    <div class="avatar">
        <img id="user-avatar" src="" alt="User Avatar">
    </div>
</div>

<!-- متن وضعیت -->
<h1 id="user-data">Timee View</h1>
<h1 id="user-error" style="display: none;">خطا دوباره امتحان کنید</h1>
```

### 3. **عنصر ویدیو:**
```html
<!-- ویدیو اصلی -->
<video id="remoteVideo" autoplay playsinline controls style="display: none;"></video>
```

### 4. **سیستم پیام خطا:**
```html
<div class="error-message" id="error-message" style="display: none;">
    <span id="error-text"></span>
    <button id="error-button">Close</button>
</div>
```

---

## سیستم لودینگ و حالات مختلف

### 1. **لودر اصلی (Spinner):**
```html
<div id="loader" class="loader-container" style="display: none;">
    <div class="spinner">
        <div class="dot dot1"></div>
        <div class="dot dot2"></div>
        <div class="dot dot3"></div>
    </div>
</div>
```

**کاربرد:**
- نمایش در حین اتصال به اتاق
- انیمیشن نقاط چرخان با افکت نئونی
- مخفی شدن پس از اتصال موفق

### 2. **لودر ثانویه:**
```html
<div class="loader-overlay">
    <div id="loader2" class="loader2"></div>
</div>
```

**کاربرد:**
- لودر ساده‌تر برای عملیات کوتاه‌مدت
- انیمیشن چرخشی کلاسیک

### 3. **حالات مختلف لودینگ:**

#### حالت 1: در انتظار داده‌های کاربر
```javascript
document.getElementById("loader").style.display = "flex"; 
document.getElementById("user-data").innerText = ""
```

#### حالت 2: اتصال به اتاق
```javascript
// تایم‌اوت 90 ثانیه برای اتصال
const loaderTimeout = setTimeout(() => {
    if (document.getElementById("loader").style.display === "flex") {
        displayErrorMessage("خطا در اتصال به اتاق, تلاش مجدد");
        document.getElementById("loader").style.display = "none";
    }
}, 90000);
```

#### حالت 3: دریافت ویدیو
```javascript
function handleTrackSubscribed(track, publication, participant) {
    let loaderOverlay = document.querySelector('.loader-container');
    loaderOverlay.classList.add('active_record'); // مخفی کردن لودر
    document.getElementById("user-data").innerText = ""
}
```

---

## عملیات انجام شده

### 1. **دریافت اطلاعات کاربر:**
```javascript
ipcRenderer.on('user-data', (event, user) => {
    fullname = user.fullname;
    let avatar = user.avatar;
    room_id = user.room_id;
    user_id = user.user_id;
    user_screan = user.user_screan;
    user_started = user.started;
    
    // بررسی وضعیت فعالیت کاربر
    if (user_started === false) {
        document.getElementById("user-data").innerText = "اتصال شما غیر فعال است";
        setTimeout(() => window.close(), 2000);
    }
    
    // تنظیم تصویر پروفایل
    document.getElementById('user-avatar').src = avatar;
    
    // بررسی وضعیت کاربر
    checkStatus(user_id, currentRoom);
});
```

### 2. **بررسی وضعیت کاربر:**
```javascript
function checkStatus(user_id, currentRoom) {
    fetch(`${activeUrl}/employees-screan/${user_id}/`)
        .then(response => response.json())
        .then(data => {
            if (data.status === true && data.active_screan) {
                console.log('Employee is active and online');
            } else {
                document.getElementById("user-data").innerText = "کارمند غیر فعال است";
                if (currentRoom) currentRoom.disconnect();
                setTimeout(() => window.close(), 2500);
            }
        });
}
```

### 3. **کنترل‌های ویدیو:**

#### زوم با ماوس:
```javascript
video.addEventListener("wheel", function (event) {
    event.preventDefault();
    if (event.deltaY < 0) {
        scale += 0.1; // زوم به داخل
    } else {
        scale = Math.max(1, scale - 0.1); // زوم به بیرون
    }
    video.style.transform = `scale(${scale})`;
});
```

#### درگ کردن ویدیو:
```javascript
video.addEventListener('mousedown', function (event) {
    if (scale > 1) {
        isDragging = true;
        startX = event.clientX - videoPosX;
        startY = event.clientY - videoPosY;
        video.style.cursor = 'grabbing';
    }
});
```

### 4. **مدیریت تمام صفحه:**
```javascript
fullscreenButton.addEventListener("click", function () {
    if (video.requestFullscreen) {
        video.requestFullscreen();
    } else if (video.webkitRequestFullscreen) {
        video.webkitRequestFullscreen();
    } else if (video.msRequestFullscreen) {
        video.msRequestFullscreen();
    }
});
```

---

## اتصال به LiveKit

### 1. **دریافت توکن:**
```javascript
async function requestToken(url, roomId, userName) {
    const user_id = Math.floor(Math.random() * 10000);
    let Name = userName + user_id;
    
    const response = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
            event: "meet",
            live_session_id: roomId,
            subject: "null",
            user_id: user_id,
            username: Name,
            can_publish: "true",
        }),
    });
    
    const data = await response.json();
    return data.token;
}
```

### 2. **ایجاد اتاق LiveKit:**
```javascript
async function connectToRoom(serverUrl, token) {
    const room = new LivekitClient.Room({
        audioCaptureDefaults: {
            autoGainControl: true,
            echoCancellation: true,
            noiseSuppression: true,
        },
        videoCaptureDefaults: {
            resolution: { width: 1280, height: 720, frameRate: 30 },
        },
        publishDefaults: {
            videoEncoding: { maxBitrate: 1_500_000, maxFramerate: 30 },
            screenShareEncoding: { maxBitrate: 1_500_000, maxFramerate: 30 },
        },
    });
    
    currentRoom = room;
    window.currentRoom = room;
}
```

### 3. **ثبت Event Listener ها:**
```javascript
currentRoom
    .on(LivekitClient.RoomEvent.TrackSubscribed, handleTrackSubscribed)
    .on(LivekitClient.RoomEvent.TrackUnsubscribed, handleTrackUnsubscribed)
    .on(LivekitClient.RoomEvent.ParticipantConnected, participantConnected)
    .on(LivekitClient.RoomEvent.ParticipantDisconnected, participantDisconnected);
```

---

## مدیریت ویدیو و Track ها

### 1. **دریافت Track ویدیو:**
```javascript
function handleTrackSubscribed(track, publication, participant) {
    let loader = document.getElementById('loader');
    let loaderOverlay = document.querySelector('.loader-container');
    loaderOverlay.classList.add('active_record');
    document.getElementById("user-data").innerText = "";

    if (track.kind === LivekitClient.Track.Kind.Video) {
        console.log(`Subscribed to video track from ${participant.identity}`);
        const videoElement = document.getElementById('remoteVideo');
        videoElement.style.display = "flex";
        track.attach(videoElement); // اتصال track به عنصر ویدیو
    }
}
```

**عملکرد:**
- بررسی نوع Track (ویدیو یا صدا)
- نمایش عنصر ویدیو
- اتصال Track به عنصر HTML
- مخفی کردن لودر

### 2. **مدیریت اتصال شرکت‌کنندگان:**
```javascript
function participantConnected(participant) {
    console.log(`Participant connected: ${participant.identity}`);
    // آماده‌سازی برای دریافت محتوا
}

function participantDisconnected(participant) {
    console.log(`Participant disconnected: ${participant.identity}`);
    // مدیریت قطع ارتباط
}
```

---

## تابع handleTrackUnsubscribed

### عملکرد کلی:
```javascript
function handleTrackUnsubscribed(track, publication, participant) {
    if (track.kind === LivekitClient.Track.Kind.Video) {
        console.log(`Unsubscribed from video track of ${participant.identity}`);
        const videoElement = document.getElementById('remoteVideo');
        if (videoElement) {
            videoElement.style.display = "none";
            track.detach(videoElement);
        }
    }
    // مشابه برای صدا
}
```

### کاربردهای این تابع:

#### 1. **قطع اشتراک ویدیو:**
- وقتی کاربر اسکرین‌شیرینگ را متوقف می‌کند
- وقتی کاربر از اتاق خارج می‌شود
- وقتی مشکل شبکه پیش می‌آید

#### 2. **پاکسازی منابع:**
```javascript
// جدا کردن track از عنصر ویدیو
track.detach(videoElement);

// مخفی کردن عنصر ویدیو
videoElement.style.display = "none";
```

#### 3. **مدیریت حافظه:**
- آزادسازی منابع ویدیو
- جلوگیری از memory leak
- بهینه‌سازی عملکرد

#### 4. **تجربه کاربری:**
- مخفی کردن ویدیو خالی
- نمایش پیام مناسب به کاربر
- آماده‌سازی برای اتصال مجدد

### سناریوهای مختلف:

#### سناریو 1: کاربر اسکرین‌شیرینگ را متوقف کرد
```
کاربر Stop Screen Share می‌زند
    ↓
handleTrackUnsubscribed فراخوانی می‌شود
    ↓
ویدیو مخفی می‌شود
    ↓
منابع آزاد می‌شوند
```

#### سناریو 2: قطع اتصال ناگهانی
```
اتصال شبکه قطع می‌شود
    ↓
LiveKit قطع اتصال را تشخیص می‌دهد
    ↓
handleTrackUnsubscribed اجرا می‌شود
    ↓
UI به حالت انتظار برمی‌گردد
```

#### سناریو 3: خروج از اتاق
```
کاربر از اتاق خارج می‌شود
    ↓
participantDisconnected فراخوانی می‌شود
    ↓
handleTrackUnsubscribed برای تمام track ها اجرا می‌شود
    ↓
صفحه به حالت اولیه برمی‌گردد
```

---

## جریان کامل سیستم

### مرحله 1: راه‌اندازی اولیه
```
بارگذاری صفحه
    ↓
نمایش لودر اصلی
    ↓
انتظار برای دریافت اطلاعات کاربر از IPC
    ↓
بررسی وضعیت کاربر از API
```

### مرحله 2: اتصال به LiveKit
```
دریافت توکن از سرور
    ↓
ایجاد Room جدید
    ↓
تنظیم Event Listener ها
    ↓
اتصال به سرور LiveKit
```

### مرحله 3: مدیریت محتوا
```
انتظار برای Track های ویدیو
    ↓
handleTrackSubscribed: نمایش ویدیو
    ↓
ارسال درخواست اسکرین‌شیرینگ
    ↓
نمایش محتوای دریافتی
```

### مرحله 4: مدیریت قطع اتصال
```
تشخیص قطع Track
    ↓
handleTrackUnsubscribed: مخفی کردن ویدیو
    ↓
آزادسازی منابع
    ↓
آماده‌سازی برای اتصال مجدد
```

### مرحله 5: پایان جلسه
```
بستن صفحه توسط کاربر
    ↓
قطع اتصال از اتاق
    ↓
پاکسازی تمام منابع
    ↓
بستن پنجره
```

---

## نکات مهم و بهینه‌سازی

### 1. **مدیریت خطا:**
- تایم‌اوت 90 ثانیه برای اتصال
- بازاتصال خودکار در صورت قطعی
- نمایش پیام‌های خطای مناسب

### 2. **بهینه‌سازی عملکرد:**
- تنظیمات کیفیت ویدیو قابل تنظیم
- مدیریت حافظه با detach کردن track ها
- استفاده از simulcast برای کیفیت متغیر

### 3. **تجربه کاربری:**
- انیمیشن‌های زیبا برای لودینگ
- کنترل‌های زوم و درگ
- دکمه‌های تمام صفحه و بستن

### 4. **امنیت:**
- بررسی وضعیت کاربر قبل از اتصال
- مدیریت توکن‌های موقت
- قطع اتصال در صورت عدم مجوز

این سیستم یک راه‌حل کامل برای نمایش اسکرین‌شیرینگ زنده با قابلیت‌های پیشرفته و مدیریت خطای جامع فراهم می‌کند.
```
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Video Player</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #1c3b58, #2a585d); /* Blue and green gradient */
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
      overflow: hidden;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.7);
      position: relative;
    }

    h1 {
      font-size: 2.5em;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
      margin-bottom: 20px;
    }


    .zoom-menu {
      position: absolute;
      top: 15px;
      right: 15px;
      /* bottom: 70%; */
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      width: 40px;
      height: 40px;
      cursor: pointer;
      transition: background-color 0.3s;
      z-index: 1001; /* بالاتر از لودر */

    }

    .zoom-menu:hover {
      background-color: rgba(255, 255, 255, 0.4);
    }

    .zoom-controls {
      position: absolute;
      top: 60px; /* Position under the zoom menu button */
      right: 15px;
      display: none; /* Hidden by default */
      flex-direction: column;
      gap: 10px;
      z-index: 1001; /* بالاتر از لودر */

    }
    .drag-bar {
      width: calc(100% - 40px); /* عرض محاسبه‌شده برای فاصله از دو طرف */
      height: 30px;
      margin-left: 20%;
      cursor: grab; /* نشان‌گر کشیدن */  
      background-color: rgba(0, 0, 0, 0.3);
      -webkit-app-region: drag; /* قابلیت درگ */
      position: absolute;
      justify-content: center; /* وسط‌چینی محتوا */
      align-items: center; /* وسط‌چینی عمودی */

      top: 0;
      left: 0;
    }
    .zoom-button {
      background-color: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      padding: 10px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      transition: background-color 0.3s, transform 0.2s;
    }

    .zoom-button i {
      font-size: 20px;
      color: #ffffff;
    }

    .close-button i {
      font-size: 24px;
      color: #ffffff;
    }

    .zoom-menu i {
      font-size: 20px;
      color: #ffffff;
    }

    .zoom-button:hover {
      background-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }
    .fullscreen-button {
      position: absolute;
      top: 10px;
      left: 7%; /* قرارگیری در سمت چپ */
      background-color: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      padding: 5px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      transition: background-color 0.3s, transform 0.2s;
      z-index: 10001; /* بالاتر از لودر */
    }

    .close-button {
      position: absolute;
      top: 10px;
      left: 15px;
      background-color: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      padding: 5px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      transition: background-color 0.3s, transform 0.2s;
      z-index: 10001; /* بالاتر از لودر */
      cursor: pointer; /* نشان‌گر برای قابل کلیک بودن */

    }
    
    /* .close-button {
      position: absolute;
      top: 15px;
      left: 15px;
      background-color: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      padding: 10px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      transition: background-color 0.3s, transform 0.2s;
    } */

    .fullscreen-button i, .close-button i {
      font-size: 16px;
      color: #ffffff;
    }

    .fullscreen-button:hover, .close-button:hover {
      background-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }
.loader-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.9); /* تاریک و نیمه‌شفاف */
    z-index: 1000;
}

/* اسپینر */
.spinner {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 80px;
    height: 80px;
}

/* نقاط چرخان */
.dot {
    width: 20px; /* اندازه دایره */
    height: 20px; /* اندازه دایره */
    border-radius: 50%;
    background-color: #00bfff; /* رنگ آبی نئونی */
    box-shadow: 0 0 20px rgba(0, 191, 255, 1), 0 0 30px rgba(0, 191, 255, 0.9), 0 0 40px rgba(0, 191, 255, 0.8); /* سایه‌های چندلایه برای جلوه نئونی */
    /* animation: bounce 1.5s infinite ease-in-out, glow 1.5s infinite ease-in-out; ترکیب انیمیشن‌ها */
    animation: bounce 1.5s infinite ease-in-out, glow 1.5s infinite ease-in-out, spin 1.5s infinite linear; /* ترکیب انیمیشن‌ها */

}

/* انیمیشن‌ها */
@keyframes bounce {
    0%, 100% {
        transform: scale(0.5);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(0, 191, 255, 1), 0 0 30px rgba(0, 191, 255, 0.9), 0 0 40px rgba(0, 191, 255, 0.8);
    }
    50% {
        box-shadow: 0 0 40px rgba(0, 191, 255, 1), 0 0 60px rgba(0, 191, 255, 0.9), 0 0 80px rgba(0, 191, 255, 0.8);
    }
}

/* تأخیر انیمیشن برای نقاط */
.dot1 {
    animation-delay: -0.4s;
}
.dot2 {
    animation-delay: -0.2s;
}
.dot3 {
    animation-delay: 0s;
}

/* افکت نهایی بر روی نقاط */
.dot {
    animation: bounce 1.5s infinite ease-in-out, glow 1.5s infinite ease-in-out; /* ترکیب انیمیشن‌ها */
}
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}


/* استایل پروفایل کاربر */
.user-profile {
    display: flex; /* استفاده از فلیکس باکس */
    flex-direction: column; /* عمودی کردن محتوا */
    align-items: center; /* وسط‌چینی محتوا */
    position: absolute; /* موقعیت‌دهی مطلق */
    top: 1%; /* فاصله از بالا */
    left: 13%;

}

.avatar {
    position: relative; /* تنظیم موقعیت نسبی برای قرار دادن متن */
    width: 45px; /* عرض دایره جدید */
    height: 45px; /* ارتفاع دایره جدید */
    border-radius: 50%; /* گرد کردن */
    overflow: hidden; /* جلوگیری از بیرون زدن تصویر */

}

.avatar img {
    width: 100%; /* استفاده از تمام عرض دایره */
    height: auto; /* حفظ نسبت تصویر */
}
.error-message {
    color: #fff; /* رنگ متن سفید */
    font-size: 1em; /* اندازه متن */
    background: linear-gradient(135deg, rgba(0, 102, 204, 0.8), rgba(0, 204, 102, 0.8)); /* گرادیان آبی و سبز لجنی */
    border: 1px solid rgba(0, 102, 204, 0.9); /* حاشیه آبی */
    padding: 15px; /* پدینگ برای زیبایی */
    border-radius: 5px; /* گوشه‌های گرد */
    margin-top: 10px; /* فاصله از عناصر دیگر */
    position: relative; /* برای قرار دادن دکمه */
    display: flex; /* استفاده از فلیکس باکس */
    justify-content: space-between; /* فاصله بین متن و دکمه */
    align-items: center; /* وسط‌چینی عمودی */
    z-index: 1000; /* بالای همه المنت‌ها */

}
.active_record {
    display: none;
}
#error-button {
    background-color: #fff; /* رنگ پس‌زمینه دکمه سفید */
    color: #0066cc; /* رنگ متن دکمه */
    border: none; /* بدون حاشیه */
    padding: 8px 12px; /* پدینگ دکمه */
    border-radius: 5px; /* گوشه‌های گرد */
    cursor: pointer; /* نشان‌گر کلیک */
    transition: background-color 0.3s; /* انیمیشن تغییر رنگ */
    z-index: 1000; /* بالای همه المنت‌ها */

}

#error-button:hover {
    background-color: #f0f0f0; /* تغییر رنگ در حالت هاور */
}

        .loader-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5); /* سایه پس‌زمینه */
            display: none; /* نمایش غیرفعال به صورت دیفالت */
            align-items: center;
            justify-content: center;
            z-index: 1000; /* بالای همه المنت‌ها */
        }
        .loader2 {
            border: 8px solid #f3f3f3;
            border-top: 8px solid #3498db;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin2 1s linear infinite;
        }
        @keyframes spin2 {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        /* max-width: 800px; */
        /* display: flex; نمایش فعال */
        #remoteVideo {
            width: 99%;
            height: 90%;
            object-fit: contain; /* حفظ نسبت ویدیو و نمایش کامل محتوا */
            margin: 10px auto 20px auto; /* تنظیم فاصله برای نمایش بالای عنوان */
            border: 2px solid #3497da;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            background-color: #000;
            outline: none;
            z-index: 10000;
        }

        video:focus {
            outline: 2px solid #3498db;
        }
</style>
</head>
<body>
    <div class="loader-overlay">
        <div id="loader2" class="loader2"></div>
    </div>

    <div id="loader" class="loader-container" style="display: none;">
        <div class="spinner">
            <div class="dot dot1"></div>
            <div class="dot dot2"></div>
            <div class="dot dot3"></div>
        </div>
    </div>

    <button class="fullscreen-button" id="fullscreen-button">
      <i class="fas fa-expand"></i> <!-- آیکون تمام صفحه -->
    </button>
    <div class="drag-bar"></div> <!-- نوار درگ در بالای صفحه -->    
    <button class="close-button">
      <i class="fas fa-times"></i>
    </button>
    <div class="user-profile">
        <div class="avatar">
            <img id="user-avatar" src="" alt="User Avatar">
        </div>
        <!-- <h6 id="user-fullname" class="fullname"></h6> -->
    </div>
    <!-- <div id="loader" class="loader"></div> -->

    <h1 id="user-data">Timee View</h1>
    <h1 id="user-error" style="display: none;">خطا دوباره امتحان کنید</h1>

    <div class="error-message" id="error-message" style="display: none;">
        <span id="error-text"></span>
        <button id="error-button">Close</button>
    </div>
    <!-- عنصر ویدیو برای نمایش جریان -->

        <video id="remoteVideo" autoplay playsinline controls style="display: none;"></video>

  <!-- <script src="./api.js"></script> -->
  <script>
      const getTokenUrl = `https://habibmeet.nwhco.ir/test/sfu/token`;
      const baseUrl = `wss://livekit.habibmeet.nwhco.ir`; 
    //   const activeUrl = `http://localhost:8000/api`
    //   let base_url = "https://timee.nwhco.ir";
      const activeUrl = `https://timee.nwhco.ir/api`
      let currentRoom;
      let fullname;
      let room_id;
      let user_id;
      let loaderTimeout;
      let screan;

    document.addEventListener("DOMContentLoaded", async function () {
        // console.log('------dom-------', request_screen_user)    

      const closeButton = document.querySelector(".close-button");
      const fullscreenButton = document.getElementById("fullscreen-button");
      const timmeText = document.getElementById("user-data");

      
      const { ipcRenderer } = require('electron');
    //   ipcRenderer.send('close-window');

      console.log('----------1---',ipcRenderer)
      // دریافت داده‌های کاربر
      ipcRenderer.on('user-data', (event, user) => {
            fullname = user.fullname; // ذخیره نام کاربر در متغیر سراسری
            let avatar = user.avatar;
            room_id = user.room_id; // ذخیره شناسه اتاق در متغیر سراسری
            user_id = user.user_id
            user_screan = user.user_screan
            user_started = user.started
            if (user_started === false){
                document.getElementById("loader").style.display = "none"; // پنهان کردن لودر در صورت خطا
                // Employee is inactive or not online
                document.getElementById("user-data").innerText = "اتصال شما غیر فعال است";
                
                if (currentRoom) {
                    currentRoom.disconnect();
                }
                setTimeout(() => {
                       window.close();  // Close the current window
                }, 2000); 
            }
            console.log('----------2---',user_id,fullname, avatar, room_id)
            checkStatus(user_id, currentRoom);
            
            // document.getElementById('user-fullname').innerText = fullname;
            document.getElementById('user-avatar').src = avatar; // تنظیم تصویر
        });
        
      const { webFrame } = require('electron');

      const video = document.getElementById("remoteVideo");  
      const container = video.parentElement; // فرض کنید والد ویدیو کانتینر باشد
      let scale = 1;
      let zoomLevel = 1;
      let isDragging = false;
      let startX, startY;
      let videoPosX = 0, videoPosY = 0;


      
    if (video) {
        // اطمینان از اینکه ویدیو همیشه در حال پخش است
        video.addEventListener('pause', () => {
            video.play();
        });
        
        // حذف قابلیت استاپ از طریق کیبورد
        video.controls = false;
        video.setAttribute('controls', false);
    }

    video.addEventListener('mousedown', function (event) {
        if (scale > 1) {
            isDragging = true;
            startX = event.clientX - videoPosX;
            startY = event.clientY - videoPosY;
            video.style.cursor = 'grabbing';
        }
    });

    window.addEventListener('mousemove', function (event) {
        if (isDragging) {
            let newPosX = event.clientX - startX;
            let newPosY = event.clientY - startY;

            // محاسبه محدودیت‌های پیمایش
            const maxPosX = (container.clientWidth - video.clientWidth * scale) / 2;
            const maxPosY = (container.clientHeight - video.clientHeight * scale) / 2;

            // محدود کردن موقعیت ویدیو به محدوده کانتینر
            videoPosX = Math.max(maxPosX, Math.min(-maxPosX, newPosX));
            videoPosY = Math.max(maxPosY, Math.min(-maxPosY, newPosY));

            video.style.position = 'relative';
            video.style.left = `${videoPosX}px`;
            video.style.top = `${videoPosY}px`;
        }
    });

    window.addEventListener('mouseup', function () {
        if (isDragging) {
            isDragging = false;
            video.style.cursor = 'grab';
        }
    });


      video.addEventListener("wheel", function (event) {
          event.preventDefault();
          if (event.deltaY < 0) {
              // زوم به داخل
              scale += 0.1;
          } else {
              // زوم به بیرون
            scale = Math.max(1, scale - 0.1);
          }
          video.style.transform = `scale(${scale})`;
          console.log('Video scale:', scale);
      });

      window.addEventListener('keydown', function (event) {
          if (event.ctrlKey && event.key === '+') {
              event.preventDefault();
              scale += 0.1;
              video.style.transform = `scale(${scale})`;
              if (scale > 1) {
                  video.style.cursor = 'grab';
              }
          }
      });
      window.addEventListener('keydown', function (event) {
          if (event.ctrlKey && event.key === '-') {
              event.preventDefault();
              scale = Math.max(1, scale - 0.1); // اطمینان از اینکه مقدار scale از 1 کمتر نمی‌شود
              video.style.transform = `scale(${scale})`;
              if (scale === 1) {
                  video.style.cursor = 'default';
                  video.style.left = '0px';
                  video.style.top = '0px';
                  videoPosX = 0;
                  videoPosY = 0;
              }   
          }
      });


      if (closeButton) {
        closeButton.addEventListener("click", function () {
            // ipcRenderer.send('close-watch-window'); // ارسال پیام به main process
          window.close();
          if (currentRoom) {
            currentRoom.disconnect();
          }


        });
      }

       if (fullscreenButton){
        fullscreenButton.addEventListener("click", function () {
            if (video.requestFullscreen) {
                video.requestFullscreen();
            } else if (video.webkitRequestFullscreen) { // برای مرورگرهای Webkit (مانند Safari)
                video.webkitRequestFullscreen();
            } else if (video.msRequestFullscreen) { // برای مرورگرهای قدیمی‌تر IE/Edge
                video.msRequestFullscreen();
            }
            // const { ipcRenderer } = require('electron'); // اضافه کردن ipcRenderer
            // ipcRenderer.send('toggle-fullscreen'); // ارسال پیام به main process

        });

       }
        // }
        
    //    await showScreen(room_id, fullname)
    document.getElementById("loader").style.display = "flex"; 
    document.getElementById("user-data").innerText = ""

    await checkUserDataAndShowScreen();

    });
    async function checkUserDataAndShowScreen() {
        const timeout = 10000; // مدت زمان حداکثر 7 ثانیه
        const interval = 1000; // چک کردن هر 500 میلی‌ثانیه
        let elapsedTime = 0; // زمان سپری شده
        let userDataReceived = false; // نشان دهنده دریافت داده کاربر

        // این تابع به ازای هر بار دریافت داده کاربر اجرا می‌شود
        const checkUserData = () => {
            if (!userDataReceived && fullname && room_id) {
                userDataReceived = true; // داده کاربر دریافت شده است
                clearInterval(intervalId); // توقف حلقه
                showScreen(room_id, fullname); // ارسال داده‌ها به showScreen
            }
        };
        if (screan && screan === "false"){
            clearInterval(intervalId); // توقف حلقه
            displayErrorMessage("عدم دسترسی"); // نمایش پیام خطا
        }
        // حلقه چک کردن
        const intervalId = setInterval(() => {
            checkUserData();
            elapsedTime += interval;

            // اگر زمان سپری شده بیشتر از زمان تعیین شده باشد
            if (elapsedTime >= timeout) {
                clearInterval(intervalId); // توقف حلقه
                displayErrorMessage("خطا در دریافت اطلاعات کاربر"); // نمایش پیام خطا
            }
        }, interval);
    }
    async function showScreen(room_id, fullname) {
        token_test = await requestToken(getTokenUrl, room_id, "test" )
        console.log("-----ok--token------>", token_test);
        
        console.log('======================', room_id, fullname)
        token = await requestToken(getTokenUrl, room_id, fullname )
        console.log('---------ok----------', room_id, fullname)
        if (!token) {
            displayErrorMessage("توکن دریافت نشد"); // نمایش پیام خطا
            return; // خروج از تابع
        }
        // console.log("-----ok--token------>", token);
    
        await connectToRoom(baseUrl, token)

    }

    async function connectToRoom(serverUrl, token) {
        console.log('-----------connectToRoom-------------')
        LivekitClient.setLogLevel(LivekitClient.LogLevel.debug);
        console.log('-----------setLogLevel-------------', LivekitClient)

        const room = new LivekitClient.Room({
          audioCaptureDefaults: {
            autoGainControl: true,
            deviceId: "",
            echoCancellation: true,
            noiseSuppression: true,
          },
          videoCaptureDefaults: {
            deviceId: "",
            facingMode: "user",
            resolution: {
              width: 1280,
              height: 720,
              frameRate: 30,
            },
          },
          publishDefaults: {
            videoEncoding: {
              maxBitrate: 1_500_000,
              maxFramerate: 30,
            },
            screenShareEncoding: {
              maxBitrate: 1_500_000,
              maxFramerate: 30,
            },
            audioBitrate: 20_000,
            dtx: true,
            // Only needed if overriding defaults
            videoSimulcastLayers: [
              {
                width: 640,
                height: 360,
                encoding: {
                  maxBitrate: 500_000,
                  maxFramerate: 20,
                },
              },
              {
                width: 320,
                height: 180,
                encoding: {
                  maxBitrate: 150_000,
                  maxFramerate: 15,
                },
              },
            ],
          },
        });
        const loaderTimeout = setTimeout(() => {
            let loader = document.getElementById("loader");
            console.log('===========times---', loader);
            if (loader.classList.contains("active_record")) {
                console.log('Loaderr = =============== ====');
            } else if (document.getElementById("loader").style.display === "flex") {
                document.getElementById("user-data").innerText = ""
                console.log('error = =============== ====');
                displayErrorMessage(" خطا در اتصال به اتاق, تلاش مجدد");
                document.getElementById("loader").style.display = "none"; // پنهان کردن لودر
            }
        }, 90000);

        console.log("Connect Room ... " + baseUrl);
        const startTime = Date.now();

        await room.prepareConnection(baseUrl, token);

        const prewarmTime = Date.now() - startTime;
        console.log("Connectd Room" + prewarmTime);

        currentRoom = room;
        window.currentRoom = room;

        currentRoom
            .on(LivekitClient.RoomEvent.TrackSubscribed, handleTrackSubscribed)
            .on(LivekitClient.RoomEvent.TrackUnsubscribed, handleTrackUnsubscribed)
            .on(LivekitClient.RoomEvent.ParticipantConnected, participantConnected)
            .on(LivekitClient.RoomEvent.ParticipantDisconnected, participantDisconnected);

        // await currentRoom.connect(baseUrl, token);
        // console.log("connected room");

        // document.getElementById("loader").style.display = "none"; 
        // responce = await request_screen_user(room_id, user_id)
        // console.log('-------response------> ',responce)
        
        try {
            await currentRoom.connect(baseUrl, token);
            console.log("connected room");
            // document.getElementById("loader").style.display = "none"; // پنهان کردن لودر بعد از اتصال موفق
            // document.getElementById("user-data").innerText = "متصل شدید"
            let text = document.getElementById("user-data");
            text.innerText = "";
            responce = await request_screen_user(room_id, user_id, fullname)
            
        } catch (error) {
            console.error("Error connecting to the room:", error);
            displayErrorMessage("خطا در اتصال به اتاق: لطفاً دوباره تلاش کنید."); // نمایش پیام خطا
            document.getElementById("loader").style.display = "none"; // پنهان کردن لودر در صورت خطا
            clearTimeout(loaderTimeout); // متوقف کردن تایمر در صورت خطا
        }

    }

    function handleTrackSubscribed(track, publication, participant) {
        let loader = document.getElementById('loader');
        let loaderOverlay = document.querySelector('.loader-container');
        loaderOverlay.classList.add('active_record'); // اضافه کردن کلاس active_record
        document.getElementById("user-data").innerText = ""

        if (track.kind === LivekitClient.Track.Kind.Video) {
            console.log(`Subscribed to video track from ${participant.identity}`);
            const videoElement = document.getElementById('remoteVideo');
            videoElement.style.display = "flex"
            track.attach(videoElement);
        }
    }
    function participantConnected(participant) {
        // document.getElementById("user-data").innerText = "در حال بارگیری صفحه نمایش"

        // console.log(`Participant connected: ${participant.identity}`);
    //   renderParticipant(participant);
    }

    function participantDisconnected(participant) {
        console.log(`Participant disconnected: ${participant.identity}`);
        // مدیریت قطع ارتباط شرکت‌کننده در صورت نیاز
    }

    function handleTrackUnsubscribed(track, publication, participant) {
        if (track.kind === LivekitClient.Track.Kind.Video) {
            console.log(`Unsubscribed from video track of ${participant.identity}`);
            const videoElement = document.getElementById('remoteVideo');
            if (videoElement) {
                videoElement.style.display = "none"
                track.detach(videoElement);
            }
        }
        // مشابه برای صدا
    }

    async function request_screen_user(room_id, user_id, fullname) {
        const urlSendRequestScreen = `https://timee.nwhco.ir/api/request-screen/`;
        const data = { "room_id": room_id, "user_id": user_id, "notification_type": "request_screen", "fullname": fullname};

        try {
          const response = await fetch(urlSendRequestScreen, {
            method: "POST", // روش درخواست
            headers: {
              "Content-Type": "application/json", // نوع محتوا
            },
            body: JSON.stringify(data), // داده‌ها به صورت JSON
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`); // اگر پاسخ موفقیت‌آمیز نبود
          }
          console.log("Sending request...------------------");

          const responseData = await response.json(); // تبدیل پاسخ به JSON
          return responseData;
        } catch (error) {
          console.error("Error sending request:", error); // لاگ خطا
        }    
    }

    async function requestToken(url, roomId, userName) {
      try {
        const user_id = Math.floor(Math.random() * 10000);
        let Name = userName + user_id
        const response = await fetch(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            event: "meet",
            live_session_id: roomId,
            subject: "null",
            user_id: user_id,
            username: Name,
            can_publish: "true",
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data.token;
      } catch (error) {
        throw new Error(`Token request failed: ${error.message}`);
      }
    }  
    function displayErrorMessage(message) {
        console.log('==============================================================')
        // let error = document.getElementById("user-error")
        document.getElementById("user-data").innerText = message
        currentRoom.disconnect();

        // error.style.display = "flex"; 
        setTimeout(function() {
            location.reload();
        }, 3300);
    }
    function checkStatus(user_id ,currentRoom) {
        console.log('=========checkStatus=====================')

        fetch(`${activeUrl}/employees-screan/${user_id}/`)
            .then(response => response.json())
            .then(data => {
                if (data.status === true && data.active_screan) {
                    // Employee is active and online
                    console.log('Employee is active and online');
                } else {
                    console.error('Employee is inactive or not online');
                    document.getElementById("loader").style.display = "none"; // پنهان کردن لودر در صورت خطا
                    // Employee is inactive or not online
                    document.getElementById("user-data").innerText = "کارمند غیر فعال است";

                    if (currentRoom) {
                        currentRoom.disconnect();
                    }

                    setTimeout(() => {
                        window.close();  // Close the current window
                    }, 2500); 
                }
            })
            .catch(error => {
                console.error('Error checking employee status:', error);
            });
    }

  </script>

  <script src="https://cdn.jsdelivr.net/npm/livekit-client@2.5.0/dist/livekit-client.umd.min.js"></script> 
  
</body>
</html>
