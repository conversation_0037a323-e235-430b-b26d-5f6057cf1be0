{"name": "desktop", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@heroui/react": "^2.7.11", "@iconify-icons/lucide": "^1.2.135", "@iconify/icons-lucide": "^1.2.135", "@iconify/json": "^2.2.356", "@iconify/react": "^6.0.0", "@internationalized/date": "^3.8.2", "@react-aria/i18n": "^3.12.10", "@tauri-apps/api": "^2.6.0", "@tauri-apps/plugin-clipboard-manager": "^2.3.0", "@tauri-apps/plugin-dialog": "^2.3.0", "@tauri-apps/plugin-fs": "^2.4.0", "@tauri-apps/plugin-notification": "^2.3.0", "@tauri-apps/plugin-store": "^2.3.0", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-calendar": "^3.9.0", "@types/react-router-dom": "^5.3.3", "date-fns": "^4.1.0", "framer-motion": "^12.23.0", "livekit-client": "^2.15.2", "react": "^19.1.0", "react-beautiful-dnd": "^13.1.1", "react-calendar": "^6.0.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tauri-apps/cli": "^2.6.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0", "vitest": "^3.2.4"}}