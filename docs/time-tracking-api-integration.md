# Time Tracking API Integration Documentation

## Overview

This document provides comprehensive documentation for the complete time tracking system integration with backend APIs. The system implements a full activity lifecycle with proper error handling, system monitoring, and data persistence.

## System Architecture

### Components
- **Frontend**: React TypeScript components with Tauri integration
- **Backend**: <PERSON>ust Tauri commands for API communication
- **Storage**: Tauri Store for local data persistence
- **Monitoring**: ImReady heartbeat system with error tracking

## API Endpoints

### 1. Start Activity API
- **Endpoint**: `POST /api/activity/start/`
- **Purpose**: Initialize a new time tracking activity
- **Authentication**: X-Api-Key header required

#### Request Payload
```json
{
  "local_time": "2024-01-15 14:30:00",
  "platform": "Linux 6.5.0",
  "ip_address": "*************",
  "device_name": "user-laptop"
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "id": 123
  },
  "message": "Activity started successfully"
}
```

#### Error Responses
- **401/403**: Authentication failed
- **Network Error**: Connection issues

### 2. ImReady Heartbeat API
- **Endpoint**: `GET /api/activity/{id}/imready/`
- **Purpose**: Maintain activity alive status
- **Frequency**: Every 10 seconds
- **Authentication**: X-Api-Key header required

#### Response (200 OK)
```json
{
  "status": "success",
  "message": "Activity updated successfully."
}
```

#### Error Responses
- **400**: Activity already ended
- **401/403**: Authentication failed

### 3. End Activity API
- **Endpoint**: `PUT /api/activity/end/`
- **Purpose**: Complete time tracking activity
- **Authentication**: X-Api-Key header required

#### Request Payload
```json
{
  "activity_id": 123,
  "project_id": 456,
  "task_id": 789,
  "notes": "Working on feature implementation",
  "duration": "02:30:45",
  "imready_error_logs_count": 3
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "message": "Activity ended successfully"
}
```

### 4. Update Activity API
- **Endpoint**: `PUT /api/activity/update/`
- **Purpose**: Update activity details after completion
- **Authentication**: X-Api-Key header required

#### Request Payload
```json
{
  "activity_id": 123,
  "project_id": 456,
  "task_id": 789,
  "notes": "Updated notes after review"
}
```

## System Flow Scenarios

### Scenario 1: Successful Activity Lifecycle

1. **User clicks Start Timer**
   - Frontend validates profile is loaded
   - Calls `start_activity` Tauri command
   - System info collected (OS, device name, IP, timestamp)
   - API request sent to `/api/activity/start/`
   - Activity ID received and stored

2. **ImReady System Activated**
   - Immediate first heartbeat sent
   - 10-second interval established
   - Each heartbeat calls `/api/activity/{id}/imready/`
   - Success: Continue monitoring
   - Error: Log to local storage, continue trying

3. **User clicks Stop Timer**
   - ImReady system stopped
   - Error count retrieved from local storage
   - API request sent to `/api/activity/end/`
   - Local timer stopped
   - Modal displayed for review

4. **User saves in Modal**
   - Updated data sent to `/api/activity/update/`
   - Local storage cleaned up
   - Process completed

### Scenario 2: Network Interruption Handling

1. **Start Activity Success**
   - Activity started normally
   - ImReady system begins

2. **Network Goes Down**
   - ImReady calls fail
   - Errors logged with timestamps and codes
   - System continues attempting every 10 seconds
   - User can continue working offline

3. **Network Restored**
   - ImReady calls succeed again
   - Previous error logs cleared for this activity
   - Normal monitoring resumes

4. **End Activity**
   - Error count included in end request
   - Server receives full activity data including downtime info

### Scenario 3: Authentication Expiry

1. **During Active Tracking**
   - ImReady call returns 403
   - Error logged locally
   - User notified via toast
   - System continues attempting

2. **User Re-authenticates**
   - New token stored
   - ImReady calls resume with new token
   - Error logs cleared on success

## Error Handling Strategy

### Error Types and Codes
- **0**: Network/Connection failure
- **400**: Activity already ended
- **401/403**: Authentication issues
- **500+**: Server errors

### Error Storage Schema
```json
{
  "timestamp": 1705320600000,
  "error_code": 0,
  "activity_id": 123
}
```

### Error Recovery
- **Network Errors**: Retry every 10 seconds indefinitely
- **Auth Errors**: Show user notification, continue retrying
- **Activity Ended**: Stop ImReady system gracefully

## Data Flow Architecture

### Frontend (React)
```typescript
// State Management
const [currentActivityId, setCurrentActivityId] = useState<number | null>(null);
const [isTimeTrackingEnabled, setIsTimeTrackingEnabled] = useState(false);

// API Integration
const startTracking = async () => {
  const activityId = await invoke<number>('start_activity');
  setCurrentActivityId(activityId);
  await startImReadySystem(activityId);
};
```

### Backend (Rust Tauri)
```rust
// Command Structure
#[tauri::command]
async fn start_activity(app: tauri::AppHandle) -> Result<i32, String>

#[tauri::command] 
async fn post_im_ready(app: tauri::AppHandle, activity_id: i32) -> Result<bool, String>

#[tauri::command]
async fn end_activity(/* parameters */) -> Result<bool, String>
```

## System Requirements

### Dependencies
- **Rust**: sysinfo, chrono, reqwest, tokio
- **Frontend**: React, TypeScript, Tauri API
- **Storage**: tauri-plugin-store

### Permissions Required
- Network access for API calls
- System info access for device details
- Local storage for error logging

## Monitoring and Logging

### Console Logging Pattern
```
🚀 [ACTIVITY] Starting activity...
💓 [IMREADY] Sending ImReady for activity ID: 123
📝 [ERROR_LOG] Inserting error log: timestamp=..., error_code=0, activity_id=123
✅ [ACTIVITY] Activity ended successfully
```

### Log Categories
- **ACTIVITY**: Start/End operations
- **IMREADY**: Heartbeat system
- **ERROR_LOG**: Error tracking
- **TIME-TRACKER**: Frontend operations

## Testing Scenarios

### Manual Testing Checklist
1. ✅ Start activity with internet connection
2. ✅ Verify ImReady heartbeats every 10 seconds
3. ✅ Disconnect internet during tracking
4. ✅ Verify error logging during offline period
5. ✅ Reconnect internet and verify recovery
6. ✅ End activity and verify error count
7. ✅ Update activity via modal
8. ✅ Verify cleanup of error logs

### Error Simulation
- Network disconnection during tracking
- Server downtime simulation
- Authentication token expiry
- Activity end before ImReady completion

## Performance Considerations

### Optimization Features
- **Lazy Loading**: Projects/tasks loaded on-demand
- **Error Batching**: Local storage prevents API spam
- **Timeout Configuration**: 30s for critical calls, 10s for heartbeats
- **Memory Management**: Interval cleanup on component unmount

### Resource Usage
- **Network**: Minimal heartbeat every 10 seconds
- **Storage**: Error logs cleaned after successful end
- **CPU**: Lightweight system info collection
- **Memory**: Efficient state management

## Security Implementation

### Authentication
- Token stored securely in Tauri store
- Header-based API authentication
- Automatic logout on 403 errors

### Data Protection
- Local error logs contain no sensitive data
- System info limited to necessary fields
- Secure token transmission

## Future Enhancements

### Planned Features
- Real IP address detection
- Advanced offline mode
- Bulk error log reporting
- Activity pause/resume functionality
- Enhanced system monitoring

### Scalability Considerations
- Multiple activity support
- Team synchronization
- Advanced analytics integration
- Cross-platform compatibility

---

**Last Updated**: January 2024  
**Version**: 1.0  
**Status**: Production Ready
