# Centrifugo Client Integration Documentation

## Overview

This document describes how to integrate with the Centrifugo WebSocket service to receive real-time employee status notifications. The integration is language-agnostic and can be implemented in any programming language that supports WebSocket connections.

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client App    │◄──►│  Centrifugo      │◄──►│  Backend API    │
│  (JS/Python/    │    │  WebSocket       │    │  (Django)       │
│   Rust/etc.)    │    │  Server          │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Connection Flow

### 1. Authentication Token Generation

Before connecting to Centrifugo, clients must obtain a JWT token from the backend API.

**Endpoint:** `GET /api/account/centrifugo/token/`

**Headers:**
```
Authorization: Bearer <user_auth_token>
```

**Response:**
```json
{
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user_id": 123,
    "ws_url": "wss://centrifugo.newhorizonco.uk/connection/websocket",
    "expires_in": 86400
}
```

### 2. WebSocket Connection

Connect to the Centrifugo WebSocket server using the provided URL and token.

**WebSocket URL:** `wss://centrifugo.newhorizonco.uk/connection/websocket`

### 3. Authentication Message

After WebSocket connection is established, send authentication message:

```json
{
    "id": 1,
    "connect": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "name": "client-name"
    }
}
```

**Expected Response:**
```json
{
    "id": 1,
    "connect": {
        "client": "client-id-here",
        "version": "5.0.0",
        "expires": true,
        "ttl": 86400
    }
}
```

### 4. Channel Subscription

Subscribe to your personal user channel:

```json
{
    "id": 2,
    "subscribe": {
        "channel": "teamby:user#<user_id>"
    }
}
```

**Expected Response:**
```json
{
    "id": 2,
    "subscribe": {
        "channel": "teamby:user#<user_id>",
        "positioned": false,
        "recoverable": false
    }
}
```

## Channel Structure

### Personal User Channel
- **Format:** `teamby:user#<user_id>`
- **Purpose:** Receive notifications specific to the authenticated user
- **Example:** `teamby:user#31`

Each user subscribes only to their own personal channel. The backend determines which users should receive notifications based on team membership and permissions.

## Message Types

### Employee Online Notification

Sent when an employee starts their work time (creates a TimeEntry).

```json
{
    "push": {
        "channel": "teamby:user#31",
        "pub": {
            "data": {
                "type": "employee_online",
                "timestamp": 1703123456789,
                "employee_id": 123,
                "employee_name": "John Doe",
                "status": "online"
            }
        }
    }
}
```

### Employee Offline Notification

Sent when an employee ends their work time (sets end_time on TimeEntry).

```json
{
    "push": {
        "channel": "teamby:user#31",
        "pub": {
            "data": {
                "type": "employee_offline",
                "timestamp": 1703123456789,
                "employee_id": 123,
                "employee_name": "John Doe",
                "status": "offline"
            }
        }
    }
}
```

## Client Implementation Requirements

### 1. Connection Management

- **Auto-reconnection:** Implement automatic reconnection with exponential backoff
- **Token refresh:** Handle token expiration and refresh tokens before they expire
- **Connection state:** Track connection state (connecting, connected, disconnected)

### 2. Message Handling

- **JSON parsing:** All messages are JSON formatted
- **Message filtering:** Filter messages by channel and message type
- **Error handling:** Handle malformed messages gracefully

### 3. Event Handling

Clients should handle these events:

- **Connection established**
- **Connection lost**
- **Authentication success/failure**
- **Subscription success/failure**
- **Employee online/offline notifications**

### 4. Security Considerations

- **Token storage:** Store JWT tokens securely (not in localStorage for web clients)
- **Token validation:** Validate token expiration before use
- **HTTPS only:** Always use secure WebSocket connections (wss://)

## Service Module Structure

### Core Components

```
CentrifugoService/
├── Connection Manager
│   ├── WebSocket connection handling
│   ├── Auto-reconnection logic
│   └── Connection state management
├── Authentication Manager
│   ├── Token retrieval from API
│   ├── Token refresh handling
│   └── Authentication message sending
├── Subscription Manager
│   ├── Channel subscription
│   ├── Subscription state tracking
│   └── Unsubscription handling
├── Message Handler
│   ├── JSON message parsing
│   ├── Message type routing
│   └── Event emission
└── Event Emitter
    ├── Connection events
    ├── Employee status events
    └── Error events
```

### Required Methods

#### Connection Methods
- `connect()` - Establish WebSocket connection
- `disconnect()` - Close WebSocket connection
- `reconnect()` - Reconnect with backoff strategy
- `isConnected()` - Check connection status

#### Authentication Methods
- `getToken()` - Retrieve JWT token from API
- `authenticate()` - Send authentication message
- `refreshToken()` - Refresh expired token

#### Subscription Methods
- `subscribe(channel)` - Subscribe to channel
- `unsubscribe(channel)` - Unsubscribe from channel
- `getSubscriptions()` - Get active subscriptions

#### Event Methods
- `on(event, callback)` - Register event listener
- `off(event, callback)` - Remove event listener
- `emit(event, data)` - Emit event (internal use)

### Event Types

#### Connection Events
- `connecting` - Connection attempt started
- `connected` - Connection established
- `disconnected` - Connection lost
- `error` - Connection error occurred

#### Employee Status Events
- `employee_online` - Employee came online
- `employee_offline` - Employee went offline

## Error Handling

### Common Error Scenarios

1. **Network connectivity issues**
2. **Invalid or expired tokens**
3. **Server unavailable**
4. **Malformed messages**
5. **Subscription failures**

### Error Response Format

```json
{
    "error": {
        "code": 2,
        "message": "transport closed"
    },
    "type": "transport"
}
```

### Recommended Error Handling Strategy

1. **Log all errors** for debugging
2. **Retry with exponential backoff** for network errors
3. **Refresh tokens** for authentication errors
4. **Notify user** for persistent connection issues
5. **Graceful degradation** when real-time features are unavailable

## Testing

### Test Scenarios

1. **Successful connection and subscription**
2. **Token expiration and refresh**
3. **Network disconnection and reconnection**
4. **Invalid token handling**
5. **Message parsing and event emission**

### Test Tools

- **Backend test command:** `python manage.py test_centrifugo online --employee-id 1`
- **WebSocket testing tools:** websocat, wscat, online WebSocket testers
- **Network simulation:** Simulate network failures and recovery

## Configuration

### Environment Variables

```bash
CENTRIFUGO_WS_URL=wss://centrifugo.newhorizonco.uk/connection/websocket
CENTRIFUGO_API_URL=http://localhost:8000/api/account/centrifugo/token/
```

### Client Configuration Options

```javascript
const config = {
    wsUrl: 'wss://centrifugo.newhorizonco.uk/connection/websocket',
    apiUrl: '/api/account/centrifugo/token/',
    reconnectInterval: 1000,
    maxReconnectAttempts: 10,
    tokenRefreshThreshold: 300, // seconds before expiry
    debug: false
};
```

## Best Practices

1. **Connection pooling:** Reuse connections when possible
2. **Message queuing:** Queue messages during disconnection
3. **Heartbeat/ping:** Implement keep-alive mechanism
4. **Resource cleanup:** Properly close connections and remove listeners
5. **Performance monitoring:** Track connection metrics and message latency
6. **User feedback:** Provide visual indicators for connection status
7. **Offline handling:** Handle offline scenarios gracefully

## Security Notes

- JWT tokens contain user information and should be handled securely
- Always use HTTPS/WSS in production
- Implement proper CORS policies on the server
- Validate all incoming messages before processing
- Log security-related events for monitoring

## Support and Troubleshooting

### Common Issues

1. **"transport closed" error:** Check server availability and network connectivity
2. **Authentication failures:** Verify token validity and API endpoint
3. **Subscription failures:** Ensure proper channel format and permissions
4. **Message not received:** Check channel subscription and message filtering

### Debug Mode

Enable debug mode to see detailed connection and message logs:

```javascript
const client = new CentrifugoClient({
    debug: true
});
```

This will output detailed information about connection attempts, message exchanges, and error conditions.
