# User Profile Error Handling & UX Improvements

## Overview
This document describes the comprehensive error handling and user experience improvements implemented for the user profile fetching system.

## Features Implemented

### 1. 403 Error Handling (Authentication Failure)
When the `get_user_profile` API call returns a 403 error:
- **Automatic Logout**: Clears all user-related stores in Tauri backend
- **Store Cleanup**: Removes `auth.json` and `user_profile.json` files
- **Context Cleanup**: Clears authentication token and user profile data from React contexts
- **Error Code**: Returns `AUTH_EXPIRED` error code for React to handle appropriately

**Implementation:**
```rust
// Tauri backend - src-tauri/src/lib.rs
403 => {
    println!("🚫 [PROFILE] Permission denied (403) - Invalid token, clearing stores...");
    let _ = clear_user_stores(app.clone()).await;
    Err("AUTH_EXPIRED".to_string())
}
```

### 2. Non-403 Error Handling
For any error other than 403:
- **Toast Notifications**: Display user-friendly error messages
- **Automatic Retry**: Retry the profile fetch every 2 seconds until successful
- **No User Disruption**: Maintains current UI state while retrying in background

**Implementation:**
```typescript
// React UserContext - src/context/UserContext.tsx
if (errorMessage === 'AUTH_EXPIRED') {
    // Handle 403 - clear everything
} else {
    // Show toast and retry
    showError('Profile Loading Failed', 'Retrying automatically...', 3000);
    retryTimeoutRef.current = setTimeout(() => {
        fetchUserProfile(true); // Retry
    }, 2000);
}
```

### 3. Cached Profile Loading
Improved startup experience:
- **Immediate Display**: Load cached profile from Tauri store on app startup
- **Offline Indicator**: Show "Offline" status for cached data
- **Seamless Transition**: Update to fresh data when API call succeeds
- **Fallback Handling**: Gracefully handle missing or corrupted cache

**Implementation:**
```typescript
// Load cached profile first, then fetch fresh data
loadCachedProfile().then(() => {
    fetchUserProfile();
});
```

### 4. Toast Notification System
New toast notification system for error display:
- **Multiple Types**: Success, Error, Warning, Info
- **Auto-dismiss**: Configurable duration (default 5 seconds)
- **Slide Animation**: Smooth slide-in from right, slide-out on dismiss
- **Positioning**: Appears below TopBar (top-right corner)
- **Manual Dismiss**: Click X button to close immediately

**Components:**
- `ToastContext.tsx`: Context provider for toast management
- `Toast.tsx`: Toast display component with animations
- `ToastContainer`: Container for rendering multiple toasts

### 5. Enhanced UI Loading States
Better loading indicators and state management:
- **Skeleton Loading**: Show skeleton components while loading
- **Cached Data Indicator**: Visual indication when data is from cache
- **Status Synchronization**: Offline status for cached data, online for fresh data
- **Smooth Transitions**: No jarring UI changes during data updates

## New Tauri Commands

### `get_cached_user_profile`
```rust
#[tauri::command]
async fn get_cached_user_profile(app: tauri::AppHandle) -> Result<Option<UserProfile>, String>
```
Retrieves cached user profile from `user_profile.json` store.

### `clear_user_stores`
```rust
#[tauri::command]
async fn clear_user_stores(app: tauri::AppHandle) -> Result<(), String>
```
Clears both `auth.json` and `user_profile.json` stores (used for 403 errors).

## Error Codes

| Error Code | Description | Handling |
|------------|-------------|----------|
| `AUTH_EXPIRED` | 403 authentication failure | Clear stores, logout user |
| `INVALID_TOKEN` | Token format invalid | Clear stores, logout user |
| `NO_TOKEN` | No token found | Clear stores, logout user |
| `STORE_ACCESS_ERROR` | Cannot access Tauri store | Clear stores, logout user |
| Other errors | Network, parsing, server errors | Show toast, retry automatically |

## User Experience Flow

### Startup Flow
1. **App Starts** → Check authentication
2. **If Authenticated** → Load cached profile immediately
3. **Display Cached Data** → Show with "Offline" status
4. **Fetch Fresh Data** → API call in background
5. **Update UI** → Replace cached data with fresh data, update status

### Error Scenarios

#### 403 Authentication Error
1. **API Returns 403** → Tauri clears all stores
2. **React Receives AUTH_EXPIRED** → Clear contexts
3. **User Redirected** → Back to login page
4. **No Toast Shown** → Silent logout for security

#### Network/Server Error
1. **API Fails** → Show error toast
2. **Retry Automatically** → Every 2 seconds
3. **Success on Retry** → Update UI, hide toast
4. **User Uninterrupted** → Can continue using app

## Configuration

### Toast Duration
```typescript
showError('Title', 'Message', 3000); // 3 seconds
```

### Retry Interval
```typescript
setTimeout(() => {
    fetchUserProfile(true);
}, 2000); // 2 seconds
```

### Cache Behavior
- Cached data shown immediately on startup
- Fresh data fetched in background
- Cache updated when fresh data received
- Cache cleared on authentication errors

## Testing

Comprehensive test suite covers:
- Cached profile loading
- 403 error handling with store cleanup
- Retry mechanism for non-403 errors
- Toast notification display
- User data clearing
- Error state management

Run tests:
```bash
npm test src/context/__tests__/UserContext.test.tsx
```

## Benefits

1. **Better UX**: Immediate profile display from cache
2. **Resilient**: Automatic retry on temporary failures
3. **Secure**: Proper cleanup on authentication failures
4. **Informative**: Clear error messages via toasts
5. **Smooth**: No jarring UI changes during loading
6. **Reliable**: Comprehensive error handling for all scenarios
