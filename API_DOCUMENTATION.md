# TeamBy Desktop - API Documentation

این مستند شامل تمام API هایی است که توسط TeamBy Desktop فراخوانی می‌شوند.

## 🌐 Base URL
```
http://127.0.0.1:8000
```

## 🔐 Authentication
تمام API ها (به جز Login) نیاز به authentication دارند:
- **Header**: `X-Api-Key: {token}`

## 📋 API Endpoints

### 1. Authentication APIs

#### 1.1 Login
```http
POST /api/v2/employees/signin/
```

**Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (200):**
```json
{
  "success": true,
  "token": "auth_token_here",
  "message": "Login successful"
}
```

**Response (400/401):**
```json
{
  "success": false,
  "message": "Invalid email or password"
}
```

---

### 2. Profile APIs

#### 2.1 Get User Profile
```http
GET /api/v2/employees/profile/
```

**Headers:**
```
X-Api-Key: {token}
version: 2
Content-Type: application/json
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "full_name": "John Doe",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "position": "Developer",
    "company": "TeamBy",
    "is_admin": false,
    "screen_active": true,
    "incomplete_activity_id": 123
  },
  "message": "Profile fetched successfully"
}
```

**Response (401/403):**
```json
{
  "success": false,
  "message": "Authentication expired"
}
```

---

### 3. Projects APIs

#### 3.1 Get Projects
```http
GET /api/projects/
```

**Headers:**
```
X-Api-Key: {token}
version: 2
Content-Type: application/json
```

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Project Alpha"
    },
    {
      "id": 2,
      "name": "Project Beta"
    }
  ],
  "message": "Projects fetched successfully"
}
```

#### 3.2 Get Project Tasks
```http
GET /api/projects/{project_id}/targets/
```

**Headers:**
```
X-Api-Key: {token}
version: 2
Content-Type: application/json
```

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Task 1"
    },
    {
      "id": 2,
      "name": "Task 2"
    }
  ],
  "message": "Tasks fetched successfully"
}
```

---

### 4. Activity APIs

#### 4.1 Start Activity
```http
POST /api/activity/start/
```

**Headers:**
```
X-Api-Key: {token}
version: 2
Content-Type: application/json
```

**Request Body:**
```json
{
  "project_id": 1,
  "task_id": 2,
  "system_local_time": "2024-07-12T22:30:00Z",
  "platform": "Linux",
  "device_name": "hostname",
  "device_os": "Ubuntu 22.04",
  "version": "2"
}
```

**Response (200):**
```json
{
  "id": 123,
  "start_time": "2024-07-12T22:30:00Z",
  "last_ready": 1720825800,
  "end_time": null,
  "proposed_start_time": null,
  "proposed_end_time": null,
  "edit_status": "active",
  "edit_reason": null,
  "edit_log": null,
  "price": null,
  "type": "work",
  "reports_audios": null,
  "report_text": null,
  "report_score": null,
  "report_improvement_suggestion": null,
  "jira_task_id": null,
  "jira_task_name": null,
  "system_local_time": "2024-07-12T22:30:00Z",
  "platform": "Linux",
  "device_name": "hostname",
  "device_os": "Ubuntu 22.04",
  "version": "2",
  "is_activity_submitted": false,
  "employee": 1,
  "project": 1,
  "project_goal": null,
  "target_goal": null
}
```

#### 4.2 ImReady Heartbeat
```http
GET /api/activity/{activity_id}/imready/
```

**Headers:**
```
X-Api-Key: {token}
version: 2
Content-Type: application/json
```

**Response (200):**
```json
{
  "status": "success",
  "message": "Activity updated successfully."
}
```

**Response (400):**
```json
{
  "status": "error",
  "message": "Activity already ended"
}
```

**Response (401/403):**
```json
{
  "status": "error",
  "message": "Authentication failed"
}
```

**Error Handling:**
- Network errors: ذخیره در local storage، retry هر 10 ثانیه
- Activity ended: متوقف کردن ImReady system
- Auth errors: ادامه retry (کاربر ممکن است دوباره login کند)

#### 4.3 End Activity
```http
PUT /api/activity/end/
```

**Headers:**
```
X-Api-Key: {token}
version: 2
Content-Type: application/json
```

**Request Body:**
```json
{
  "activity_id": 123,
  "project_id": 1,
  "task_id": 2,
  "notes": "Activity completion notes",
  "duration": "02:30:45",
  "imready_error_logs_count": 3,
  "system_local_time": "2024-07-12T23:30:00Z",
  "platform": "Linux",
  "device_name": "hostname",
  "device_os": "Ubuntu 22.04",
  "version": "2"
}
```

**Fields Description:**
- `activity_id`: ID دریافت شده از start_activity
- `project_id`, `task_id`: پروژه و task انتخابی
- `notes`: یادداشت‌های کاربر (اختیاری)
- `duration`: مدت زمان فعالیت (محاسبه شده در frontend)
- `imready_error_logs_count`: تعداد خطاهای ImReady در طول فعالیت

**Response (200):**
```json
{
  "success": true,
  "message": "Activity ended successfully"
}
```

**Post-Success Actions:**
- تمام error logs از local storage پاک می‌شوند (`clear_all_error_logs`)

#### 4.4 Update Activity
```http
PUT /api/activity/update/
```

**Headers:**
```
X-Api-Key: {token}
version: 2
Content-Type: application/json
```

**Request Body:**
```json
{
  "activity_id": 123,
  "project_id": 1,
  "task_id": 2,
  "notes": "Updated activity notes after completion",
  "imready_error_logs_count": 3,
  "system_local_time": "2024-07-12T23:30:00Z",
  "platform": "Linux",
  "device_name": "hostname",
  "device_os": "Ubuntu 22.04",
  "version": "2"
}
```

**Fields Description:**
- `imready_error_logs_count`: تعداد خطاهای ImReady (یا 0 اگر قبلاً clear شده باشد)

**Response (200):**
```json
{
  "success": true,
  "message": "Activity updated successfully"
}
```

**Post-Success Actions:**
- تمام error logs از local storage پاک می‌شوند (`clear_all_error_logs`)

**Response (401/403):**
```json
{
  "success": false,
  "message": "Authentication expired"
}
```

---

## 🔧 Common Headers

### Standard Headers (تمام API ها):
```
Content-Type: application/json
```

### Authenticated Headers (همه به جز Login):
```
X-Api-Key: {authentication_token}
version: 2
```

## ⚠️ Error Handling

### Network Errors:
- **Connection Refused**: `NETWORK_CONNECTION_FAILED`
- **Timeout**: `NETWORK_CONNECTION_FAILED`

### HTTP Status Codes:
- **200**: Success
- **400**: Bad Request
- **401**: Unauthorized
- **403**: Authentication Expired (`AUTH_EXPIRED`)
- **500**: Server Error

## 🕐 Timeouts

- **Login API**: 30 seconds
- **Profile API**: 30 seconds  
- **Projects API**: 30 seconds
- **Tasks API**: 30 seconds
- **Activity Start**: 30 seconds
- **Activity End**: 30 seconds
- **Activity Update**: 30 seconds
- **ImReady**: 10 seconds

## 📝 Notes

1. تمام timestamps در فرمت ISO 8601 ارسال می‌شوند
2. فیلد `version` همیشه "2" است
3. فیلدهای `platform`, `device_name`, `device_os` از system info گرفته می‌شوند
4. ImReady هر 10 ثانیه یکبار ارسال می‌شود
5. در صورت خطای 401/403، کاربر logout می‌شود
6. Error logs در local storage ذخیره می‌شوند و با end_activity ارسال می‌شوند
7. Modal submission از update_activity استفاده می‌کند

## 🔄 API Call Flow

### Application Startup:
1. **check_auth** - بررسی وضعیت authentication از store
2. **get_cached_user_profile** - بارگیری profile از cache
3. **get_user_profile** - دریافت profile جدید از server (اگر authenticated باشد)

### Login Process:
1. **login** - ارسال credentials
2. **get_user_profile** - دریافت profile کاربر
3. Store token و profile در local storage

### Time Tracking Complete Flow:
1. **fetch_projects** - دریافت لیست پروژه‌ها
2. **fetch_project_tasks** - دریافت tasks پروژه انتخابی
3. **start_activity** - شروع فعالیت
4. **post_im_ready** - ارسال heartbeat هر 10 ثانیه (تا زمان stop)
5. **end_activity** - پایان فعالیت (شامل error logs count)
6. **Modal Display** - نمایش modal برای review و ویرایش
7. **update_activity** - ذخیره تغییرات از modal (project, task, notes)

## 🏗️ System Information

### Platform Detection:
```rust
// در request body ارسال می‌شود:
{
  "platform": "Linux",           // OS type
  "device_name": "hostname",      // Computer name
  "device_os": "Ubuntu 22.04",   // OS version
  "version": "2"                  // API version
}
```

### Timestamp Format:
```rust
// همه timestamps در فرمت ISO 8601:
"system_local_time": "2024-07-12T22:30:00Z"
```

## 🔒 Security

### Token Storage:
- Tokens در `auth.json` store ذخیره می‌شوند
- Profile data در `user.json` store ذخیره می‌شود

### Auto Logout:
- در صورت دریافت 401/403، automatic logout انجام می‌شود
- تمام stores پاک می‌شوند

## 🚀 Performance

### Caching Strategy:
- Profile data cache می‌شود
- در صورت عدم دسترسی به network، از cache استفاده می‌شود
- Offline profile ایجاد می‌شود اگر server در دسترس نباشد

### Retry Logic:
- Network errors منجر به retry نمی‌شوند
- User باید manually retry کند

## 📊 Logging

### Request Logging:
```
📡 [CONTEXT] Sending METHOD request to: URL
🔢 [CONTEXT] API Version: 2
```

### Response Logging:
```
📨 [CONTEXT] Received response with status: STATUS_CODE
```

### Error Logging:
```
❌ [CONTEXT] Error message
```

## 🧪 Testing

### Mock Data:
- برای testing، mock services در `src/shared/services/` موجود است
- ApiClient interface برای dependency injection استفاده می‌شود

### Error Simulation:
- Network errors با قطع کردن server قابل تست است
- Authentication errors با invalid token قابل تست است
