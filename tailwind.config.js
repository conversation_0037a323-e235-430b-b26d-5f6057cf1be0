import { heroui } from "@heroui/react";

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: "class",
  theme: {
    extend: {
      colors: {
        'custom-background': 'var(--custom-background)',
        'custom-card': 'var(--custom-card)',
        'custom-sidebar': 'var(--custom-sidebar)',
        'custom-border': 'var(--custom-border)',
        'custom-text': 'var(--custom-text)',
        'custom-muted': 'var(--custom-muted)',
        'custom-body': 'var(--custom-body)',
        'custom-primary': 'var(--custom-primary)',
      },
      fontFamily: {
        'inter': ['Inter', 'sans-serif'],
      },
    },
  },
  plugins: [
    heroui({
      components: {
        tooltip: {
          baseClasses: {
            base: "bg-[#1A1D2B] text-white",
            content: "text-white",
          },
        },
        dropdown: {
          baseClasses: {
            button: "",
            items: "bg-[#1A1D2B] text-white border border-[#2A2D3C] shadow-xl rounded-lg w-64 py-2",
            item: "px-4 py-2 text-sm text-white hover:bg-[#2A2D3C] cursor-pointer",
            icon: "mr-2",
          },
        },
      },
    }),
  ],
}
