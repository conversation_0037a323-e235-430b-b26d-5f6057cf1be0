[package]
name = "app"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
license = ""
repository = ""
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.2.0", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
log = "0.4"
tauri = { version = "2.5.0", features = [] }
tauri-plugin-log = "2.0.0-rc"
tauri-plugin-store = "2"
tauri-plugin-global-shortcut = "2"
tauri-plugin-os = "2"
tauri-plugin-shell = "2"
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.12", features = ["json", "rustls-tls"], default-features = false }
tauri-plugin-system-info = "2.0.9"
urlencoding = "2.1"
chrono = { version = "0.4.41", features = ["serde"] }
sysinfo = "0.36.0"
hostname = "0.4"
tokio-centrifuge = { git = "https://github.com/IntrepidAI/tokio-centrifuge.git", default-features = false, features = ["rustls-tls-webpki-roots"] }
futures-util = "0.3"
