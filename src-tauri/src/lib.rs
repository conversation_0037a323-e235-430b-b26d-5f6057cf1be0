use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Emitter};
use std::sync::Mutex;
use std::time::{Duration, Instant};

use serde_json::json;

// Import modules
mod modules;

// Re-export module functions
use modules::auth::*;
use modules::profile::*;
use modules::projects::*;
use modules::activity::*;
use modules::error_logs::*;
use modules::centrifugo::*;
use modules::livekit::*;

// Debug logging command for React
#[tauri::command]
async fn debug_log(message: String, tag: String) -> Result<(), String> {
    println!("🔍 [REACT-DEBUG] [{}] {}", tag, message);
    Ok(())
}

// Window management commands
#[tauri::command]
async fn minimize_window(app: AppHandle) -> Result<(), String> {
    if let Some(window) = app.get_webview_window("main") {
        window.minimize().map_err(|e| e.to_string())?;
    }
    Ok(())
}

#[tauri::command]
async fn maximize_window(app: AppHandle) -> Result<(), String> {
    if let Some(window) = app.get_webview_window("main") {
        if window.is_maximized().unwrap_or(false) {
            window.unmaximize().map_err(|e| e.to_string())?;
        } else {
            window.maximize().map_err(|e| e.to_string())?;
        }
    }
    Ok(())
}

#[tauri::command]
async fn close_window(app: AppHandle) -> Result<(), String> {
    if let Some(window) = app.get_webview_window("main") {
        window.close().map_err(|e| e.to_string())?;
    }
    Ok(())
}

#[tauri::command]
async fn is_window_maximized(app: AppHandle) -> Result<bool, String> {
    if let Some(window) = app.get_webview_window("main") {
        Ok(window.is_maximized().unwrap_or(false))
    } else {
        Ok(false)
    }
}

// Global state for reload debouncing
static LAST_RELOAD_TIME: Mutex<Option<Instant>> = Mutex::new(None);

#[tauri::command]
async fn reload_window(app: AppHandle) -> Result<(), String> {
    // Debouncing: prevent multiple reloads within 3 seconds
    {
        let mut last_reload = LAST_RELOAD_TIME.lock().unwrap();
        let now = Instant::now();

        if let Some(last_time) = *last_reload {
            if now.duration_since(last_time) < Duration::from_secs(3) {
                println!("⚠️ [WINDOW] Restart request ignored - too frequent (debounced within 3 seconds)");
                return Ok(());
            }
        }

        *last_reload = Some(now);
    }

    println!("🔄 [WINDOW] Restarting application...");

    // Check if we're in development mode
    let is_dev = cfg!(debug_assertions);

    if is_dev {
        println!("🔧 [WINDOW] Development mode detected - using window reload instead of app restart");

        // In development mode, use a more comprehensive reload
        if let Some(window) = app.get_webview_window("main") {
            println!("🔄 [WINDOW] Performing comprehensive window reload...");

            // Method 1: Try JavaScript reload with cache clear
            match window.eval("
                try {
                    console.log('🔄 [TAURI] Starting comprehensive reload...');
                    // Clear all caches and reload
                    if ('serviceWorker' in navigator) {
                        navigator.serviceWorker.getRegistrations().then(function(registrations) {
                            for(let registration of registrations) {
                                registration.unregister();
                            }
                        });
                    }
                    // Clear localStorage and sessionStorage
                    localStorage.clear();
                    sessionStorage.clear();
                    // Force reload from server
                    window.location.reload(true);
                } catch (e) {
                    console.error('❌ [TAURI] Reload error:', e);
                }
            ") {
                Ok(_) => {
                    println!("✅ [WINDOW] Comprehensive reload initiated successfully");
                    Ok(())
                }
                Err(e) => {
                    println!("❌ [WINDOW] Failed to reload window: {:?}", e);

                    // Fallback: Hide and show window
                    println!("🔄 [WINDOW] Trying fallback method (hide/show)...");
                    match window.hide() {
                        Ok(_) => {
                            tokio::time::sleep(Duration::from_millis(200)).await;
                            match window.show() {
                                Ok(_) => {
                                    println!("✅ [WINDOW] Window refreshed via hide/show");
                                    Ok(())
                                }
                                Err(e2) => {
                                    println!("❌ [WINDOW] Fallback method also failed: {:?}", e2);
                                    Err(format!("All reload methods failed: {} | {}", e, e2))
                                }
                            }
                        }
                        Err(e2) => {
                            println!("❌ [WINDOW] Fallback method failed: {:?}", e2);
                            Err(format!("Failed to reload window: {} | {}", e, e2))
                        }
                    }
                }
            }
        } else {
            println!("❌ [WINDOW] Main window not found");
            Err("Main window not found".to_string())
        }
    } else {
        println!("🚀 [WINDOW] Production mode - performing full app restart");

        // Add small delay to ensure any pending operations complete
        tokio::time::sleep(Duration::from_millis(100)).await;

        // Restart the entire application - this will never return
        app.restart();
    }
}

// Splash screen commands
#[tauri::command]
async fn close_splashscreen(app: AppHandle) -> Result<(), String> {
    if let Some(splashscreen) = app.get_webview_window("splashscreen") {
        splashscreen.close().map_err(|e| e.to_string())?;
    }

    if let Some(main_window) = app.get_webview_window("main") {
        main_window.show().map_err(|e| e.to_string())?;
        main_window.set_focus().map_err(|e| e.to_string())?;
    }

    Ok(())
}

// Screen view window commands
#[tauri::command]
async fn open_screen_view_window(app: AppHandle, employee_data: String) -> Result<(), String> {
    println!("🔧 [WINDOW] Opening screen view window with data: {}", &employee_data[..std::cmp::min(100, employee_data.len())]);

    // Always close existing window first to ensure fresh data
    if let Some(existing_window) = app.get_webview_window("screen-view") {
        println!("🗑️ [WINDOW] Closing existing screen view window to refresh data");
        let _ = existing_window.close();
        // Small delay to ensure window is fully closed
        tokio::time::sleep(std::time::Duration::from_millis(100)).await;
    }

    // Create new window with fresh data (no URL parameters for Tauri)
    println!("🔧 [WINDOW] Creating new screen view window");

    use tauri::{WebviewUrl, WebviewWindowBuilder};

    println!("🔗 [WINDOW] Screen view URL: screen-view-tauri.html (data will be passed directly)");

    let screen_view_window = WebviewWindowBuilder::new(
        &app,
        "screen-view",
        WebviewUrl::App("screen-view-tauri.html".into())
    )
    .title("TeamBy - Screen View")
    .inner_size(900.0, 600.0)  // Updated to match tauri.conf.json
    .min_inner_size(800.0, 500.0)  // Updated to match tauri.conf.json
    .resizable(true)
    .fullscreen(false)
    .decorations(false)
    .center()
    .transparent(true)
    .visible(false)
    .build()
    .map_err(|e| {
        println!("❌ [WINDOW] Failed to create screen view window: {}", e);
        e.to_string()
    })?;

    // Show the window after creation
    screen_view_window.show().map_err(|e| e.to_string())?;
    screen_view_window.set_focus().map_err(|e| e.to_string())?;

    // Send employee data directly to the window via event
    println!("📤 [WINDOW] Sending employee data to screen view window");
    screen_view_window.emit("employee-data", &employee_data).map_err(|e| {
        println!("❌ [WINDOW] Failed to send employee data: {}", e);
        e.to_string()
    })?;

    println!("✅ [WINDOW] Screen view window created and data sent successfully");

    Ok(())
}

#[tauri::command]
async fn get_platform() -> Result<String, String> {
    let platform_name = std::env::consts::OS;
    println!("🖥️ [PLATFORM] Detected platform: {}", platform_name);
    Ok(platform_name.to_string())
    // Ok("windows".to_string())

}

#[tauri::command]
async fn open_in_browser(url: String) -> Result<(), String> {
    println!("🌐 [BROWSER] Opening URL in browser: {}", &url[..std::cmp::min(100, url.len())]);

    use std::process::Command;

    // Check if this is a LiveKit browser URL
    let is_livekit_url = url.contains("livekit-browser");

    let result = if cfg!(target_os = "linux") {
        if is_livekit_url {
            println!("🖥️ [BROWSER] Opening LiveKit URL with custom window size");
            // Try Chrome with incognito mode to force new window
            Command::new("google-chrome")
                .args([
                    "--incognito",
                    "--new-window",
                    "--window-size=800,600",
                    "--window-position=100,100",
                    "--app-shell-host-window-size=800x600",
                    &url
                ])
                .spawn()
                .or_else(|_| {
                    // Try Chrome with temporary user data directory
                    println!("🔄 [BROWSER] Trying Chrome with temporary profile...");
                    Command::new("google-chrome")
                        .args([
                            "--user-data-dir=/tmp/chrome-livekit",
                            "--new-window",
                            "--window-size=800,600",
                            "--window-position=100,100",
                            &url
                        ])
                        .spawn()
                })
                .or_else(|_| {
                    // Fallback to Firefox with new window
                    println!("🔄 [BROWSER] Chrome not found, trying Firefox...");
                    Command::new("firefox")
                        .args([
                            "--new-window",
                            "--width=800",
                            "--height=600",
                            &url
                        ])
                        .spawn()
                })
                .or_else(|_| {
                    // Try Chromium as alternative
                    println!("🔄 [BROWSER] Trying Chromium...");
                    Command::new("chromium-browser")
                        .args([
                            "--incognito",
                            "--new-window",
                            "--window-size=800,600",
                            "--window-position=100,100",
                            &url
                        ])
                        .spawn()
                })
                .or_else(|_| {
                    // Final fallback to default browser
                    println!("🔄 [BROWSER] All browsers failed, using default browser...");
                    Command::new("xdg-open").arg(&url).spawn()
                })
        } else {
            // Use default browser for non-LiveKit URLs
            Command::new("xdg-open").arg(&url).spawn()
        }
    } else if cfg!(target_os = "windows") {
        if is_livekit_url {
            println!("🖥️ [BROWSER] Opening LiveKit URL with custom window size on Windows");
            // Try Chrome with incognito mode
            Command::new("cmd")
                .args(["/C", "start", "", "chrome", "--incognito", "--new-window", "--window-size=800,600", &url])
                .spawn()
                .or_else(|_| {
                    // Try Chrome with temporary profile
                    println!("🔄 [BROWSER] Trying Chrome with temporary profile...");
                    Command::new("cmd")
                        .args(["/C", "start", "", "chrome", "--user-data-dir=%TEMP%\\chrome-livekit", "--new-window", "--window-size=800,600", &url])
                        .spawn()
                })
                .or_else(|_| {
                    // Fallback to default browser
                    println!("🔄 [BROWSER] Chrome not found, using default browser...");
                    Command::new("cmd").args(["/C", "start", &url]).spawn()
                })
        } else {
            Command::new("cmd").args(["/C", "start", &url]).spawn()
        }
    } else if cfg!(target_os = "macos") {
        if is_livekit_url {
            println!("🖥️ [BROWSER] Opening LiveKit URL with custom window size on macOS");
            // Try Chrome with incognito mode
            Command::new("open")
                .args(["-na", "Google Chrome", "--args", "--incognito", "--new-window", "--window-size=800,600", &url])
                .spawn()
                .or_else(|_| {
                    // Try Chrome with temporary profile
                    println!("🔄 [BROWSER] Trying Chrome with temporary profile...");
                    Command::new("open")
                        .args(["-na", "Google Chrome", "--args", "--user-data-dir=/tmp/chrome-livekit", "--new-window", "--window-size=800,600", &url])
                        .spawn()
                })
                .or_else(|_| {
                    // Fallback to default browser
                    println!("🔄 [BROWSER] Chrome not found, using default browser...");
                    Command::new("open").arg(&url).spawn()
                })
        } else {
            Command::new("open").arg(&url).spawn()
        }
    } else {
        return Err("Unsupported platform for opening browser".to_string());
    };

    match result {
        Ok(_) => {
            println!("✅ [BROWSER] URL opened successfully in default browser");
            Ok(())
        }
        Err(e) => {
            println!("❌ [BROWSER] Failed to open URL in browser: {}", e);
            Err(format!("Failed to open browser: {}", e))
        }
    }
}

// Open LiveKit browser window with custom size
#[tauri::command]
async fn open_livekit_browser(url: String) -> Result<(), String> {
    println!("🎥 [LIVEKIT-BROWSER] Opening LiveKit browser window: {}", &url[..std::cmp::min(100, url.len())]);

    use std::process::Command;

    let result = if cfg!(target_os = "linux") {
        // Try Chrome first with optimal LiveKit settings
        Command::new("google-chrome")
            .args([
                "--new-window",
                "--window-size=900,700",
                "--window-position=200,100",
                "--app-auto-launched",
                "--disable-web-security", // For screen sharing permissions
                "--allow-running-insecure-content",
                &url
            ])
            .spawn()
            .or_else(|_| {
                // Fallback to Firefox
                println!("🔄 [LIVEKIT-BROWSER] Chrome not found, trying Firefox...");
                Command::new("firefox")
                    .args([
                        "--new-window",
                        "--width=900",
                        "--height=700",
                        &url
                    ])
                    .spawn()
            })
            .or_else(|_| {
                // Final fallback to default browser
                println!("🔄 [LIVEKIT-BROWSER] Firefox not found, using default browser...");
                Command::new("xdg-open").arg(&url).spawn()
            })
    } else if cfg!(target_os = "windows") {
        // Windows Chrome with LiveKit optimizations
        Command::new("cmd")
            .args([
                "/C", "start", "chrome",
                "--new-window",
                "--window-size=900,700",
                "--disable-web-security",
                "--allow-running-insecure-content",
                &url
            ])
            .spawn()
            .or_else(|_| {
                println!("🔄 [LIVEKIT-BROWSER] Chrome not found, using default browser...");
                Command::new("cmd").args(["/C", "start", &url]).spawn()
            })
    } else if cfg!(target_os = "macos") {
        // macOS Chrome with LiveKit optimizations
        Command::new("open")
            .args([
                "-a", "Google Chrome",
                "--args",
                "--new-window",
                "--window-size=900,700",
                "--disable-web-security",
                "--allow-running-insecure-content",
                &url
            ])
            .spawn()
            .or_else(|_| {
                println!("🔄 [LIVEKIT-BROWSER] Chrome not found, using default browser...");
                Command::new("open").arg(&url).spawn()
            })
    } else {
        return Err("Unsupported platform for opening LiveKit browser".to_string());
    };

    match result {
        Ok(_) => {
            println!("✅ [LIVEKIT-BROWSER] LiveKit browser window opened successfully");
            Ok(())
        }
        Err(e) => {
            println!("❌ [LIVEKIT-BROWSER] Failed to open LiveKit browser: {}", e);
            Err(format!("Failed to open LiveKit browser: {}", e))
        }
    }
}

// Get desktop sources for screen sharing
#[tauri::command]
async fn get_sources() -> Result<Vec<serde_json::Value>, String> {
    println!("🖥️ [SOURCES] Getting desktop sources for screen sharing...");

    // For now, return a mock desktop source
    // In a real implementation, you would enumerate actual desktop sources
    let sources = vec![
        json!({
            "id": "screen:0:0",
            "name": "Entire Screen",
            "type": "screen"
        })
    ];

    println!("✅ [SOURCES] Found {} desktop sources", sources.len());
    Ok(sources)
}

// Close screen view window
#[tauri::command]
async fn close_screen_view_window(app: AppHandle) -> Result<(), String> {
    println!("🗑️ [WINDOW] Closing screen view window...");

    if let Some(window) = app.get_webview_window("screen-view") {
        window.close().map_err(|e| {
            println!("❌ [WINDOW] Failed to close screen view window: {}", e);
            e.to_string()
        })?;
        println!("✅ [WINDOW] Screen view window closed successfully");
    } else {
        println!("⚠️ [WINDOW] Screen view window not found");
    }

    Ok(())
}

// Call window commands
#[tauri::command]
async fn open_call_window(app: AppHandle, call_data: String) -> Result<(), String> {
    println!("📞 [WINDOW] Opening call window with data: {}", &call_data[..std::cmp::min(100, call_data.len())]);

    // Always close existing window first to ensure fresh data
    if let Some(existing_window) = app.get_webview_window("call") {
        println!("🗑️ [WINDOW] Closing existing call window to refresh data");
        let _ = existing_window.close();
        // Longer delay to ensure window is fully closed and resources freed
        tokio::time::sleep(std::time::Duration::from_millis(300)).await;
    }

    // Create new window with fresh data
    println!("🔧 [WINDOW] Creating new call window");

    use tauri::{WebviewUrl, WebviewWindowBuilder};

    println!("🔗 [WINDOW] Call URL: call-tauri.html (data will be passed directly)");

    let call_window = WebviewWindowBuilder::new(
        &app,
        "call",
        WebviewUrl::App("call-tauri.html".into())
    )
    .title("TeamBy - Call")
    .inner_size(400.0, 350.0)  // Modal size for call window
    .min_inner_size(350.0, 300.0)
    .resizable(false)  // Fixed size like a modal
    .fullscreen(false)
    .decorations(false)
    .center()
    .transparent(true)
    .visible(false)
    .always_on_top(true)  // Keep call window on top
    .initialization_script(&format!(
        "window.callData = {};",
        call_data
    ))
    .build()
    .map_err(|e| {
        println!("❌ [WINDOW] Failed to create call window: {}", e);
        e.to_string()
    })?;

    // Wait for DOM to be ready before showing
    tokio::time::sleep(std::time::Duration::from_millis(200)).await;

    // Show the window
    call_window.show().map_err(|e| {
        println!("❌ [WINDOW] Failed to show call window: {}", e);
        e.to_string()
    })?;

    println!("✅ [WINDOW] Call window opened successfully");
    Ok(())
}

// Close call window
#[tauri::command]
async fn close_call_window(app: AppHandle) -> Result<(), String> {
    println!("🗑️ [WINDOW] Closing call window...");

    if let Some(window) = app.get_webview_window("call") {
        window.close().map_err(|e| {
            println!("❌ [WINDOW] Failed to close call window: {}", e);
            e.to_string()
        })?;
        println!("✅ [WINDOW] Call window closed successfully");
        
        // Small delay to ensure proper cleanup
        tokio::time::sleep(std::time::Duration::from_millis(100)).await;
    } else {
        println!("⚠️ [WINDOW] Call window not found");
    }

    Ok(())
}

// Retry call window opening with fallback
#[tauri::command]
async fn retry_call_window(app: AppHandle, call_data: String) -> Result<(), String> {
    println!("🔄 [WINDOW] Retrying call window creation...");
    
    // Close any existing window first
    let _ = close_call_window(app.clone()).await;
    
    // Wait a bit longer before retry
    tokio::time::sleep(std::time::Duration::from_millis(500)).await;
    
    // Try to open again
    open_call_window(app, call_data).await
}

// Main library function
#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        // Global shortcuts temporarily disabled due to conflicts
        // .plugin(tauri_plugin_global_shortcut::init())
        .plugin(tauri_plugin_store::Builder::new().build())
        .plugin(tauri_plugin_system_info::init())
        .plugin(tauri_plugin_os::init())
        .plugin(tauri_plugin_shell::init())
        .setup(|app| {
            // Global shortcuts temporarily disabled due to conflicts
            println!("⌨️ [SHORTCUT] Global shortcuts disabled to avoid conflicts");
            println!("⌨️ [SHORTCUT] Use window controls or menu to restart application");



            // Get window handles
            let splashscreen_window = app.get_webview_window("splashscreen").unwrap();
            let main_window = app.get_webview_window("main").unwrap();

            // Force splash screen to stay open for exactly 9 seconds
            let splashscreen_window_clone = splashscreen_window.clone();
            let main_window_clone = main_window.clone();

            tauri::async_runtime::spawn(async move {
                // Wait exactly 9 seconds - no early closing allowed
                tokio::time::sleep(tokio::time::Duration::from_millis(5000)).await;

                // Force close splash screen and show main window after 9 seconds
                splashscreen_window_clone.close().unwrap();
                main_window_clone.show().unwrap();
                main_window_clone.set_focus().unwrap();
            });

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // Window management
            minimize_window,
            maximize_window,
            close_window,
            is_window_maximized,
            reload_window,
            close_splashscreen,
            open_screen_view_window,
            close_screen_view_window,
            open_call_window,
            close_call_window,
            retry_call_window,

            // Authentication
            clear_auth_token,
            is_authenticated,
            get_stored_token,
            check_auth,
            login,
            logout,
            clear_token,


            // Profile
            get_user_profile,
            get_cached_user_profile,
            clear_user_stores,
            create_offline_profile,
            update_screen_active_status,

            // Projects
            fetch_projects,
            fetch_project_tasks,

            // Employees
            fetch_employees_list,
            fetch_employee_detail,
            validate_screen_share,

            // LiveKit
            get_livekit_token,
            test_livekit_connection,
            request_livekit_token,

            // Activity Stats
            fetch_activity_stats,
            store_activity_stats,
            get_cached_activity_stats,

            // Activity
            start_activity,
            post_im_ready,
            end_activity,
            update_activity,

            // Error logs
            store_error_log,
            get_error_logs,
            clear_all_error_logs,

            // Centrifugo
            start_centrifugo_service,
            stop_centrifugo_service,
            get_centrifugo_status,
            debug_log,

            // Legacy aliases for React compatibility
            insert_error_log,
            delete_error_logs,

            // Platform-specific functionality
            get_platform,
            open_in_browser,
            open_livekit_browser,
            get_sources,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}