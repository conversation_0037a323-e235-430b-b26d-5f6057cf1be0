use tauri::Manager;
use tauri_plugin_store::StoreBuilder;
use tauri_plugin_global_shortcut::GlobalShortcutExt;
use serde::{Deserialize, Serialize};
use reqwest;
use std::time::{Duration, Instant};
use std::sync::Mutex;

#[derive(Serialize, Deserialize)]
struct AuthResponse {
    success: bool,
    token: Option<String>,
    message: String,
}

// User Profile Structures
#[derive(Serialize, Deserialize, Clone, Debug)]
struct UserProfile {
    full_name: String,
    email: String,
    avatar: Option<String>,
    position: Option<String>,
    company: Option<String>,
    is_admin: bool,
    screen_active: bool,
}

// Project and Task Structures
#[derive(Serialize, Deserialize, Clone, Debug)]
struct Project {
    id: i32,
    name: String,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
struct Task {
    id: i32,
    name: String,
}

#[derive(Serialize, Deserialize)]
struct ProjectsResponse {
    success: bool,
    data: Vec<Project>,
    message: String,
}

#[derive(Serialize, Deserialize)]
struct TasksResponse {
    success: bool,
    data: Vec<Task>,
    message: String,
}

// Server response structure (with typo in field name)
#[derive(Serialize, Deserialize, Clone, Debug)]
struct ServerUserProfile {
    full_name: String,
    email: String,
    avatar: Option<String>,
    position: Option<String>,
    company: Option<String>,
    is_admin: bool,
    screan_active: bool, // Note: typo in server response
}

#[derive(Serialize, Deserialize)]
struct UserProfileResponse {
    success: bool,
    data: UserProfile,
    message: String,
}

#[derive(Serialize, Deserialize)]
struct ServerUserProfileResponse {
    success: bool,
    data: ServerUserProfile,
    message: String,
}

// Convert ServerUserProfile to UserProfile
impl From<ServerUserProfile> for UserProfile {
    fn from(server_profile: ServerUserProfile) -> Self {
        UserProfile {
            full_name: server_profile.full_name,
            email: server_profile.email,
            avatar: server_profile.avatar,
            position: server_profile.position,
            company: server_profile.company,
            is_admin: server_profile.is_admin,
            screen_active: server_profile.screan_active, // Fix the typo here
        }
    }
}

#[derive(Serialize, Deserialize)]
struct UserProfileError {
    success: bool,
    message: String,
}

#[derive(Serialize, Deserialize)]
struct LoginRequest {
    email: String,
    password: String,
}

#[derive(Serialize, Deserialize)]
struct ApiResponse {
    success: bool,
    token: Option<String>,
    message: String,
}



// Check if user is authenticated by looking for token in store
#[tauri::command]
async fn check_auth(app: tauri::AppHandle) -> Result<AuthResponse, String> {
    match StoreBuilder::new(&app, std::path::PathBuf::from("auth.json")).build() {
        Ok(store) => {
            match store.get("token") {
                Some(token_value) => {
                    if let Some(token) = token_value.as_str() {
                        if !token.is_empty() {
                            return Ok(AuthResponse {
                                success: true,
                                token: Some(token.to_string()),
                                message: "User is authenticated".to_string(),
                            });
                        }
                    }
                }
                None => {}
            }
        }
        Err(_) => {}
    }

    Ok(AuthResponse {
        success: false,
        token: None,
        message: "User is not authenticated".to_string(),
    })
}

// Login user with real API call
#[tauri::command]
async fn login(app: tauri::AppHandle, email: String, password: String) -> Result<AuthResponse, String> {
    println!("🔐 [AUTH] Starting login attempt for email: {}", email);

    // Create HTTP client with timeout
    let client = reqwest::Client::builder()
        .timeout(Duration::from_secs(30))
        .build()
        .map_err(|e| {
            println!("❌ [AUTH] Failed to create HTTP client: {}", e);
            format!("Failed to create HTTP client: {}", e)
        })?;

    // Prepare login request payload
    let login_request = LoginRequest {
        email: email.clone(),
        password: password.clone()
    };

    println!("📡 [AUTH] Sending request to: http://127.0.0.1:8000/api/v2/employees/signin/");

    // Make API call to signin endpoint
    match client
        .post("http://127.0.0.1:8000/api/v2/employees/signin/")
        .json(&login_request)
        .timeout(Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => {
            let status = response.status();
            println!("📨 [AUTH] Received response with status: {}", status);

            match status.as_u16() {
                200 => {
                    println!("✅ [AUTH] Login successful, parsing response...");
                    // Success - parse response and extract token
                    match response.json::<ApiResponse>().await {
                        Ok(api_response) => {
                            println!("📋 [AUTH] API Response: success={}, message={}", api_response.success, api_response.message);
                            if let Some(token) = api_response.token {
                                println!("🔑 [AUTH] Token received, storing in local storage...");
                                // Store token in Tauri store
                                match StoreBuilder::new(&app, std::path::PathBuf::from("auth.json")).build() {
                                    Ok(store) => {
                                        store.set("token", serde_json::Value::String(token.clone()));
                                        let _ = store.save();
                                        println!("💾 [AUTH] Token stored successfully");
                                        Ok(AuthResponse {
                                            success: true,
                                            token: Some(token),
                                            message: api_response.message,
                                        })
                                    }
                                    Err(e) => {
                                        println!("❌ [AUTH] Failed to store token: {:?}", e);
                                        Err("Failed to store authentication token".to_string())
                                    }
                                }
                            } else {
                                println!("⚠️ [AUTH] No token in API response");
                                Ok(AuthResponse {
                                    success: false,
                                    token: None,
                                    message: "No token received from server".to_string(),
                                })
                            }
                        }
                        Err(e) => {
                            println!("❌ [AUTH] Failed to parse response JSON: {}", e);
                            Err("Failed to parse server response".to_string())
                        }
                    }
                }
                400 => {
                    println!("⚠️ [AUTH] Bad request (400)");
                    Ok(AuthResponse {
                        success: false,
                        token: None,
                        message: "Invalid request. Please check your input.".to_string(),
                    })
                }
                401 => {
                    println!("🚫 [AUTH] Unauthorized (401)");
                    Ok(AuthResponse {
                        success: false,
                        token: None,
                        message: "Invalid email or password".to_string(),
                    })
                }
                _ => {
                    println!("❌ [AUTH] Server error: {}", status);
                    Ok(AuthResponse {
                        success: false,
                        token: None,
                        message: format!("Server error: {}", status),
                    })
                }
            }
        }
        Err(err) => {
            println!("🌐 [AUTH] Network/Connection error: {}", err);

            // Check if it's a timeout error
            if err.is_timeout() {
                Err("Request timeout. Please check your internet connection and try again.".to_string())
            } else if err.is_connect() {
                Err("Failed to connect to server. Please check your internet connection.".to_string())
            } else {
                Err(format!("Network error: {}", err))
            }
        }
    }
}

// Clear stored authentication token
#[tauri::command]
async fn clear_token(app: tauri::AppHandle) -> Result<bool, String> {
    println!("🗑️ [AUTH] Clearing stored authentication token...");

    match StoreBuilder::new(&app, std::path::PathBuf::from("auth.json")).build() {
        Ok(store) => {
            // Remove token from store
            store.delete("token");

            // Save the changes
            match store.save() {
                Ok(_) => {
                    println!("✅ [AUTH] Token cleared successfully");
                    Ok(true)
                }
                Err(e) => {
                    println!("❌ [AUTH] Failed to save store after clearing token: {:?}", e);
                    Err("Failed to save changes after clearing token".to_string())
                }
            }
        }
        Err(e) => {
            println!("❌ [AUTH] Failed to access store for clearing token: {:?}", e);
            Err("Failed to access authentication store".to_string())
        }
    }
}

// Global state for reload debouncing
static LAST_RELOAD_TIME: Mutex<Option<Instant>> = Mutex::new(None);

// Reload the main window with debouncing
#[tauri::command]
async fn reload_window(app: tauri::AppHandle) -> Result<bool, String> {
    // Debouncing: prevent multiple reloads within 2 seconds
    {
        let mut last_reload = LAST_RELOAD_TIME.lock().unwrap();
        let now = Instant::now();

        if let Some(last_time) = *last_reload {
            if now.duration_since(last_time) < Duration::from_secs(2) {
                println!("⚠️ [WINDOW] Reload request ignored - too frequent (debounced)");
                return Ok(false);
            }
        }

        *last_reload = Some(now);
    }

    println!("🔄 [WINDOW] Reloading main window...");

    match app.get_webview_window("main") {
        Some(window) => {
            println!("📱 [WINDOW] Main window found, initiating reload...");

            // Use a more gentle reload approach
            match window.eval("
                try {
                    console.log('🔄 [TAURI] Starting window reload via Ctrl+R...');
                    setTimeout(() => {
                        console.log('🔄 [TAURI] Executing window.location.reload()...');
                        window.location.reload();
                    }, 100);
                } catch (e) {
                    console.error('❌ [TAURI] Reload error:', e);
                }
            ") {
                Ok(_) => {
                    println!("✅ [WINDOW] Main window reload initiated successfully");
                    Ok(true)
                }
                Err(e) => {
                    println!("❌ [WINDOW] Failed to initiate window reload: {:?}", e);
                    Err(format!("Failed to reload window: {}", e))
                }
            }
        }
        None => {
            println!("❌ [WINDOW] Main window not found");
            Err("Main window not found".to_string())
        }
    }
}

// Get cached user profile from store with migration support
#[tauri::command]
async fn get_cached_user_profile(app: tauri::AppHandle) -> Result<Option<UserProfile>, String> {
    println!("💾 [PROFILE] Getting cached user profile...");

    match StoreBuilder::new(&app, std::path::PathBuf::from("user_profile.json")).build() {
        Ok(store) => {
            match store.get("profile") {
                Some(profile_value) => {
                    // Try to parse as current UserProfile structure
                    match serde_json::from_value::<UserProfile>(profile_value.clone()) {
                        Ok(profile) => {
                            println!("✅ [PROFILE] Cached profile found: {:?}", profile.email);
                            Ok(Some(profile))
                        }
                        Err(e) => {
                            println!("⚠️ [PROFILE] Failed to parse cached profile: {}", e);
                            println!("🔄 [PROFILE] Attempting to migrate old profile format...");

                            // Try to migrate old profile format
                            if let Ok(mut old_profile) = serde_json::from_value::<serde_json::Value>(profile_value.clone()) {
                                // Add missing screen_active field with default value
                                if !old_profile.as_object().unwrap().contains_key("screen_active") {
                                    old_profile["screen_active"] = serde_json::Value::Bool(false);
                                    println!("🔧 [PROFILE] Added missing screen_active field");
                                }

                                // Try to parse again with migrated data
                                match serde_json::from_value::<UserProfile>(old_profile.clone()) {
                                    Ok(migrated_profile) => {
                                        println!("✅ [PROFILE] Profile migrated successfully");

                                        // Save migrated profile back to store
                                        store.set("profile", old_profile);
                                        let _ = store.save();

                                        Ok(Some(migrated_profile))
                                    }
                                    Err(migration_error) => {
                                        println!("❌ [PROFILE] Migration failed: {}", migration_error);
                                        println!("🗑️ [PROFILE] Clearing corrupted cache");
                                        store.delete("profile");
                                        let _ = store.save();
                                        Ok(None)
                                    }
                                }
                            } else {
                                println!("❌ [PROFILE] Cannot parse profile as JSON, clearing cache");
                                store.delete("profile");
                                let _ = store.save();
                                Ok(None)
                            }
                        }
                    }
                }
                None => {
                    println!("ℹ️ [PROFILE] No cached profile found");
                    Ok(None)
                }
            }
        }
        Err(e) => {
            println!("❌ [PROFILE] Failed to access profile store: {:?}", e);
            Ok(None)
        }
    }
}

// Clear all user-related stores (for 403 errors and logout)
#[tauri::command]
async fn clear_user_stores(app: tauri::AppHandle) -> Result<(), String> {
    println!("🗑️ [STORES] Clearing all user-related stores...");

    // Clear auth store
    match StoreBuilder::new(&app, std::path::PathBuf::from("auth.json")).build() {
        Ok(store) => {
            store.delete("token");
            let _ = store.save();
            println!("✅ [STORES] Auth store cleared");
        }
        Err(e) => {
            println!("⚠️ [STORES] Failed to clear auth store: {:?}", e);
        }
    }

    // Clear profile store
    match StoreBuilder::new(&app, std::path::PathBuf::from("user_profile.json")).build() {
        Ok(store) => {
            store.delete("profile");
            let _ = store.save();
            println!("✅ [STORES] Profile store cleared");
        }
        Err(e) => {
            println!("⚠️ [STORES] Failed to clear profile store: {:?}", e);
        }
    }

    Ok(())
}

// Create a default offline profile when server is not available
#[tauri::command]
async fn create_offline_profile(app: tauri::AppHandle, email: String) -> Result<UserProfile, String> {
    println!("📴 [PROFILE] Creating offline profile for: {}", email);

    let offline_profile = UserProfile {
        full_name: "User".to_string(),
        email: email.clone(),
        avatar: None,
        position: None,
        company: None,
        is_admin: false,
        screen_active: false,
    };

    // Store offline profile
    match StoreBuilder::new(&app, std::path::PathBuf::from("user_profile.json")).build() {
        Ok(store) => {
            store.set("profile", serde_json::to_value(&offline_profile).unwrap());
            let _ = store.save();
            println!("💾 [PROFILE] Offline profile stored");
        }
        Err(e) => {
            println!("⚠️ [PROFILE] Failed to store offline profile: {:?}", e);
        }
    }

    Ok(offline_profile)
}

// Get user profile from API
#[tauri::command]
async fn get_user_profile(app: tauri::AppHandle) -> Result<UserProfile, String> {
    println!("👤 [PROFILE] Fetching user profile...");

    // Get token from store
    let token = match StoreBuilder::new(&app, std::path::PathBuf::from("auth.json")).build() {
        Ok(store) => {
            match store.get("token") {
                Some(token_value) => {
                    if let Some(token_str) = token_value.as_str() {
                        token_str.to_string()
                    } else {
                        println!("❌ [PROFILE] Invalid token format in store");
                        return Err("INVALID_TOKEN".to_string());
                    }
                }
                None => {
                    println!("❌ [PROFILE] No token found in store");
                    return Err("NO_TOKEN".to_string());
                }
            }
        }
        Err(e) => {
            println!("❌ [PROFILE] Failed to access store: {:?}", e);
            return Err("STORE_ACCESS_ERROR".to_string());
        }
    };

    // Create HTTP client with timeout
    let client = reqwest::Client::builder()
        .timeout(Duration::from_secs(30))
        .build()
        .map_err(|e| {
            println!("❌ [PROFILE] Failed to create HTTP client: {}", e);
            format!("Failed to create HTTP client: {}", e)
        })?;

    println!("📡 [PROFILE] Sending request to: http://127.0.0.1:8000/api/v2/employees/profile/");

    // Make API call to profile endpoint
    match client
        .get("http://127.0.0.1:8000/api/v2/employees/profile/")
        .header("X-Api-Key", &token)
        .timeout(Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => {
            let status = response.status();
            println!("📨 [PROFILE] Received response with status: {}", status);

            match status.as_u16() {
                200 => {
                    println!("✅ [PROFILE] Profile fetch successful, parsing response...");

                    // First get the response text to see what we're dealing with
                    let response_text = match response.text().await {
                        Ok(text) => {
                            println!("📄 [PROFILE] Raw response body: {}", text);
                            text
                        }
                        Err(e) => {
                            println!("❌ [PROFILE] Failed to read response text: {}", e);
                            return Err("Failed to read response body".to_string());
                        }
                    };

                    // Try to parse as UserProfileResponse first
                    match serde_json::from_str::<UserProfileResponse>(&response_text) {
                        Ok(profile_response) => {
                            println!("📋 [PROFILE] Profile data: {:?}", profile_response.data);

                            // Store profile in Tauri store
                            match StoreBuilder::new(&app, std::path::PathBuf::from("user_profile.json")).build() {
                                Ok(store) => {
                                    store.set("profile", serde_json::to_value(&profile_response.data).unwrap());
                                    let _ = store.save();
                                    println!("💾 [PROFILE] Profile stored successfully");
                                }
                                Err(e) => {
                                    println!("⚠️ [PROFILE] Failed to store profile: {:?}", e);
                                    // Continue anyway, just log the warning
                                }
                            }

                            Ok(profile_response.data)
                        }
                        Err(e) => {
                            println!("⚠️ [PROFILE] Failed to parse as UserProfileResponse: {}", e);
                            println!("🔄 [PROFILE] Trying to parse as ServerUserProfileResponse (with typo)...");

                            // Try to parse as ServerUserProfileResponse (handles server typo)
                            match serde_json::from_str::<ServerUserProfileResponse>(&response_text) {
                                Ok(server_response) => {
                                    println!("✅ [PROFILE] Successfully parsed as ServerUserProfileResponse");

                                    // Convert ServerUserProfile to UserProfile (fixes typo)
                                    let profile: UserProfile = server_response.data.into();
                                    println!("📋 [PROFILE] Converted profile data: {:?}", profile);

                                    // Store profile in Tauri store
                                    match StoreBuilder::new(&app, std::path::PathBuf::from("user_profile.json")).build() {
                                        Ok(store) => {
                                            store.set("profile", serde_json::to_value(&profile).unwrap());
                                            let _ = store.save();
                                            println!("💾 [PROFILE] Profile stored successfully");
                                        }
                                        Err(e) => {
                                            println!("⚠️ [PROFILE] Failed to store profile: {:?}", e);
                                        }
                                    }

                                    Ok(profile)
                                }
                                Err(server_parse_error) => {
                                    println!("⚠️ [PROFILE] Failed to parse as ServerUserProfileResponse: {}", server_parse_error);
                                    println!("🔄 [PROFILE] Trying to parse as direct UserProfile...");

                                    // Try to parse as direct UserProfile (in case server sends profile directly)
                                    match serde_json::from_str::<UserProfile>(&response_text) {
                                        Ok(profile) => {
                                            println!("✅ [PROFILE] Successfully parsed as direct UserProfile: {:?}", profile);

                                            // Store profile in Tauri store
                                            match StoreBuilder::new(&app, std::path::PathBuf::from("user_profile.json")).build() {
                                                Ok(store) => {
                                                    store.set("profile", serde_json::to_value(&profile).unwrap());
                                                    let _ = store.save();
                                                    println!("💾 [PROFILE] Profile stored successfully");
                                                }
                                                Err(e) => {
                                                    println!("⚠️ [PROFILE] Failed to store profile: {:?}", e);
                                                }
                                            }

                                            Ok(profile)
                                        }
                                        Err(direct_parse_error) => {
                                            println!("❌ [PROFILE] Failed to parse as direct UserProfile: {}", direct_parse_error);
                                            println!("❌ [PROFILE] Original UserProfileResponse error: {}", e);
                                            println!("❌ [PROFILE] ServerUserProfileResponse error: {}", server_parse_error);
                                            println!("❌ [PROFILE] Response body was: {}", response_text);
                                            Err("Failed to parse profile response in any expected format".to_string())
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                403 => {
                    println!("🚫 [PROFILE] Permission denied (403) - Invalid token, clearing stores...");

                    // Clear all user-related stores on 403 error
                    let _ = clear_user_stores(app.clone()).await;

                    Err("AUTH_EXPIRED".to_string())
                }
                _ => {
                    println!("❌ [PROFILE] Server error: {}", status);
                    Err(format!("Server error: {}", status))
                }
            }
        }
        Err(err) => {
            println!("🌐 [PROFILE] Network/Connection error: {}", err);

            if err.is_timeout() {
                Err("NETWORK_TIMEOUT".to_string())
            } else if err.is_connect() {
                Err("NETWORK_CONNECTION_FAILED".to_string())
            } else if err.is_request() {
                Err("NETWORK_REQUEST_FAILED".to_string())
            } else {
                Err("NETWORK_ERROR".to_string())
            }
        }
    }
}

// Logout user by removing token from store
#[tauri::command]
async fn logout(app: tauri::AppHandle) -> Result<AuthResponse, String> {
    match StoreBuilder::new(&app, std::path::PathBuf::from("auth.json")).build() {
        Ok(store) => {
            store.delete("token");
            let _ = store.save();
        }
        Err(_) => {}
    }

    Ok(AuthResponse {
        success: true,
        token: None,
        message: "Logout successful".to_string(),
    })
}

// Fetch projects command
#[tauri::command]
async fn fetch_projects(app: tauri::AppHandle) -> Result<Vec<Project>, String> {
    println!("📋 [PROJECTS] Fetching projects from API...");

    // Get auth token
    let token = match StoreBuilder::new(&app, std::path::PathBuf::from("auth.json")).build() {
        Ok(store) => {
            match store.get("token") {
                Some(token_value) => {
                    match token_value.as_str() {
                        Some(token_str) => token_str.to_string(),
                        None => {
                            println!("❌ [PROJECTS] Token is not a string");
                            return Err("AUTH_TOKEN_INVALID".to_string());
                        }
                    }
                }
                None => {
                    println!("❌ [PROJECTS] No auth token found");
                    return Err("AUTH_TOKEN_MISSING".to_string());
                }
            }
        }
        Err(e) => {
            println!("❌ [PROJECTS] Failed to access token store: {:?}", e);
            return Err("AUTH_STORE_ERROR".to_string());
        }
    };

    // Create HTTP client
    let client = reqwest::Client::new();
    let url = "http://127.0.0.1:8000/api/projects/";

    println!("📡 [PROJECTS] Sending request to: {}", url);

    // Make API request
    let response = match client
        .get(url)
        .header("X-Api-Key", &token)
        .header("Content-Type", "application/json")
        .timeout(std::time::Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            println!("🌐 [PROJECTS] Network/Connection error: {}", e);
            return Err("NETWORK_CONNECTION_FAILED".to_string());
        }
    };

    let status = response.status();
    println!("📨 [PROJECTS] Received response with status: {}", status);

    match status.as_u16() {
        200 => {
            println!("✅ [PROJECTS] Projects fetch successful, parsing response...");

            let response_text = match response.text().await {
                Ok(text) => {
                    println!("📄 [PROJECTS] Raw response body: {}", text);
                    text
                }
                Err(e) => {
                    println!("❌ [PROJECTS] Failed to read response text: {}", e);
                    return Err("Failed to read response body".to_string());
                }
            };

            match serde_json::from_str::<ProjectsResponse>(&response_text) {
                Ok(projects_response) => {
                    if projects_response.success {
                        println!("✅ [PROJECTS] Successfully parsed {} projects", projects_response.data.len());
                        Ok(projects_response.data)
                    } else {
                        println!("❌ [PROJECTS] API returned success=false: {}", projects_response.message);
                        Err("API_ERROR".to_string())
                    }
                }
                Err(e) => {
                    println!("❌ [PROJECTS] Failed to parse projects response: {}", e);
                    println!("❌ [PROJECTS] Response body was: {}", response_text);
                    Err("Failed to parse projects response".to_string())
                }
            }
        }
        401 | 403 => {
            println!("🔐 [PROJECTS] Authentication failed ({})", status);
            Err("AUTH_EXPIRED".to_string())
        }
        _ => {
            println!("❌ [PROJECTS] API error: {}", status);
            Err(format!("API_ERROR_{}", status.as_u16()))
        }
    }
}

// Fetch project tasks command
#[tauri::command]
async fn fetch_project_tasks(app: tauri::AppHandle, project_id: i32) -> Result<Vec<Task>, String> {
    println!("📋 [TASKS] Fetching tasks for project ID: {}", project_id);

    // Get auth token
    let token = match StoreBuilder::new(&app, std::path::PathBuf::from("auth.json")).build() {
        Ok(store) => {
            match store.get("token") {
                Some(token_value) => {
                    match token_value.as_str() {
                        Some(token_str) => token_str.to_string(),
                        None => {
                            println!("❌ [TASKS] Token is not a string");
                            return Err("AUTH_TOKEN_INVALID".to_string());
                        }
                    }
                }
                None => {
                    println!("❌ [TASKS] No auth token found");
                    return Err("AUTH_TOKEN_MISSING".to_string());
                }
            }
        }
        Err(e) => {
            println!("❌ [TASKS] Failed to access token store: {:?}", e);
            return Err("AUTH_STORE_ERROR".to_string());
        }
    };

    // Create HTTP client
    let client = reqwest::Client::new();
    let url = format!("http://127.0.0.1:8000/api/projects/{}/targets/", project_id);

    println!("📡 [TASKS] Sending request to: {}", url);

    // Make API request
    let response = match client
        .get(&url)
        .header("X-Api-Key", &token)
        .header("Content-Type", "application/json")
        .timeout(std::time::Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            println!("🌐 [TASKS] Network/Connection error: {}", e);
            return Err("NETWORK_CONNECTION_FAILED".to_string());
        }
    };

    let status = response.status();
    println!("📨 [TASKS] Received response with status: {}", status);

    match status.as_u16() {
        200 => {
            println!("✅ [TASKS] Tasks fetch successful, parsing response...");

            let response_text = match response.text().await {
                Ok(text) => {
                    println!("📄 [TASKS] Raw response body: {}", text);
                    text
                }
                Err(e) => {
                    println!("❌ [TASKS] Failed to read response text: {}", e);
                    return Err("Failed to read response body".to_string());
                }
            };

            match serde_json::from_str::<TasksResponse>(&response_text) {
                Ok(tasks_response) => {
                    if tasks_response.success {
                        println!("✅ [TASKS] Successfully parsed {} tasks", tasks_response.data.len());
                        Ok(tasks_response.data)
                    } else {
                        println!("❌ [TASKS] API returned success=false: {}", tasks_response.message);
                        Err("API_ERROR".to_string())
                    }
                }
                Err(e) => {
                    println!("❌ [TASKS] Failed to parse tasks response: {}", e);
                    println!("❌ [TASKS] Response body was: {}", response_text);
                    Err("Failed to parse tasks response".to_string())
                }
            }
        }
        401 | 403 => {
            println!("🔐 [TASKS] Authentication failed ({})", status);
            Err("AUTH_EXPIRED".to_string())
        }
        404 => {
            println!("🔍 [TASKS] Project not found (404)");
            Err("PROJECT_NOT_FOUND".to_string())
        }
        _ => {
            println!("❌ [TASKS] API error: {}", status);
            Err(format!("API_ERROR_{}", status.as_u16()))
        }
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  tauri::Builder::default()
    .plugin(tauri_plugin_store::Builder::default().build())
    .plugin(
      tauri_plugin_global_shortcut::Builder::new()
        .with_handler(move |app, shortcut, event| {
          use tauri_plugin_global_shortcut::{Code, Modifiers, Shortcut, ShortcutState};

          let ctrl_n_shortcut = Shortcut::new(Some(Modifiers::CONTROL), Code::KeyN);
          let ctrl_r_shortcut = Shortcut::new(Some(Modifiers::CONTROL), Code::KeyR);

          match event.state() {
            ShortcutState::Pressed => {
              if shortcut == &ctrl_n_shortcut {
                println!("🔥 [SHORTCUT] Ctrl+N pressed - clearing authentication token");
                let app_handle = app.clone();
                tauri::async_runtime::spawn(async move {
                  match clear_token(app_handle).await {
                    Ok(_) => println!("✅ [SHORTCUT] Token cleared successfully via shortcut"),
                    Err(e) => println!("❌ [SHORTCUT] Failed to clear token via shortcut: {}", e),
                  }
                });
              } else if shortcut == &ctrl_r_shortcut {
                println!("🔄 [SHORTCUT] Ctrl+R pressed - reloading main window");
                let app_handle = app.clone();
                tauri::async_runtime::spawn(async move {
                  match reload_window(app_handle).await {
                    Ok(_) => println!("✅ [SHORTCUT] Window reloaded successfully via shortcut"),
                    Err(e) => println!("❌ [SHORTCUT] Failed to reload window via shortcut: {}", e),
                  }
                });
              }
            }
            ShortcutState::Released => {
              // Do nothing on release
            }
          }
        })
        .build()
    )
    .setup(|app| {
      if cfg!(debug_assertions) {
        app.handle().plugin(
          tauri_plugin_log::Builder::default()
            .level(log::LevelFilter::Info)
            .build(),
        )?;
      }

      // Get the splashscreen window
      let splashscreen_window = app.get_webview_window("splashscreen").unwrap();
      let main_window = app.get_webview_window("main").unwrap();

      // Show splashscreen immediately
      splashscreen_window.show().unwrap();

      // Register global shortcuts
      use tauri_plugin_global_shortcut::{Code, Modifiers, Shortcut};

      let ctrl_n_shortcut = Shortcut::new(Some(Modifiers::CONTROL), Code::KeyN);
      let ctrl_r_shortcut = Shortcut::new(Some(Modifiers::CONTROL), Code::KeyR);

      // Unregister shortcuts first (in case they were already registered)
      let _ = app.global_shortcut().unregister(ctrl_n_shortcut);
      let _ = app.global_shortcut().unregister(ctrl_r_shortcut);

      // Register shortcuts with error handling
      if let Err(e) = app.global_shortcut().register(ctrl_n_shortcut) {
        println!("⚠️ [SHORTCUT] Failed to register Ctrl+N: {:?}", e);
      } else {
        println!("✅ [SHORTCUT] Ctrl+N registered successfully");
      }

      if let Err(e) = app.global_shortcut().register(ctrl_r_shortcut) {
        println!("⚠️ [SHORTCUT] Failed to register Ctrl+R: {:?}", e);
      } else {
        println!("✅ [SHORTCUT] Ctrl+R registered successfully");
      }

      println!("⌨️ [SHORTCUT] Global shortcuts registered:");
      println!("   • Ctrl+N: Clear authentication token");
      println!("   • Ctrl+R: Reload main window");

      // Force splash screen to stay open for exactly 9 seconds
      let splashscreen_window_clone = splashscreen_window.clone();
      let main_window_clone = main_window.clone();

      tauri::async_runtime::spawn(async move {
        // Wait exactly 9 seconds - no early closing allowed
        tokio::time::sleep(tokio::time::Duration::from_millis(9000)).await;

        // Force close splash screen and show main window after 9 seconds
        splashscreen_window_clone.close().unwrap();
        main_window_clone.show().unwrap();
        main_window_clone.set_focus().unwrap();
      });

      Ok(())
    })
    .invoke_handler(tauri::generate_handler![check_auth, login, logout, clear_token, reload_window, get_user_profile, get_cached_user_profile, clear_user_stores, create_offline_profile, fetch_projects, fetch_project_tasks])
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}
