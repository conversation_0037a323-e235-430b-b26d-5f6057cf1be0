use tauri::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use tauri_plugin_store::StoreBuilder;

use super::types::ErrorLog;
use super::utils::{get_current_timestamp};

// Store error log
#[tauri::command]
pub async fn store_error_log(
    app: AppHandle,
    activity_id: i32,
    error_message: String,
) -> Result<bool, String> {
    println!("📝 [ERROR_LOG] Storing error log for activity ID: {}", activity_id);
    
    let store = match StoreBuilder::new(&app, "error_logs.json").build() {
        Ok(store) => store,
        Err(e) => return Err(format!("Failed to build store: {}", e)),
    };

    // Create error log entry
    let error_log = ErrorLog {
        id: None,
        activity_id,
        error_message: error_message.clone(),
        timestamp: get_current_timestamp(),
    };

    // Get existing error logs
    let mut error_logs: Vec<ErrorLog> = match store.get("logs") {
        Some(logs_value) => {
            match serde_json::from_value(logs_value.clone()) {
                Ok(logs) => logs,
                Err(_) => Vec::new(),
            }
        }
        None => Vec::new(),
    };
    
    // Add new error log
    error_logs.push(error_log);
    
    // Store updated error logs
    match serde_json::to_value(&error_logs) {
        Ok(logs_value) => {
            store.set("logs", logs_value);
            match store.save() {
                Ok(_) => {
                    println!("✅ [ERROR_LOG] Error log stored successfully");
                    Ok(true)
                }
                Err(e) => {
                    let error_msg = format!("Failed to save error logs store: {}", e);
                    println!("❌ [ERROR_LOG] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        Err(e) => {
            let error_msg = format!("Failed to serialize error logs: {}", e);
            println!("❌ [ERROR_LOG] {}", error_msg);
            Err(error_msg)
        }
    }
}

// Get error logs count for activity
pub async fn get_error_logs_count(app: &AppHandle, activity_id: i32) -> Result<i32, String> {
    println!("📊 [ERROR_LOG] Getting error count for activity ID: {}", activity_id);
    
    let store = match StoreBuilder::new(app, "error_logs.json").build() {
        Ok(store) => store,
        Err(e) => return Err(format!("Failed to build store: {}", e)),
    };
    
    let error_logs: Vec<ErrorLog> = match store.get("logs") {
        Some(logs_value) => {
            match serde_json::from_value(logs_value.clone()) {
                Ok(logs) => logs,
                Err(_) => Vec::new(),
            }
        }
        None => Vec::new(),
    };
    
    let count = error_logs
        .iter()
        .filter(|log| log.activity_id == activity_id)
        .count() as i32;
    
    println!("📊 [ERROR_LOG] Found {} error logs for activity {}", count, activity_id);
    Ok(count)
}

// Get total error logs count (all activities)
pub async fn get_all_error_logs_count(app: &AppHandle) -> Result<i32, String> {
    println!("📊 [ERROR_LOG] Getting total error logs count...");

    let store = match StoreBuilder::new(app, "error_logs.json").build() {
        Ok(store) => store,
        Err(e) => return Err(format!("Failed to build store: {}", e)),
    };

    let error_logs: Vec<ErrorLog> = match store.get("logs") {
        Some(logs_value) => {
            match serde_json::from_value(logs_value.clone()) {
                Ok(logs) => logs,
                Err(_) => Vec::new(),
            }
        }
        None => Vec::new(),
    };

    let count = error_logs.len() as i32;
    println!("📊 [ERROR_LOG] Found {} total error logs", count);
    Ok(count)
}

// Get error logs for activity
#[tauri::command]
pub async fn get_error_logs(app: AppHandle, activity_id: i32) -> Result<Vec<ErrorLog>, String> {
    println!("📋 [ERROR_LOG] Getting error logs for activity ID: {}", activity_id);
    
    let store = match StoreBuilder::new(&app, "error_logs.json").build() {
        Ok(store) => store,
        Err(e) => return Err(format!("Failed to build store: {}", e)),
    };

    let error_logs: Vec<ErrorLog> = match store.get("logs") {
        Some(logs_value) => {
            match serde_json::from_value(logs_value.clone()) {
                Ok(logs) => logs,
                Err(_) => Vec::new(),
            }
        }
        None => Vec::new(),
    };
    
    let activity_logs: Vec<ErrorLog> = error_logs
        .into_iter()
        .filter(|log| log.activity_id == activity_id)
        .collect();
    
    println!("📋 [ERROR_LOG] Found {} error logs for activity {}", activity_logs.len(), activity_id);
    Ok(activity_logs)
}

// Delete error logs for activity (internal function)
pub async fn delete_error_logs_internal(app: AppHandle, activity_id: i32) -> Result<bool, String> {
    println!("🗑️ [ERROR_LOG] Deleting error logs for activity ID: {}", activity_id);
    
    let store = match StoreBuilder::new(&app, "error_logs.json").build() {
        Ok(store) => store,
        Err(e) => return Err(format!("Failed to build store: {}", e)),
    };

    let error_logs: Vec<ErrorLog> = match store.get("logs") {
        Some(logs_value) => {
            match serde_json::from_value(logs_value.clone()) {
                Ok(logs) => logs,
                Err(_) => Vec::new(),
            }
        }
        None => Vec::new(),
    };
    
    // Filter out logs for this activity
    let remaining_logs: Vec<ErrorLog> = error_logs
        .into_iter()
        .filter(|log| log.activity_id != activity_id)
        .collect();
    
    // Store updated error logs
    match serde_json::to_value(&remaining_logs) {
        Ok(logs_value) => {
            store.set("logs", logs_value);
            match store.save() {
                Ok(_) => {
                    println!("✅ [ERROR_LOG] Error logs deleted successfully");
                    Ok(true)
                }
                Err(e) => {
                    let error_msg = format!("Failed to save error logs store: {}", e);
                    println!("❌ [ERROR_LOG] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        Err(e) => {
            let error_msg = format!("Failed to serialize error logs: {}", e);
            println!("❌ [ERROR_LOG] {}", error_msg);
            Err(error_msg)
        }
    }
}

// Clear all error logs
#[tauri::command]
pub async fn clear_all_error_logs(app: AppHandle) -> Result<bool, String> {
    println!("🗑️ [ERROR_LOG] Clearing all error logs...");
    
    let store = match StoreBuilder::new(&app, "error_logs.json").build() {
        Ok(store) => store,
        Err(e) => return Err(format!("Failed to build store: {}", e)),
    };
    
    let empty_logs: Vec<ErrorLog> = Vec::new();
    
    match serde_json::to_value(&empty_logs) {
        Ok(logs_value) => {
            store.set("logs", logs_value);
            match store.save() {
                Ok(_) => {
                    println!("✅ [ERROR_LOG] All error logs cleared successfully");
                    Ok(true)
                }
                Err(e) => {
                    let error_msg = format!("Failed to save error logs store: {}", e);
                    println!("❌ [ERROR_LOG] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        Err(e) => {
            let error_msg = format!("Failed to serialize error logs: {}", e);
            println!("❌ [ERROR_LOG] {}", error_msg);
            Err(error_msg)
        }
    }
}

// Legacy alias for React compatibility
#[tauri::command]
pub async fn insert_error_log(
    app: AppHandle,
    timestamp: i64,
    error_code: i32,
    activity_id: i32,
) -> Result<bool, String> {
    println!("📝 [ERROR_LOG] Legacy insert_error_log called - redirecting to store_error_log");

    // Convert timestamp to readable format
    let error_message = format!("Error code: {} at timestamp: {}", error_code, timestamp);

    // Call the main store_error_log function
    store_error_log(app, activity_id, error_message).await
}

// Legacy alias for React compatibility (exact name match)
#[tauri::command]
pub async fn delete_error_logs(app: AppHandle, activity_id: i32) -> Result<bool, String> {
    println!("🗑️ [ERROR_LOG] Legacy delete_error_logs called");

    // Call the internal delete function with different name
    delete_error_logs_internal(app, activity_id).await
}
