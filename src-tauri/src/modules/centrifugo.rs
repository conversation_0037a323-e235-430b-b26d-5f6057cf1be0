use tauri::{AppHandle, Emitter};
use tauri_plugin_store::StoreBuilder;
use tokio::sync::Mutex;
use tokio::time::Duration;
use std::sync::Arc;
use serde_json;
use serde::{Deserialize, Serialize};

use super::utils::get_auth_token;
use tokio_centrifuge::{
    client::Client,
    config::Config,
};
use std::sync::OnceLock;

// Constants
const CENTRIFUGO_WS_URL: &str = "wss://centrifugo.newhorizonco.uk/connection/websocket";
const CENTRIFUGO_API_URL: &str = "http://localhost:8000/api/account/centrifugo/token/";

// Global service instance
static CENTRIFUGO_SERVICE: OnceLock<Arc<CentrifugoService>> = OnceLock::new();

#[derive(Serialize, Deserialize, Debug)]
pub struct CentrifugoStatus {
    pub is_running: bool,
    pub user_id: Option<u64>,
    pub channel: Option<String>,
}

// Simple Centrifugo service (like examples)
pub struct CentrifugoService {
    pub app: AppHandle,
    pub is_running: Arc<Mutex<bool>>,
    pub user_id: Arc<Mutex<Option<u64>>>,
    pub channel: Arc<Mutex<Option<String>>>,
}

impl CentrifugoService {
    pub fn new(app: AppHandle) -> Self {
        Self {
            app,
            is_running: Arc::new(Mutex::new(false)),
            user_id: Arc::new(Mutex::new(None)),
            channel: Arc::new(Mutex::new(None)),
        }
    }

    // Simple connect like examples
    pub async fn connect(&self) -> Result<(), String> {
        // Check if already running
        {
            let is_running = self.is_running.lock().await;
            if *is_running {
                println!("⚠️ [CENTRIFUGO] Already running");
                // If already running, assume it's connected - update TopBar
                println!("📡 [CENTRIFUGO] Emitting connected=true for already running service");
                let _ = self.app.emit("centrifugo-connected", true);
                return Ok(());
            }
        }

        // Set running flag
        {
            let mut is_running = self.is_running.lock().await;
            *is_running = true;
        }

        // Get token from store
        let token = self.get_token_from_store().await?;
        println!("🎫 [CENTRIFUGO] Token obtained for user: {}", token.user_id);

        // Store user_id (convert i32 to u64)
        {
            let mut user_id = self.user_id.lock().await;
            *user_id = Some(token.user_id as u64);
        }

        // Create client (simple like example)
        let config = Config::new().with_token(&token.token);
        let client = Client::new(CENTRIFUGO_WS_URL, config);

        // Setup handlers exactly like example
        client.on_connecting(|| {
            println!("🔄 [CENTRIFUGO] connecting");
        });

        let is_running_clone = self.is_running.clone();
        client.on_connected(move || {
            println!("✅ [CENTRIFUGO] connected");

            // Set is_running back to true on reconnect
            tokio::spawn({
                let is_running_clone = is_running_clone.clone();
                async move {
                    let mut is_running = is_running_clone.lock().await;
                    *is_running = true;
                    println!("📊 [CENTRIFUGO] is_running set to true on reconnect");
                }
            });
        });

        let app_clone = self.app.clone();
        let is_running_clone = self.is_running.clone();
        client.on_disconnected(move || {
            println!("🔌 [CENTRIFUGO] disconnected");

            // Update is_running flag to false
            tokio::spawn({
                let is_running_clone = is_running_clone.clone();
                async move {
                    let mut is_running = is_running_clone.lock().await;
                    *is_running = false;
                    println!("📊 [CENTRIFUGO] is_running set to false due to disconnect");
                }
            });

            // Simple status update for TopBar
            let _ = app_clone.emit("centrifugo-connected", false);
        });

        let app_clone = self.app.clone();
        let is_running_clone = self.is_running.clone();
        client.on_error(move |err| {
            println!("❌ [CENTRIFUGO] error: {:?}", err);

            // Check if it's a connection error
            let error_str = format!("{:?}", err);
            if error_str.contains("WebSocket") || error_str.contains("connection") || error_str.contains("network") {
                println!("🔌 [CENTRIFUGO] Connection error detected, setting is_running to false");

                // Update is_running flag to false
                tokio::spawn({
                    let is_running_clone = is_running_clone.clone();
                    let app_clone = app_clone.clone();
                    async move {
                        let mut is_running = is_running_clone.lock().await;
                        *is_running = false;
                        println!("📊 [CENTRIFUGO] is_running set to false due to connection error");

                        // Update TopBar status
                        let _ = app_clone.emit("centrifugo-connected", false);
                    }
                });
            }
        });

        // Subscribe to user channel exactly like example
        let channel = format!("teamby:user#{}", token.user_id);
        let sub = client.new_subscription(&channel);

        // Store channel
        {
            let mut channel_store = self.channel.lock().await;
            *channel_store = Some(channel.clone());
        }

        let app_clone = self.app.clone();
        sub.on_subscribed(move || {
            println!("📡 [CENTRIFUGO] subscribed to user channel");
            // When subscribed, we're fully connected - update TopBar
            println!("📡 [CENTRIFUGO] Emitting connected=true for subscription");
            let _ = app_clone.emit("centrifugo-connected", true);
        });

        let app_clone = self.app.clone();
        sub.on_unsubscribed(move || {
            println!("📡 [CENTRIFUGO] unsubscribed from user channel");
            // When unsubscribed, we're disconnected - update TopBar
            let _ = app_clone.emit("centrifugo-connected", false);
        });

        sub.on_subscribing(|| {
            println!("📡 [CENTRIFUGO] subscribing to user channel");
        });

        let app_clone = self.app.clone();
        sub.on_publication(move |data| {
            println!("📨 [CENTRIFUGO] publication: {} bytes", data.data.len());
            // Parse message data and forward to React
            if let Ok(json_data) = serde_json::from_slice::<serde_json::Value>(&data.data) {
                println!("📨 [CENTRIFUGO] message data: {}", json_data);
                // Forward message to React frontend
                let _ = app_clone.emit("centrifugo-message", json_data);
                println!("📨 [CENTRIFUGO] Message forwarded to React frontend");
            } else {
                println!("❌ [CENTRIFUGO] Failed to parse message data");
            }
        });

        // Start exactly like example
        sub.subscribe();
        client.connect();

        println!("👂 [CENTRIFUGO] Listening to channel: {}", channel);

        // Keep alive exactly like example
        loop {
            tokio::time::sleep(Duration::from_secs(1)).await;
        }
    }

    // Get token from Tauri store
    async fn get_token_from_store(&self) -> Result<CentrifugoToken, String> {
        let store = StoreBuilder::new(&self.app, "centrifugo.json").build()
            .map_err(|e| format!("Failed to create store: {}", e))?;

        if let Some(token_value) = store.get("token") {
            if let Ok(token) = serde_json::from_value::<CentrifugoToken>(token_value.clone()) {
                // Check if token is still valid
                let now = chrono::Utc::now().timestamp();
                if token.expires_at > now + 300 { // 5 minutes buffer
                    return Ok(token);
                }
            }
        }

        // If no valid token, fetch new one
        self.fetch_new_token().await
    }

    // Fetch new token from API
    async fn fetch_new_token(&self) -> Result<CentrifugoToken, String> {
        // Get auth token using utility function
        let auth_token = get_auth_token(&self.app).await?;
        println!("🔑 [CENTRIFUGO] Using auth token: {}...", &auth_token[..std::cmp::min(20, auth_token.len())]);

        let client = reqwest::Client::new();

        let response = client
            .get(CENTRIFUGO_API_URL)
            .header("Content-Type", "application/json")
            .header("X-Api-Key", auth_token)
            .send()
            .await
            .map_err(|e| format!("Request failed: {}", e))?;

        let status = response.status();
        if !status.is_success() {
            let error_text = response.text().await.unwrap_or_default();
            println!("❌ [CENTRIFUGO] API Error: {} - {}", status, error_text);
            return Err(format!("API returned status: {}", status));
        }

        let response_text = response.text().await
            .map_err(|e| format!("Failed to read response: {}", e))?;

        let api_response: serde_json::Value = serde_json::from_str(&response_text)
            .map_err(|e| format!("Failed to parse JSON: {}", e))?;

        let token_str = api_response["token"]
            .as_str()
            .ok_or("Token not found in response")?;

        let user_id = api_response["user_id"]
            .as_i64()
            .ok_or("User ID not found in response")? as i32;

        let expires_at = chrono::Utc::now().timestamp() + 86400; // 24 hours

        let token = CentrifugoToken {
            token: token_str.to_string(),
            user_id,
            expires_at,
        };

        // Store token
        let store = StoreBuilder::new(&self.app, "centrifugo.json").build()
            .map_err(|e| format!("Failed to create store: {}", e))?;
        store.set("token", serde_json::to_value(&token).unwrap());
        store.save().map_err(|e| format!("Failed to save token: {}", e))?;

        Ok(token)
    }
}

// Token structure
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct CentrifugoToken {
    pub token: String,
    pub user_id: i32,
    pub expires_at: i64,
}

// Get or create global service instance
fn get_or_create_service(app: AppHandle) -> Arc<CentrifugoService> {
    CENTRIFUGO_SERVICE.get_or_init(|| {
        Arc::new(CentrifugoService::new(app))
    }).clone()
}

// Tauri commands
#[tauri::command]
pub async fn start_centrifugo_service(app: AppHandle) -> Result<bool, String> {
    println!("🚀 [CENTRIFUGO] Starting simple service...");

    let service = get_or_create_service(app);

    // Start connection in background task
    let service_for_task = service.clone();
    tokio::spawn(async move {
        if let Err(e) = service_for_task.connect().await {
            println!("❌ [CENTRIFUGO] Connection failed: {}", e);
        }
    });

    Ok(true)
}

#[tauri::command]
pub async fn stop_centrifugo_service(app: AppHandle) -> Result<bool, String> {
    println!("🛑 [CENTRIFUGO] Stopping service...");
    let service = get_or_create_service(app);

    // Reset all flags
    {
        let mut is_running = service.is_running.lock().await;
        *is_running = false;
    }
    {
        let mut user_id = service.user_id.lock().await;
        *user_id = None;
    }
    {
        let mut channel = service.channel.lock().await;
        *channel = None;
    }

    Ok(true)
}

#[tauri::command]
pub async fn get_centrifugo_status(app: AppHandle) -> Result<CentrifugoStatus, String> {
    let service = get_or_create_service(app);

    let is_running = *service.is_running.lock().await;
    let user_id = *service.user_id.lock().await;
    let channel = service.channel.lock().await.clone();

    let status = CentrifugoStatus {
        is_running,
        user_id,
        channel,
    };

    println!("📊 [CENTRIFUGO] Status check: running={}, user_id={:?}, channel={:?}",
             status.is_running, status.user_id, status.channel);

    Ok(status)
}
