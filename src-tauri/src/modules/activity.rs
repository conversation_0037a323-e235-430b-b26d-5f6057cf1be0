
use tauri::A<PERSON><PERSON><PERSON><PERSON>;

use super::types::{ActivityStartRequest, ActivityStartResponse, ActivityEndRequest, ActivityUpdateRequest};
use super::utils::{create_http_client, get_auth_token, get_system_info, log_api_request, log_api_response};
use super::error_logs::{delete_error_logs, get_error_logs_count, clear_all_error_logs, get_all_error_logs_count};

// Start activity
#[tauri::command]
pub async fn start_activity(app: AppHandle) -> Result<i32, String> {
    println!("🚀 [ACTIVITY] Starting activity...");
    
    // Get authentication token
    let token = get_auth_token(&app).await?;
    
    // Get system information
    let system_info = get_system_info().await;
    println!("📊 [ACTIVITY] System Info - Platform: {}, Device: {}, OS: {}, Time: {}", 
             system_info.platform, system_info.device_name, system_info.device_os, system_info.local_time);
    
    // Create request body
    let request_body = ActivityStartRequest {
        project_id: None,
        task_id: None,
        system_local_time: system_info.local_time,
        platform: system_info.platform,
        device_name: system_info.device_name,
        device_os: system_info.device_os,
    };
    
    // Create HTTP client
    let client = create_http_client();
    
    let url = "http://127.0.0.1:8000/api/activity/start/";
    log_api_request("POST", url, "ACTIVITY");
    
    // Make API request
    let response = match client
        .post(url)
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .header("Content-Type", "application/json")
        .json(&request_body)
        .timeout(std::time::Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = if e.is_timeout() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else if e.is_connect() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else {
                format!("Network error: {}", e)
            };
            println!("❌ [ACTIVITY] {}", error_msg);
            return Err(error_msg);
        }
    };
    
    let status = response.status();
    log_api_response(&status, "ACTIVITY");
    
    match status.as_u16() {
        200 | 201 => {
            println!("✅ [ACTIVITY] Activity start successful ({}), parsing response...", status);
            
            let response_text = match response.text().await {
                Ok(text) => {
                    println!("📄 [ACTIVITY] Raw response body: {}", text);
                    text
                }
                Err(e) => {
                    let error_msg = format!("Failed to read response body: {}", e);
                    println!("❌ [ACTIVITY] {}", error_msg);
                    return Err(error_msg);
                }
            };
            
            match serde_json::from_str::<ActivityStartResponse>(&response_text) {
                Ok(activity_response) => {
                    println!("✅ [ACTIVITY] Activity started successfully with ID: {}", activity_response.id);
                    Ok(activity_response.id)
                }
                Err(e) => {
                    let error_msg = format!("Failed to parse activity response: {}", e);
                    println!("❌ [ACTIVITY] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        401 | 403 => {
            let error_msg = "AUTH_EXPIRED".to_string();
            println!("❌ [ACTIVITY] Authentication expired");
            Err(error_msg)
        }
        _ => {
            let error_msg = format!("API error: {}", status);
            println!("❌ [ACTIVITY] {}", error_msg);
            Err(error_msg)
        }
    }
}

// Post ImReady heartbeat
#[tauri::command]
pub async fn post_im_ready(app: AppHandle, activity_id: i32) -> Result<bool, String> {
    println!("💓 [IMREADY] Sending ImReady for activity ID: {}", activity_id);
    
    // Get authentication token
    let token = get_auth_token(&app).await?;
    
    // Create HTTP client
    let client = create_http_client();
    
    let url = format!("http://127.0.0.1:8000/api/activity/{}/imready/", activity_id);
    log_api_request("GET", &url, "IMREADY");
    
    // Make API request
    let response = match client
        .get(&url)
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .header("Content-Type", "application/json")
        .timeout(std::time::Duration::from_secs(10))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = if e.is_timeout() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else if e.is_connect() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else {
                format!("Network error: {}", e)
            };
            println!("❌ [IMREADY] {}", error_msg);
            return Err(error_msg);
        }
    };
    
    let status = response.status();
    log_api_response(&status, "IMREADY");
    
    match status.as_u16() {
        200 => {
            println!("✅ [IMREADY] ImReady successful");
            Ok(true)
        }
        401 | 403 => {
            let error_msg = "AUTH_EXPIRED".to_string();
            println!("❌ [IMREADY] Authentication expired");
            Err(error_msg)
        }
        _ => {
            let error_msg = format!("API error: {}", status);
            println!("❌ [IMREADY] {}", error_msg);
            Err(error_msg)
        }
    }
}

// End activity
#[tauri::command]
pub async fn end_activity(
    app: AppHandle,
    activity_id: i32,
    project_id: Option<i32>,
    task_id: Option<i32>,
    notes: Option<String>,
    duration_seconds: u64,
) -> Result<bool, String> {
    println!("🏁 [ACTIVITY] Ending activity ID: {}", activity_id);

    // Get authentication token
    let token = get_auth_token(&app).await?;

    // Get error logs count for this activity
    let imready_error_logs_count = get_error_logs_count(&app, activity_id).await?;
    println!("📊 [ACTIVITY] ImReady error count: {}", imready_error_logs_count);

    // Format duration
    let duration = super::utils::format_duration(duration_seconds);

    // Create request body
    let request_body = ActivityEndRequest {
        activity_id,
        project_id,
        task_id,
        notes,
        duration,
        imready_error_logs_count,
    };

    println!("📋 [ACTIVITY] End request data: {:?}", request_body);

    // Create HTTP client
    let client = create_http_client();

    let url = "http://127.0.0.1:8000/api/activity/end/";
    log_api_request("PUT", url, "ACTIVITY");

    // Make API request
    let response = match client
        .put(url)
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .header("Content-Type", "application/json")
        .json(&request_body)
        .timeout(std::time::Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = if e.is_timeout() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else if e.is_connect() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else {
                format!("Network error: {}", e)
            };
            println!("❌ [ACTIVITY] {}", error_msg);
            return Err(error_msg);
        }
    };

    let status = response.status();
    log_api_response(&status, "ACTIVITY");

    match status.as_u16() {
        200 | 201 => {
            println!("✅ [ACTIVITY] Activity ended successfully ({})", status);

            // Clean up error logs for this activity
            match delete_error_logs(app.clone(), activity_id).await {
                Ok(_) => println!("🗑️ [ACTIVITY] Activity-specific error logs cleaned up"),
                Err(e) => println!("⚠️ [ACTIVITY] Failed to clean up activity error logs: {}", e),
            }

            // Clear all error logs since API call was successful
            match clear_all_error_logs(app).await {
                Ok(_) => println!("🗑️ [ACTIVITY] All error logs cleared after successful end_activity"),
                Err(e) => println!("⚠️ [ACTIVITY] Failed to clear all error logs: {}", e),
            }

            Ok(true)
        }
        401 | 403 => {
            let error_msg = "AUTH_EXPIRED".to_string();
            println!("❌ [ACTIVITY] Authentication expired");
            Err(error_msg)
        }
        _ => {
            let error_msg = format!("API error: {}", status);
            println!("❌ [ACTIVITY] {}", error_msg);
            Err(error_msg)
        }
    }
}

// Update activity
#[tauri::command]
pub async fn update_activity(
    app: AppHandle,
    activity_id: i32,
    project_id: Option<i32>,
    task_id: Option<i32>,
    notes: Option<String>,
) -> Result<bool, String> {
    println!("🔄 [ACTIVITY] Updating activity ID: {}", activity_id);

    // Get authentication token
    let token = get_auth_token(&app).await?;

    // Get error logs count (or 0 if already cleared)
    let imready_error_logs_count = match get_all_error_logs_count(&app).await {
        Ok(count) => count,
        Err(_) => 0,
    };

    println!("📊 [ACTIVITY] ImReady error logs count: {}", imready_error_logs_count);

    // Create request body
    let request_body = ActivityUpdateRequest {
        activity_id,
        project_id,
        task_id,
        notes,
        imready_error_logs_count: Some(imready_error_logs_count),
    };

    println!("📋 [ACTIVITY] Update request data: {:?}", request_body);

    // Create HTTP client
    let client = create_http_client();

    let url = "http://127.0.0.1:8000/api/activity/update/";
    log_api_request("PUT", url, "ACTIVITY");

    // Make API request
    let response = match client
        .put(url)
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .header("Content-Type", "application/json")
        .json(&request_body)
        .timeout(std::time::Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = if e.is_timeout() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else if e.is_connect() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else {
                format!("Network error: {}", e)
            };
            println!("❌ [ACTIVITY] {}", error_msg);
            return Err(error_msg);
        }
    };

    let status = response.status();
    log_api_response(&status, "ACTIVITY");

    match status.as_u16() {
        200 | 201 => {
            println!("✅ [ACTIVITY] Activity updated successfully ({})", status);

            // Clear all error logs since API call was successful
            match clear_all_error_logs(app).await {
                Ok(_) => println!("🗑️ [ACTIVITY] All error logs cleared after successful update_activity"),
                Err(e) => println!("⚠️ [ACTIVITY] Failed to clear all error logs: {}", e),
            }

            Ok(true)
        }
        401 | 403 => {
            let error_msg = "AUTH_EXPIRED".to_string();
            println!("❌ [ACTIVITY] Authentication expired");
            Err(error_msg)
        }
        _ => {
            let error_msg = format!("API error: {}", status);
            println!("❌ [ACTIVITY] {}", error_msg);
            Err(error_msg)
        }
    }
}
