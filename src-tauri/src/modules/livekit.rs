use serde::{Deserialize, Serialize};
use tauri::command;
use reqwest;

#[derive(Debug, Serialize, Deserialize)]
pub struct LiveKitTokenRequest {
    pub event: String,
    pub live_session_id: String,
    pub subject: String,
    pub user_id: u32,
    pub username: String,
    pub can_publish: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LiveKitTokenResponse {
    pub token: String,
}

/// Request LiveKit token via Tauri command
#[command]
pub async fn get_livekit_token(
    name: String,
    user_id: String,
    room_name: String,
    event_type: String,
) -> Result<String, String> {
    println!("🔧 [LIVEKIT_TAURI] Making token request with params:");
    println!("  - name: {}", name);
    println!("  - user_id: {}", user_id);
    println!("  - room_name: {}", room_name);
    println!("  - event_type: {}", event_type);

    // Generate random user ID for LiveKit using system time
    let random_user_id = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_millis() as u32 % 10000;
    let username = format!("{}{}", name, random_user_id);

    // Prepare request payload
    let payload = LiveKitTokenRequest {
        event: event_type,
        live_session_id: room_name,
        subject: "null".to_string(),
        user_id: random_user_id,
        username,
        can_publish: "true".to_string(),
    };

    println!("🌐 [LIVEKIT_TAURI] Sending request to token server...");

    // Make HTTP request to LiveKit token server
    let client = reqwest::Client::new();
    let response = client
        .post("https://habibmeet.nwhco.ir/test/sfu/token")
        .header("Content-Type", "application/json")
        .json(&payload)
        .send()
        .await
        .map_err(|e| {
            let error_msg = format!("Failed to send request: {}", e);
            println!("❌ [LIVEKIT_TAURI] {}", error_msg);
            error_msg
        })?;

    if !response.status().is_success() {
        let error_msg = format!("HTTP error! status: {}", response.status());
        println!("❌ [LIVEKIT_TAURI] {}", error_msg);
        return Err(error_msg);
    }

    let token_response: LiveKitTokenResponse = response
        .json()
        .await
        .map_err(|e| {
            let error_msg = format!("Failed to parse response: {}", e);
            println!("❌ [LIVEKIT_TAURI] {}", error_msg);
            error_msg
        })?;

    println!("✅ [LIVEKIT_TAURI] Token received successfully");
    println!("🔧 [LIVEKIT_TAURI] Token length: {}", token_response.token.len());

    Ok(token_response.token)
}

/// Test function to verify LiveKit token request
#[command]
pub async fn test_livekit_connection() -> Result<String, String> {
    println!("🧪 [LIVEKIT_TAURI] Testing LiveKit connection...");
    
    let test_result = get_livekit_token(
        "test_user".to_string(),
        "<EMAIL>".to_string(),
        "test_room".to_string(),
        "screen_view".to_string(),
    ).await;

    match test_result {
        Ok(token) => {
            println!("✅ [LIVEKIT_TAURI] Test successful - token received");
            Ok(format!("Test successful - token length: {}", token.len()))
        }
        Err(e) => {
            println!("❌ [LIVEKIT_TAURI] Test failed: {}", e);
            Err(format!("Test failed: {}", e))
        }
    }
}
