use std::time::Duration;
use tauri::AppHandle;
use tauri_plugin_store::StoreBuilder;

use super::types::{UserProfile, UserProfileResponse, ServerUserProfileResponse};
use super::utils::{create_http_client, get_auth_token, log_api_request, log_api_response};
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize, Debug)]
struct UpdateScreenActiveRequest {
    screan_active: bool,
}

#[derive(Serialize, Deserialize, Debug)]
struct UpdateScreenActiveResponse {
    success: bool,
    message: String,
}

// Get user profile from API
#[tauri::command]
pub async fn get_user_profile(app: AppHandle) -> Result<UserProfile, String> {
    println!("👤 [PROFILE] Fetching user profile...");
    
    // Get authentication token
    let token = get_auth_token(&app).await?;
    
    // Create HTTP client
    let client = create_http_client();
    
    // Log API request
    log_api_request("GET", "http://127.0.0.1:8000/api/v2/employees/profile/", "PROFILE");
    
    // Make API call to profile endpoint
    match client
        .get("http://127.0.0.1:8000/api/v2/employees/profile/")
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .timeout(Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => {
            let status = response.status();
            log_api_response(&status, "PROFILE");
            
            match status.as_u16() {
                200 => {
                    println!("✅ [PROFILE] Profile fetch successful, parsing response...");
                    
                    let response_text = match response.text().await {
                        Ok(text) => {
                            println!("📄 [PROFILE] Raw response body: {}", text);
                            text
                        }
                        Err(e) => {
                            let error_msg = format!("Failed to read response body: {}", e);
                            println!("❌ [PROFILE] {}", error_msg);
                            return Err(error_msg);
                        }
                    };
                    
                    // Try to parse as UserProfileResponse first
                    match serde_json::from_str::<UserProfileResponse>(&response_text) {
                        Ok(profile_response) => {
                            println!("✅ [PROFILE] Successfully parsed as UserProfileResponse");
                            let profile = profile_response.data;
                            
                            // Store profile in Tauri store
                            store_user_profile(&app, &profile).await?;
                            
                            Ok(profile)
                        }
                        Err(e) => {
                            println!("⚠️ [PROFILE] Failed to parse as UserProfileResponse: {}", e);
                            println!("🔄 [PROFILE] Trying to parse as ServerUserProfileResponse (with typo)...");
                            
                            // Try to parse as ServerUserProfileResponse (with typo)
                            match serde_json::from_str::<ServerUserProfileResponse>(&response_text) {
                                Ok(server_response) => {
                                    println!("✅ [PROFILE] Successfully parsed as ServerUserProfileResponse");
                                    
                                    // Convert ServerUserProfile to UserProfile
                                    let profile = UserProfile {
                                        full_name: server_response.data.full_name,
                                        email: server_response.data.email,
                                        avatar: server_response.data.avatar,
                                        position: server_response.data.position,
                                        company: server_response.data.company,
                                        is_admin: server_response.data.is_admin,
                                        screen_active: server_response.data.screan_active, // Fix typo
                                        incomplete_activity: server_response.data.incomplete_activity,
                                    };

                                    println!("📋 [PROFILE] Converted profile data: {:?}", profile);

                                    // Log incomplete activity if exists
                                    if let Some(ref incomplete) = profile.incomplete_activity {
                                        println!("⚠️ [PROFILE] Found incomplete activity ID: {} with duration: {}",
                                                incomplete.activity_id, incomplete.duration);
                                    } else {
                                        println!("✅ [PROFILE] No incomplete activity found");
                                    }
                                    
                                    // Store profile in Tauri store
                                    store_user_profile(&app, &profile).await?;
                                    
                                    Ok(profile)
                                }
                                Err(e2) => {
                                    let error_msg = format!("Failed to parse profile response: {} | {}", e, e2);
                                    println!("❌ [PROFILE] {}", error_msg);
                                    Err(error_msg)
                                }
                            }
                        }
                    }
                }
                401 | 403 => {
                    let error_msg = "AUTH_EXPIRED".to_string();
                    println!("❌ [PROFILE] Authentication expired");
                    Err(error_msg)
                }
                404 => {
                    let error_msg = "Profile not found".to_string();
                    println!("❌ [PROFILE] {}", error_msg);
                    Err(error_msg)
                }
                _ => {
                    let error_msg = format!("API error: {}", status);
                    println!("❌ [PROFILE] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        Err(e) => {
            let error_msg = if e.is_timeout() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else if e.is_connect() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else {
                format!("Network error: {}", e)
            };
            println!("❌ [PROFILE] {}", error_msg);
            Err(error_msg)
        }
    }
}

// Store user profile in Tauri store
async fn store_user_profile(app: &AppHandle, profile: &UserProfile) -> Result<(), String> {
    let store = match StoreBuilder::new(app, "user.json").build() {
        Ok(store) => store,
        Err(e) => return Err(format!("Failed to build store: {}", e)),
    };

    match serde_json::to_value(profile) {
        Ok(profile_value) => {
            store.set("profile", profile_value);
            match store.save() {
                Ok(_) => {
                    println!("💾 [PROFILE] Profile stored successfully");
                    Ok(())
                }
                Err(e) => {
                    let error_msg = format!("Failed to save profile store: {}", e);
                    println!("❌ [PROFILE] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        Err(e) => {
            let error_msg = format!("Failed to serialize profile: {}", e);
            println!("❌ [PROFILE] {}", error_msg);
            Err(error_msg)
        }
    }
}

// Get cached user profile from store
#[tauri::command]
pub async fn get_cached_user_profile(app: AppHandle) -> Result<Option<UserProfile>, String> {
    println!("💾 [PROFILE] Getting cached user profile...");
    
    let store = match StoreBuilder::new(&app, "user.json").build() {
        Ok(store) => store,
        Err(e) => {
            println!("⚠️ [PROFILE] Failed to build store: {}", e);
            return Ok(None);
        }
    };

    match store.get("profile") {
        Some(profile_value) => {
            match serde_json::from_value::<UserProfile>(profile_value.clone()) {
                Ok(profile) => {
                    println!("✅ [PROFILE] Cached profile found: {:?}", profile.email);
                    println!("🖼️ [PROFILE] Cached profile avatar: {:?}", profile.avatar);
                    println!("📋 [PROFILE] Full cached profile: {:?}", profile);
                    Ok(Some(profile))
                }
                Err(e) => {
                    println!("⚠️ [PROFILE] Failed to parse cached profile: {}", e);
                    Ok(None)
                }
            }
        }
        None => {
            println!("ℹ️ [PROFILE] No cached profile found");
            Ok(None)
        }
    }
}

// Clear all user-related stores (for 403 errors and logout)
#[tauri::command]
pub async fn clear_user_stores(app: AppHandle) -> Result<(), String> {
    println!("🗑️ [STORES] Clearing all user-related stores...");

    // Clear auth store
    match StoreBuilder::new(&app, "auth.json").build() {
        Ok(store) => {
            let deleted = store.delete("token");
            if deleted {
                let _ = store.save();
                println!("✅ [STORES] Auth store cleared");
            }
        }
        Err(e) => {
            println!("⚠️ [STORES] Failed to clear auth store: {:?}", e);
        }
    }

    // Clear profile store
    match StoreBuilder::new(&app, "user.json").build() {
        Ok(store) => {
            let deleted = store.delete("profile");
            if deleted {
                let _ = store.save();
                println!("✅ [STORES] Profile store cleared");
            }
        }
        Err(e) => {
            println!("⚠️ [STORES] Failed to clear profile store: {:?}", e);
        }
    }

    Ok(())
}

// Create a default offline profile when server is not available
#[tauri::command]
pub async fn create_offline_profile(app: AppHandle, email: String) -> Result<UserProfile, String> {
    println!("📴 [PROFILE] Creating offline profile for: {}", email);

    let offline_profile = UserProfile {
        full_name: "User".to_string(),
        email: email.clone(),
        avatar: None,
        position: None,
        company: None,
        is_admin: false,
        screen_active: false,
        incomplete_activity: None,
    };

    // Store offline profile
    match StoreBuilder::new(&app, "user.json").build() {
        Ok(store) => {
            match serde_json::to_value(&offline_profile) {
                Ok(profile_value) => {
                    store.set("profile", profile_value);
                    match store.save() {
                        Ok(_) => {
                            println!("💾 [PROFILE] Offline profile stored");
                            Ok(offline_profile)
                        }
                        Err(e) => {
                            let error_msg = format!("Failed to save offline profile: {}", e);
                            println!("❌ [PROFILE] {}", error_msg);
                            Err(error_msg)
                        }
                    }
                }
                Err(e) => {
                    let error_msg = format!("Failed to serialize offline profile: {}", e);
                    println!("❌ [PROFILE] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        Err(e) => {
            let error_msg = format!("Failed to build store: {}", e);
            println!("❌ [PROFILE] {}", error_msg);
            Err(error_msg)
        }
    }
}

// Update screen active status
#[tauri::command]
pub async fn update_screen_active_status(app: AppHandle, screen_active: bool) -> Result<UserProfile, String> {
    println!("👁️ [PROFILE] Updating screen active status to: {}", screen_active);

    // Get authentication token
    let token = get_auth_token(&app).await?;

    // Create HTTP client
    let client = create_http_client();

    // Create request body
    let request_body = UpdateScreenActiveRequest {
        screan_active: screen_active,
    };

    let url = "http://127.0.0.1:8000/api/update-screan-active/";
    log_api_request("POST", url, "SCREEN_ACTIVE");

    // Make API request
    let response = match client
        .post(url)
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .header("Content-Type", "application/json")
        .json(&request_body)
        .timeout(Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = if e.is_timeout() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else if e.is_connect() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else {
                format!("Network error: {}", e)
            };
            println!("❌ [SCREEN_ACTIVE] {}", error_msg);
            return Err(error_msg);
        }
    };

    let status = response.status();
    log_api_response(&status, "SCREEN_ACTIVE");

    match status.as_u16() {
        200 | 201 => {
            println!("✅ [SCREEN_ACTIVE] Screen active status updated successfully");

            // Parse response to confirm success
            match response.json::<UpdateScreenActiveResponse>().await {
                Ok(api_response) => {
                    println!("📄 [SCREEN_ACTIVE] API Response: {}", api_response.message);

                    // Get updated profile from cache and update screen_active
                    match get_cached_user_profile(app.clone()).await? {
                        Some(mut profile) => {
                            profile.screen_active = screen_active;

                            // Store updated profile back to cache
                            store_user_profile(&app, &profile).await?;

                            println!("✅ [SCREEN_ACTIVE] Profile updated in cache with new screen_active: {}", screen_active);
                            Ok(profile)
                        }
                        None => {
                            println!("⚠️ [SCREEN_ACTIVE] No cached profile found, fetching fresh profile");
                            // Fallback: fetch fresh profile
                            get_user_profile(app).await
                        }
                    }
                }
                Err(e) => {
                    println!("⚠️ [SCREEN_ACTIVE] Failed to parse response, but API call succeeded: {}", e);
                    // Still update local cache since API call was successful
                    match get_cached_user_profile(app.clone()).await? {
                        Some(mut profile) => {
                            profile.screen_active = screen_active;
                            store_user_profile(&app, &profile).await?;
                            Ok(profile)
                        }
                        None => get_user_profile(app).await
                    }
                }
            }
        }
        401 | 403 => {
            let error_msg = "AUTH_EXPIRED".to_string();
            println!("❌ [SCREEN_ACTIVE] Authentication expired");
            Err(error_msg)
        }
        400 => {
            let error_msg = "Invalid data provided".to_string();
            println!("❌ [SCREEN_ACTIVE] {}", error_msg);
            Err(error_msg)
        }
        _ => {
            let error_msg = format!("API error: {}", status);
            println!("❌ [SCREEN_ACTIVE] {}", error_msg);
            Err(error_msg)
        }
    }
}
