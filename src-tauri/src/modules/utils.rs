use chrono::{Local, Utc};
use reqwest::Client;
use std::time::Duration;
use tauri::AppHandle;
use tauri_plugin_store::StoreBuilder;

use super::types::{SystemInfo, VERSION};

// Create HTTP client with timeout
pub fn create_http_client() -> Client {
    Client::builder()
        .timeout(Duration::from_secs(30))
        .build()
        .expect("Failed to create HTTP client")
}

// Get authentication token from store
pub async fn get_auth_token(app: &AppHandle) -> Result<String, String> {
    let store = match StoreBuilder::new(app, "auth.json").build() {
        Ok(store) => store,
        Err(e) => return Err(format!("Failed to build store: {}", e)),
    };

    match store.get("token") {
        Some(token_value) => {
            if let Some(token) = token_value.as_str() {
                Ok(token.to_string())
            } else {
                Err("AUTH_EXPIRED".to_string())
            }
        }
        None => Err("AUTH_EXPIRED".to_string()),
    }
}

// Format duration from seconds to HH:MM:SS
pub fn format_duration(seconds: u64) -> String {
    let hours = seconds / 3600;
    let minutes = (seconds % 3600) / 60;
    let secs = seconds % 60;
    format!("{:02}:{:02}:{:02}", hours, minutes, secs)
}

// Get current timestamp as string
pub fn get_current_timestamp() -> String {
    Local::now().format("%Y-%m-%d %H:%M:%S").to_string()
}

// Get current UTC timestamp as string
#[allow(dead_code)]
pub fn get_current_utc_timestamp() -> String {
    Utc::now().format("%Y-%m-%dT%H:%M:%S").to_string()
}

// Get system information
pub async fn get_system_info() -> SystemInfo {
    let platform = std::env::consts::OS.to_string();
    let device_name = hostname::get()
        .unwrap_or_else(|_| "unknown".into())
        .to_string_lossy()
        .to_string();
    
    // Get more detailed OS info
    let device_os = if cfg!(target_os = "linux") {
        // Try to get Linux distribution info
        match std::fs::read_to_string("/etc/os-release") {
            Ok(content) => {
                for line in content.lines() {
                    if line.starts_with("PRETTY_NAME=") {
                        let name = line.trim_start_matches("PRETTY_NAME=")
                            .trim_matches('"');
                        return SystemInfo {
                            platform: platform.clone(),
                            device_name: device_name.clone(),
                            device_os: name.to_string(),
                            local_time: get_current_timestamp(),
                        };
                    }
                }
                platform.clone()
            }
            Err(_) => platform.clone(),
        }
    } else {
        platform.clone()
    };

    SystemInfo {
        platform,
        device_name,
        device_os,
        local_time: get_current_timestamp(),
    }
}

// Add version header to request
#[allow(dead_code)]
pub fn add_version_header(client: &Client, url: &str) -> reqwest::RequestBuilder {
    client
        .get(url)
        .header("version", VERSION.to_string())
}

// Add version header to POST request
#[allow(dead_code)]
pub fn add_version_header_post(client: &Client, url: &str) -> reqwest::RequestBuilder {
    client
        .post(url)
        .header("version", VERSION.to_string())
}

// Add version header to PUT request
#[allow(dead_code)]
pub fn add_version_header_put(client: &Client, url: &str) -> reqwest::RequestBuilder {
    client
        .put(url)
        .header("version", VERSION.to_string())
}

// Log API request
pub fn log_api_request(method: &str, url: &str, context: &str) {
    println!("📡 [{}] Sending {} request to: {}", context, method, url);
    println!("🔢 [{}] API Version: {}", context, VERSION);
}

// Log API response
pub fn log_api_response(status: &reqwest::StatusCode, context: &str) {
    println!("📨 [{}] Received response with status: {}", context, status);
}
