
use tauri::AppHandle;
use serde_json;

use super::types::{Project, ProjectsResponse, Task, TasksResponse, Employee, EmployeeDetail, ActivityStat};
use super::utils::{create_http_client, get_auth_token, log_api_request, log_api_response};
use serde::{Deserialize, Serialize};

// Screen share validation request
#[derive(Debug, Serialize)]
pub struct ScreenShareValidationRequest {
    employee_target_id: i32,
    share_type: String,
}

// Screen share validation response
#[derive(Debug, Serialize, Deserialize)]
pub struct ScreenShareValidationResponse {
    pub success: bool,
    pub message: Option<String>,
}

// LiveKit Token Request
#[derive(Debug, Serialize)]
pub struct LiveKitTokenRequest {
    pub name: String,
    pub user_id: String,
    pub room_name: String,
    pub event_type: String,
}

// LiveKit Token Response
#[derive(Debug, Serialize, Deserialize)]
pub struct LiveKitTokenResponse {
    pub token: String,
    pub data: Option<serde_json::Value>,
}

// Fetch projects from API
#[tauri::command]
pub async fn fetch_projects(app: AppHandle) -> Result<Vec<Project>, String> {
    println!("📋 [PROJECTS] Fetching projects from API...");
    
    // Get authentication token
    let token = get_auth_token(&app).await?;
    
    // Create HTTP client
    let client = create_http_client();
    
    let url = "http://127.0.0.1:8000/api/projects/";
    log_api_request("GET", url, "PROJECTS");
    
    // Make API request
    let response = match client
        .get(url)
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .header("Content-Type", "application/json")
        .timeout(std::time::Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = if e.is_timeout() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else if e.is_connect() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else {
                format!("Network error: {}", e)
            };
            println!("❌ [PROJECTS] {}", error_msg);
            return Err(error_msg);
        }
    };
    
    let status = response.status();
    log_api_response(&status, "PROJECTS");
    
    match status.as_u16() {
        200 => {
            println!("✅ [PROJECTS] Projects fetch successful, parsing response...");
            
            let response_text = match response.text().await {
                Ok(text) => {
                    println!("📄 [PROJECTS] Raw response body: {}", text);
                    text
                }
                Err(e) => {
                    let error_msg = format!("Failed to read response body: {}", e);
                    println!("❌ [PROJECTS] {}", error_msg);
                    return Err(error_msg);
                }
            };
            
            match serde_json::from_str::<ProjectsResponse>(&response_text) {
                Ok(projects_response) => {
                    println!("✅ [PROJECTS] Successfully parsed {} projects", projects_response.data.len());
                    Ok(projects_response.data)
                }
                Err(e) => {
                    let error_msg = format!("Failed to parse projects response: {}", e);
                    println!("❌ [PROJECTS] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        401 | 403 => {
            let error_msg = "AUTH_EXPIRED".to_string();
            println!("❌ [PROJECTS] Authentication expired");
            Err(error_msg)
        }
        404 => {
            let error_msg = "Projects not found".to_string();
            println!("❌ [PROJECTS] {}", error_msg);
            Err(error_msg)
        }
        _ => {
            let error_msg = format!("API error: {}", status);
            println!("❌ [PROJECTS] {}", error_msg);
            Err(error_msg)
        }
    }
}

// Fetch project tasks from API
#[tauri::command]
pub async fn fetch_project_tasks(app: AppHandle, project_id: i32) -> Result<Vec<Task>, String> {
    println!("📋 [TASKS] Fetching tasks for project ID: {}", project_id);
    
    // Get authentication token
    let token = get_auth_token(&app).await?;
    
    // Create HTTP client
    let client = create_http_client();
    
    let url = format!("http://127.0.0.1:8000/api/projects/{}/targets/", project_id);
    log_api_request("GET", &url, "TASKS");
    
    let response = match client
        .get(&url)
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .header("Content-Type", "application/json")
        .timeout(std::time::Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = if e.is_timeout() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else if e.is_connect() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else {
                format!("Network error: {}", e)
            };
            println!("❌ [TASKS] {}", error_msg);
            return Err(error_msg);
        }
    };
    
    let status = response.status();
    log_api_response(&status, "TASKS");
    
    match status.as_u16() {
        200 => {
            println!("✅ [TASKS] Tasks fetch successful, parsing response...");
            
            let response_text = match response.text().await {
                Ok(text) => {
                    println!("📄 [TASKS] Raw response body: {}", text);
                    text
                }
                Err(e) => {
                    let error_msg = format!("Failed to read response body: {}", e);
                    println!("❌ [TASKS] {}", error_msg);
                    return Err(error_msg);
                }
            };
            
            match serde_json::from_str::<TasksResponse>(&response_text) {
                Ok(tasks_response) => {
                    println!("✅ [TASKS] Successfully parsed {} tasks", tasks_response.data.len());
                    Ok(tasks_response.data)
                }
                Err(e) => {
                    let error_msg = format!("Failed to parse tasks response: {}", e);
                    println!("❌ [TASKS] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        401 | 403 => {
            let error_msg = "AUTH_EXPIRED".to_string();
            println!("❌ [TASKS] Authentication expired");
            Err(error_msg)
        }
        404 => {
            let error_msg = "Tasks not found for this project".to_string();
            println!("❌ [TASKS] {}", error_msg);
            Err(error_msg)
        }
        _ => {
            let error_msg = format!("API error: {}", status);
            println!("❌ [TASKS] {}", error_msg);
            Err(error_msg)
        }
    }
}

// Fetch employees list from API
#[tauri::command]
pub async fn fetch_employees_list(app: AppHandle) -> Result<Vec<Employee>, String> {
    println!("👥 [EMPLOYEES] Fetching employees list from API...");

    // Get authentication token
    let token = get_auth_token(&app).await?;

    // Create HTTP client
    let client = create_http_client();

    let url = "http://127.0.0.1:8000/api/employees/list/";
    log_api_request("GET", url, "EMPLOYEES");

    // Make API request
    let response = match client
        .get(url)
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .header("Content-Type", "application/json")
        .timeout(std::time::Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = format!("Network error: {}", e);
            println!("❌ [EMPLOYEES] {}", error_msg);
            return Err(error_msg);
        }
    };

    let status = response.status();
    log_api_response(&status, "EMPLOYEES");

    match status {
        reqwest::StatusCode::OK => {
            // Parse response as direct array of employees
            match response.json::<Vec<Employee>>().await {
                Ok(employees) => {
                    println!("✅ [EMPLOYEES] Successfully fetched {} employees", employees.len());
                    Ok(employees)
                }
                Err(e) => {
                    let error_msg = format!("Failed to parse employees response: {}", e);
                    println!("❌ [EMPLOYEES] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        reqwest::StatusCode::UNAUTHORIZED => {
            let error_msg = "AUTH_EXPIRED".to_string();
            println!("❌ [EMPLOYEES] {}", error_msg);
            Err(error_msg)
        }
        reqwest::StatusCode::NOT_FOUND => {
            let error_msg = "EMPLOYEES_NOT_FOUND".to_string();
            println!("❌ [EMPLOYEES] {}", error_msg);
            Err(error_msg)
        }
        _ => {
            let error_msg = format!("API error: {}", status);
            println!("❌ [EMPLOYEES] {}", error_msg);
            Err(error_msg)
        }
    }
}

// Fetch employee detail from API
#[tauri::command]
pub async fn fetch_employee_detail(app: AppHandle, employee_id: i32) -> Result<EmployeeDetail, String> {
    println!("👤 [EMPLOYEE_DETAIL] Fetching employee detail for ID: {}", employee_id);

    // Get authentication token
    let token = get_auth_token(&app).await?;

    // Create HTTP client
    let client = create_http_client();

    let url = format!("http://127.0.0.1:8000/api/employees/{}/", employee_id);
    log_api_request("GET", &url, "EMPLOYEE_DETAIL");

    // Make API request
    let response = match client
        .get(&url)
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .header("Content-Type", "application/json")
        .timeout(std::time::Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = format!("Network error: {}", e);
            println!("❌ [EMPLOYEE_DETAIL] {}", error_msg);
            return Err(error_msg);
        }
    };

    let status = response.status();
    log_api_response(&status, "EMPLOYEE_DETAIL");

    match status {
        reqwest::StatusCode::OK => {
            // Get raw response text first for debugging
            match response.text().await {
                Ok(response_text) => {
                    println!("📄 [EMPLOYEE_DETAIL] Raw response body: {}", response_text);

                    // Try to parse the JSON
                    match serde_json::from_str::<EmployeeDetail>(&response_text) {
                        Ok(employee_detail) => {
                            println!("✅ [EMPLOYEE_DETAIL] Successfully fetched employee detail for: {}", employee_detail.full_name);
                            Ok(employee_detail)
                        }
                        Err(e) => {
                            let error_msg = format!("Failed to parse employee detail response: {}", e);
                            println!("❌ [EMPLOYEE_DETAIL] {}", error_msg);
                            println!("❌ [EMPLOYEE_DETAIL] Raw response was: {}", response_text);
                            Err(error_msg)
                        }
                    }
                }
                Err(e) => {
                    let error_msg = format!("Failed to read response text: {}", e);
                    println!("❌ [EMPLOYEE_DETAIL] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        reqwest::StatusCode::UNAUTHORIZED => {
            let error_msg = "AUTH_EXPIRED".to_string();
            println!("❌ [EMPLOYEE_DETAIL] {}", error_msg);
            Err(error_msg)
        }
        reqwest::StatusCode::NOT_FOUND => {
            let error_msg = "EMPLOYEE_NOT_FOUND".to_string();
            println!("❌ [EMPLOYEE_DETAIL] {}", error_msg);
            Err(error_msg)
        }
        _ => {
            let error_msg = format!("API error: {}", status);
            println!("❌ [EMPLOYEE_DETAIL] {}", error_msg);
            Err(error_msg)
        }
    }
}

// Fetch activity stats from API
#[tauri::command]
pub async fn fetch_activity_stats(app: AppHandle) -> Result<Vec<ActivityStat>, String> {
    println!("📊 [ACTIVITY_STATS] Fetching activity stats from API...");

    // Get authentication token
    let token = get_auth_token(&app).await?;

    // Create HTTP client
    let client = create_http_client();

    let url = "http://127.0.0.1:8000/api/activity/stats/";
    log_api_request("GET", url, "ACTIVITY_STATS");

    // Make API request
    let response = match client
        .get(url)
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .header("Content-Type", "application/json")
        .timeout(std::time::Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = format!("Network error: {}", e);
            println!("❌ [ACTIVITY_STATS] {}", error_msg);
            return Err(error_msg);
        }
    };

    let status = response.status();
    log_api_response(&status, "ACTIVITY_STATS");

    match status {
        reqwest::StatusCode::OK => {
            // Get raw response text first for debugging
            match response.text().await {
                Ok(response_text) => {
                    println!("📄 [ACTIVITY_STATS] Raw response body: {}", response_text);

                    // Try to parse the JSON
                    match serde_json::from_str::<Vec<ActivityStat>>(&response_text) {
                        Ok(activity_stats) => {
                            println!("✅ [ACTIVITY_STATS] Successfully fetched {} activity stats", activity_stats.len());
                            Ok(activity_stats)
                        }
                        Err(e) => {
                            let error_msg = format!("Failed to parse activity stats response: {}", e);
                            println!("❌ [ACTIVITY_STATS] {}", error_msg);
                            println!("❌ [ACTIVITY_STATS] Raw response was: {}", response_text);
                            Err(error_msg)
                        }
                    }
                }
                Err(e) => {
                    let error_msg = format!("Failed to read response text: {}", e);
                    println!("❌ [ACTIVITY_STATS] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        reqwest::StatusCode::UNAUTHORIZED => {
            let error_msg = "AUTH_EXPIRED".to_string();
            println!("❌ [ACTIVITY_STATS] {}", error_msg);
            Err(error_msg)
        }
        reqwest::StatusCode::NOT_FOUND => {
            let error_msg = "STATS_NOT_FOUND".to_string();
            println!("❌ [ACTIVITY_STATS] {}", error_msg);
            Err(error_msg)
        }
        _ => {
            let error_msg = format!("API error: {}", status);
            println!("❌ [ACTIVITY_STATS] {}", error_msg);
            Err(error_msg)
        }
    }
}

// Store activity stats in Tauri store
#[tauri::command]
pub async fn store_activity_stats(app: AppHandle, stats: Vec<ActivityStat>) -> Result<(), String> {
    println!("💾 [ACTIVITY_STATS] Storing {} activity stats in Tauri store...", stats.len());

    use tauri_plugin_store::StoreBuilder;

    let store = match StoreBuilder::new(&app, ".settings.dat").build() {
        Ok(store) => store,
        Err(e) => {
            let error_msg = format!("Failed to build store: {}", e);
            println!("❌ [ACTIVITY_STATS] {}", error_msg);
            return Err(error_msg);
        }
    };

    // Set the value (store.set returns () not Result)
    store.set("activity_stats", serde_json::to_value(&stats).map_err(|e| e.to_string())?);

    // Save the store
    match store.save() {
        Ok(_) => {
            println!("✅ [ACTIVITY_STATS] Activity stats stored successfully in Tauri store");
            Ok(())
        }
        Err(e) => {
            let error_msg = format!("Failed to save store: {}", e);
            println!("❌ [ACTIVITY_STATS] {}", error_msg);
            Err(error_msg)
        }
    }
}

// Get cached activity stats from Tauri store
#[tauri::command]
pub async fn get_cached_activity_stats(app: AppHandle) -> Result<Option<Vec<ActivityStat>>, String> {
    println!("💾 [ACTIVITY_STATS] Getting cached activity stats from Tauri store...");

    use tauri_plugin_store::StoreBuilder;

    let store = match StoreBuilder::new(&app, ".settings.dat").build() {
        Ok(store) => store,
        Err(e) => {
            let error_msg = format!("Failed to build store: {}", e);
            println!("❌ [ACTIVITY_STATS] {}", error_msg);
            return Err(error_msg);
        }
    };

    match store.get("activity_stats") {
        Some(value) => {
            match serde_json::from_value::<Vec<ActivityStat>>(value.clone()) {
                Ok(stats) => {
                    println!("✅ [ACTIVITY_STATS] Found {} cached activity stats", stats.len());
                    Ok(Some(stats))
                }
                Err(e) => {
                    println!("❌ [ACTIVITY_STATS] Failed to parse cached activity stats: {}", e);
                    Ok(None)
                }
            }
        }
        None => {
            println!("ℹ️ [ACTIVITY_STATS] No cached activity stats found");
            Ok(None)
        }
    }
}

// Validate screen share permissions
#[tauri::command]
pub async fn validate_screen_share(app: AppHandle, employee_id: i32) -> Result<ScreenShareValidationResponse, String> {
    println!("🔍 [SCREEN_SHARE_VALIDATION] Validating screen share for employee ID: {}", employee_id);

    // Get authentication token
    let token = get_auth_token(&app).await?;

    // Create HTTP client
    let client = create_http_client();

    // Prepare request body
    let request_body = ScreenShareValidationRequest {
        employee_target_id: employee_id,
        share_type: "subscriber".to_string(),
    };

    let url = "http://127.0.0.1:8000/api/v2/employees/livekit-screen-share/";
    log_api_request("POST", url, "SCREEN_SHARE_VALIDATION");

    // Make API request
    let response = match client
        .post(url)
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .header("Content-Type", "application/json")
        .json(&request_body)
        .timeout(std::time::Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = if e.is_timeout() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else if e.is_connect() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else {
                format!("Network error: {}", e)
            };
            println!("❌ [SCREEN_SHARE_VALIDATION] {}", error_msg);
            return Err(error_msg);
        }
    };

    let status = response.status();
    log_api_response(&status, "SCREEN_SHARE_VALIDATION");

    match status {
        reqwest::StatusCode::OK => {
            // Get raw response text first for debugging
            match response.text().await {
                Ok(response_text) => {
                    println!("📄 [SCREEN_SHARE_VALIDATION] Raw response body: {}", response_text);

                    // Try to parse the JSON
                    match serde_json::from_str::<ScreenShareValidationResponse>(&response_text) {
                        Ok(validation_response) => {
                            println!("✅ [SCREEN_SHARE_VALIDATION] Successfully parsed validation response");
                            Ok(validation_response)
                        }
                        Err(e) => {
                            let error_msg = format!("Failed to parse screen share validation response: {}", e);
                            println!("❌ [SCREEN_SHARE_VALIDATION] {}", error_msg);
                            println!("❌ [SCREEN_SHARE_VALIDATION] Raw response was: {}", response_text);
                            Err(error_msg)
                        }
                    }
                }
                Err(e) => {
                    let error_msg = format!("Failed to read response text: {}", e);
                    println!("❌ [SCREEN_SHARE_VALIDATION] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        reqwest::StatusCode::UNAUTHORIZED => {
            let error_msg = "AUTH_EXPIRED".to_string();
            println!("❌ [SCREEN_SHARE_VALIDATION] {}", error_msg);
            Err(error_msg)
        }
        reqwest::StatusCode::FORBIDDEN => {
            let error_msg = "SCREEN_SHARE_FORBIDDEN".to_string();
            println!("❌ [SCREEN_SHARE_VALIDATION] {}", error_msg);
            Err(error_msg)
        }
        reqwest::StatusCode::NOT_FOUND => {
            let error_msg = "EMPLOYEE_NOT_FOUND".to_string();
            println!("❌ [SCREEN_SHARE_VALIDATION] {}", error_msg);
            Err(error_msg)
        }
        _ => {
            let error_msg = format!("API error: {}", status);
            println!("❌ [SCREEN_SHARE_VALIDATION] {}", error_msg);
            Err(error_msg)
        }
    }
}

// Request LiveKit token for screen viewing
#[tauri::command]
pub async fn request_livekit_token(
    _app: AppHandle,
    name: String,
    user_id: String,
    room_name: String,
    event_type: String,
) -> Result<LiveKitTokenResponse, String> {
    println!("🎥 [LIVEKIT] Requesting token for user: {} in room: {}", name, room_name);

    // Create HTTP client
    let client = create_http_client();

    // Prepare request body
    let request_body = LiveKitTokenRequest {
        name,
        user_id,
        room_name,
        event_type,
    };

    let url = "https://qa.habibapp.com/meet/livekit-apis/public-token";
    log_api_request("POST", url, "LIVEKIT_TOKEN");

    // Make API request
    let response = match client
        .post(url)
        .header("Content-Type", "application/json")
        .json(&request_body)
        .timeout(std::time::Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = if e.is_timeout() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else if e.is_connect() {
                "NETWORK_CONNECTION_FAILED".to_string()
            } else {
                format!("Network error: {}", e)
            };
            println!("❌ [LIVEKIT_TOKEN] {}", error_msg);
            return Err(error_msg);
        }
    };

    let status = response.status();
    log_api_response(&status, "LIVEKIT_TOKEN");

    match status {
        reqwest::StatusCode::OK => {
            match response.json::<LiveKitTokenResponse>().await {
                Ok(token_response) => {
                    println!("✅ [LIVEKIT_TOKEN] Token received successfully");
                    Ok(token_response)
                }
                Err(e) => {
                    let error_msg = format!("Failed to parse token response: {}", e);
                    println!("❌ [LIVEKIT_TOKEN] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        reqwest::StatusCode::BAD_REQUEST => {
            let error_msg = "INVALID_REQUEST".to_string();
            println!("❌ [LIVEKIT_TOKEN] {}", error_msg);
            Err(error_msg)
        }
        reqwest::StatusCode::UNAUTHORIZED => {
            let error_msg = "AUTH_EXPIRED".to_string();
            println!("❌ [LIVEKIT_TOKEN] {}", error_msg);
            Err(error_msg)
        }
        reqwest::StatusCode::FORBIDDEN => {
            let error_msg = "LIVEKIT_FORBIDDEN".to_string();
            println!("❌ [LIVEKIT_TOKEN] {}", error_msg);
            Err(error_msg)
        }
        _ => {
            let error_msg = format!("API error: {}", status);
            println!("❌ [LIVEKIT_TOKEN] {}", error_msg);
            Err(error_msg)
        }
    }
}
