{"$schema": "https://schema.tauri.app/config/2", "productName": "TeamBy Desktop App", "version": "0.1.0", "identifier": "com.tauri.dev", "build": {"frontendDist": "../dist", "devUrl": "http://127.0.0.1:3000", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"label": "main", "title": "TeamBy Desktop", "width": 1200, "height": 800, "minWidth": 1200, "minHeight": 800, "resizable": true, "fullscreen": false, "decorations": false, "center": true, "transparent": true, "visible": false}, {"label": "screen-view", "title": "TeamBy - Screen View", "width": 900, "height": 600, "minWidth": 800, "minHeight": 500, "resizable": true, "fullscreen": false, "decorations": false, "center": true, "transparent": true, "visible": false, "url": "screen-view.html"}, {"label": "splashscreen", "title": "TeamBy Desktop - Loading", "width": 350, "height": 350, "resizable": false, "fullscreen": false, "decorations": false, "center": true, "transparent": true, "alwaysOnTop": true, "skipTaskbar": true, "url": "splash.html"}, {"label": "call", "title": "TeamBy - Call", "width": 400, "height": 350, "minWidth": 350, "minHeight": 300, "resizable": false, "fullscreen": false, "decorations": false, "center": true, "transparent": true, "alwaysOnTop": true, "visible": false, "url": "call-tauri.html"}], "security": {"csp": "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss:; connect-src 'self' ws: wss: http: https: *; media-src 'self' blob: data: mediastream:; img-src 'self' data: blob: https: *; style-src 'self' 'unsafe-inline' *; font-src 'self' data: *; script-src 'self' 'unsafe-inline' 'unsafe-eval' *; frame-src 'self' *; worker-src 'self' blob:; object-src 'none';"}, "withGlobalTauri": false}, "plugins": {}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}